# 项目结构原理图

本文档旨在阐明"电商市场动态监测系统"的整体架构、文件组织方式和关键代码功能，为后续的开发和维护工作提供清晰的指引。

## 一、 系统高层架构

系统采用前后端分离的经典架构模式。

-   **前端 (Frontend)**：基于 Vue.js 3 和 Vite 构建的单页面应用 (SPA)，负责用户界面的展示和交互。
-   **后端 (Backend)**：基于 PHP Laravel 框架的 API 服务，负责业务逻辑处理、数据存储和队列任务。
-   **数据库 (Database)**：使用 MySQL (根据 `config/database.php` 推断) 存储所有业务数据。
-   **Web 服务器 (Web Server)**：如 Nginx 或 Apache，负责处理 HTTP 请求，并将请求转发给前端或后端服务。

### 交互流程图

```mermaid
graph TD
    A[用户] --> B{浏览器};
    B --> C[前端 (Vue.js on Vite)];
    C -->|API 请求 (HTTP/S)| D[后端 (Laravel API)];
    D --> E[数据库 (MySQL)];
    D --> F[队列系统 (Redis/Database)];
    F --> G[后台任务处理器];
    G --> D;
    E --> D;
    D --> C;
    C --> B;
```

**流程说明:**

1.  用户通过浏览器访问网站。
2.  前端应用 (`frontend`) 被加载，负责渲染页面。
3.  当用户进行操作时（如查看数据、创建监控任务），前端会向后端 (`backend`) 发送 API 请求。
4.  后端 Laravel 应用处理请求，可能涉及：
    *   与数据库进行数据读写。
    *   将耗时任务（如数据采集）推送到队列中。
5.  后台的队列处理器会异步执行这些任务。
6.  后端将处理结果返回给前端。
7.  前端根据返回的数据更新视图，展示给用户。

---

## 二、 目录结构详解

### 1. 根目录 (`/`)

根目录主要包含前后端项目的分离文件夹，以及一些项目级的配置文件和文档。

-   `frontend/`: 存放所有前端相关代码。
-   `backend/`: 存放所有后端相关代码。
-   `database/`: 包含一些旧的数据库迁移文件，但主要的迁移文件在 `backend/database/migrations`。
-   `app/`: 似乎是一个遗留的或错误的目录结构，核心应用逻辑在 `backend/app` 中。
-   `img/`: 存放项目相关图片资源，如文档配图。
-   `开发蓝图.txt`: 项目的开发计划和高级目标。
-   `接口说明.txt`: 前后端交互的 API 接口详细定义。
-   `电商市场动态监测系统需求文档.md`: 项目的原始需求文档。
-   `*.php`: 一些用于测试、检查状态的临时脚本。

### 2. 后端目录 (`/backend`)

这是一个标准的 Laravel 项目结构。

-   `app/`: 项目的核心代码目录。
    -   `Console/Commands/`: Artisan 命令，用于执行计划任务（如分发监控、消费队列）。
    -   `Http/Controllers/Api/`: API 控制器，处理所有来自前端的 HTTP 请求。
    -   `Http/Middleware/`: 中间件，用于请求过滤（如权限、角色检查）。
    -   `Jobs/`: 队列任务，处理异步、耗时的操作（如检查警报、处理数据源）。
    -   `Models/`: Eloquent 模型，代表数据库中的表，并定义了表之间的关系。
    -   `Policies/`: 授权策略，定义了用户操作模型时的权限。
    -   `Providers/`: 服务提供者，用于注册和启动框架服务。
    -   `Services/`: 业务服务类，封装了具体的业务逻辑（如警报服务、数据采集服务）。
-   `bootstrap/`: 框架启动和自动加载文件。
-   `config/`: 存放所有配置文件（数据库、缓存、队列、应用信息等）。
-   `database/`: 数据库相关文件。
    -   `factories/`: 模型工厂，用于生成测试数据。
    -   `migrations/`: 数据库迁移文件，用于版本化管理数据库结构。
    -   `seeders/`: 数据填充文件，用于向数据库中插入初始数据。
-   `public/`: Web 服务器的根目录，是应用的入口 `index.php` 所在。
-   `routes/`: 路由定义。
    -   `api.php`: 定义了所有 API 路由，是前后端交互的入口点。
    -   `console.php`: 定义了所有基于闭包的 Artisan 命令。
-   `storage/`: 存放框架生成的文件、日志、缓存等。
-   `tests/`: 存放自动化测试用例。
-   `composer.json`: PHP 依赖管理文件。
-   `artisan`: Artisan 命令行工具的入口。

### 3. 前端目录 (`/frontend`)

这是一个基于 Vite 的 Vue 3 + TypeScript 项目。

-   `src/`: 项目源代码目录。
    -   `api/`: 封装了所有与后端交互的 API 请求。
    -   `assets/`: 存放静态资源，如图片、CSS。
    -   `components/`: 存放可复用的 Vue 组件。
    -   `router/`: Vue Router 的配置，定义了页面的路由。
    -   `stores/`: Pinia 状态管理，用于存储全局状态（如用户信息）。
    -   `views/`: 存放页面级 Vue 组件，每个文件通常对应一个路由。
    -   `App.vue`: 应用的根组件。
    -   `main.ts`: 应用的入口文件，负责创建 Vue 实例并挂载。
-   `public/`: 存放不会被 Vite 处理的静态文件，会直接复制到构建输出的根目录。
-   `index.html`: 单页面应用的 HTML 入口文件。
-   `vite.config.ts`: Vite 的配置文件。
-   `package.json`: Node.js 依赖管理和项目脚本定义文件。
-   `tsconfig.json`: TypeScript 的配置文件。

---

## 三、 关键交互与数据流

1.  **用户认证**:
    -   前端 `Login.vue` 页面提交用户名密码。
    -   调用 `src/api/` 中的登录接口，请求后端 `/api/login` (`backend/routes/api.php`)。
    -   后端 `AuthController.php` 处理登录，验证成功后返回 Token。
    -   前端将 Token 存储在 `pinia` (`stores/auth.ts`) 和 `localStorage` 中，并在后续请求的 Header 中携带。

2.  **数据监控任务 (MonitoringTask)**:
    -   用户在前端 `ChannelPriceTaskManagement.vue` 或类似界面创建任务。
    -   前端调用 `monitoringTask.js` 中的 API 函数。
    -   请求后端 `MonitoringTaskController.php` 的 `store` 方法。
    -   后端 `DataCollectionService.php` 被调用，可能会创建一个 `ProcessDataSourceTask` Job 并推入队列。
    -   后端的计划任务 `DispatchMonitoringTasks` (`app/Console/Commands/`) 定期运行，将到期的任务分发到队列中。
    -   Job 处理器从队列中获取任务，执行数据抓取、分析和存储。

3.  **数据显示**:
    -   前端页面（如 `ChannelPriceDashboard.vue`）加载时，会调用 `productData.js` 或 `analytics.js` 中的 API 函数。
    -   请求后端 `ProductDataController.php` 或 `AnalyticsController.php`。
    -   控制器从数据库中查询数据，可能会经过 `AnalyticsService` 处理，然后返回给前端。
    -   前端使用获取到的数据渲染图表和表格。

---

## 总结

该项目是一个功能明确、技术栈现代化的前后端分离应用。其核心挑战在于：

-   **数据采集的稳定性与及时性**：依赖于后端的队列和计划任务系统。
-   **业务逻辑的复杂性**：后端通过 Service 层来封装复杂的业务逻辑，保持 Controller 的简洁。
-   **前后端接口的协同**：需要严格遵守 `接口说明.txt` 中的约定。

理解以上结构和流程，将有助于在修改现有功能或添加新功能时，快速定位到正确的代码位置，并预见修改可能带来的影响。 



---

## 四、全量代码文件与功能说明（修正版）

### 1. 后端 backend/app 主要代码文件

#### 1.1 Models（模型）
- backend/app/Models/ProductData.php  —— 商品数据主表模型，存储采集到的商品信息
- backend/app/Models/ProductDataHistory.php —— 商品数据历史记录模型，用于追踪商品信息变更
- backend/app/Models/ProductSku.php  —— 商品SKU模型，管理商品的库存、价格等SKU级别数据
- backend/app/Models/ProductSkuHistory.php —— 商品SKU历史记录模型，用于追踪SKU信息变更
- backend/app/Models/AuditLog.php  —— 审计日志模型，记录系统操作日志
- backend/app/Models/MonitoringTask.php  —— 监控任务模型，定义每个监控任务的参数和状态
- backend/app/Models/DataSource.php  —— 数据源模型，管理各类采集数据的来源
- backend/app/Models/AlertRule.php  —— 预警规则模型，定义触发预警的条件
- backend/app/Models/Alert.php  —— 预警事件模型，记录已触发的预警
- backend/app/Models/TaskGroup.php  —— 任务分组模型，便于批量管理监控任务
- backend/app/Models/FieldMapping.php  —— 字段映射模型，支持不同数据源字段的标准化
- backend/app/Models/User.php  —— 用户模型，管理系统用户信息
- backend/app/Models/Permission.php  —— 权限模型，定义系统操作权限
- backend/app/Models/Role.php  —— 角色模型，定义用户角色

#### 1.2 Services（服务类）
- backend/app/Services/DataCollectionService.php  —— 核心数据采集与处理逻辑
- backend/app/Services/AuditLogService.php  —— 系统操作日志记录服务
- backend/app/Services/AlertService.php  —— 预警触发与通知逻辑
- backend/app/Services/AnalyticsService.php  —— 数据分析与报表生成
- backend/app/Services/NotificationService.php  —— 消息通知服务
- backend/app/Services/QueueProducerService.php  —— 队列任务生产者，负责将任务推送到队列

#### 1.3 Controllers/Api（API控制器）
- backend/app/Http/Controllers/Api/DataSourceController.php  —— 数据源管理API
- backend/app/Http/Controllers/Api/ProductDataController.php  —— 商品数据API
- backend/app/Http/Controllers/Api/ProductDataHistoryController.php —— 商品数据历史API
- backend/app/Http/Controllers/Api/AnalyticsController.php  —— 数据分析API
- backend/app/Http/Controllers/Api/MonitoringTaskController.php  —— 监控任务API
- backend/app/Http/Controllers/Api/AuthController.php  —— 用户认证API
- backend/app/Http/Controllers/Api/AlertsController.php  —— 预警事件API
- backend/app/Http/Controllers/Api/AlertRuleController.php  —— 预警规则API
- backend/app/Http/Controllers/Api/TaskGroupController.php  —— 任务分组API
- backend/app/Http/Controllers/Api/UserController.php  —— 用户管理API
- backend/app/Http/Controllers/Api/PermissionController.php  —— 权限管理API
- backend/app/Http/Controllers/Api/RoleController.php  —— 角色管理API
- backend/app/Http/Controllers/AuditLogController.php —— 审计日志API（非Api子目录，部分功能）

#### 1.4 Jobs（队列任务）
- backend/app/Jobs/ProcessDataSourceTask.php  —— 采集数据的异步任务
- backend/app/Jobs/ProcessAlertChecking.php  —— 预警检测的异步任务
- backend/app/Jobs/SendAlertNotification.php  —— 发送预警通知的异步任务

#### 1.5 Console/Commands（自定义命令）
- backend/app/Console/Commands/ManageMonitoringTasks.php  —— 监控任务管理命令
- backend/app/Console/Commands/DispatchMonitoringTasks.php  —— 分发监控任务到队列
- backend/app/Console/Commands/TestQueueSystem.php  —— 队列系统测试命令
- backend/app/Console/Commands/ConsumeQueueTasks.php  —— 消费队列任务命令
- backend/app/Console/Commands/ListDataSources.php —— 列出现有数据源的命令
- backend/app/Console/Commands/TestHistorySave.php —— 测试历史数据保存功能的命令

#### 1.6 其它重要目录和文件
- backend/app/Http/Requests/StoreMonitoringTaskRequest.php、UpdateMonitoringTaskRequest.php —— 监控任务相关表单验证
- backend/app/Http/Middleware/CheckPermission.php、CheckRole.php、CorsMiddleware.php —— 权限、角色与跨域中间件
- backend/app/Policies/MonitoringTaskPolicy.php —— 监控任务授权策略
- backend/app/Providers/AppServiceProvider.php、EventServiceProvider.php —— 服务提供者
- backend/app/Observers/AuditObserver.php —— 审计日志观察者
- backend/app/Listeners/AuthEventListener.php —— 认证相关事件监听
- backend/app/Notifications/AlertTriggeredNotification.php —— 预警通知

### 2. 数据库迁移 backend/database/migrations 主要文件

- backend/database/migrations/2025_06_22_141542_create_roles_table.php  —— 创建角色表
- backend/database/migrations/2025_06_22_141543_create_permissions_table.php  —— 创建权限表
- backend/database/migrations/2025_06_22_141544_create_role_permissions_table.php  —— 创建角色权限关联表
- backend/database/migrations/2025_06_22_141545_create_user_roles_table.php  —— 创建用户角色关联表
- backend/database/migrations/2025_06_22_141550_create_data_sources_table.php  —— 创建数据源表
- backend/database/migrations/2025_06_22_141552_create_task_groups_table.php  —— 创建任务分组表
- backend/database/migrations/2025_06_22_141554_create_monitoring_tasks_table.php  —— 创建监控任务表
- backend/database/migrations/2025_06_22_141555_create_alert_rules_table.php  —— 创建预警规则表
- backend/database/migrations/2025_06_22_141600_create_alerts_table.php  —— 创建预警事件表
- backend/database/migrations/2025_06_22_141601_create_audit_logs_table.php  —— 创建审计日志表
- backend/database/migrations/2025_06_22_141602_create_field_mappings_table.php  —— 创建字段映射表
- backend/database/migrations/2025_06_22_143804_create_personal_access_tokens_table.php  —— 创建个人访问令牌表
- backend/database/migrations/2025_06_22_154233_create_product_data_table.php  —— 创建商品数据表
- backend/database/migrations/2025_06_22_161000_create_notifications_table.php  —— 创建通知表
- backend/database/migrations/2025_06_23_015541_add_field_mapping_to_data_sources_table.php  —— 为数据源表添加字段映射
- backend/database/migrations/2025_06_23_021324_refactor_data_sources_table.php  —— 重构数据源表结构
- backend/database/migrations/2025_06_23_021509_refactor_data_sources_table.php  —— 重构数据源表结构
- backend/database/migrations/2025_06_24_004248_modify_product_skus_table_for_api_alignment.php  —— 调整商品SKU表以对齐API需求
- backend/database/migrations/2025_06_24_005708_rename_quantity_to_stock_in_product_skus_table.php  —— 将商品SKU表的quantity字段重命名为stock
- backend/database/migrations/2025_06_24_010706_revert_field_names_in_product_skus_table.php  —— 恢复商品SKU表字段名
- backend/database/migrations/2025_06_24_021334_remove_raw_data_from_product_data_table.php  —— 移除商品数据表的原始数据字段
- backend/database/migrations/2025_06_24_050435_add_promotions_to_product_data_table.php  —— 为商品数据表添加促销字段
- backend/database/migrations/2025_06_24_084214_drop_promotions_column_from_product_data_table.php  —— 删除商品数据表的促销字段
- backend/database/migrations/2025_06_24_104206_modify_promotion_column_in_product_data_table.php  —— 修改商品数据表促销字段
- backend/database/migrations/2025_06_25_000001_create_product_skus_table.php  —— 创建商品SKU表
- backend/database/migrations/2025_06_25_000002_add_standard_fields_to_product_data_table.php  —— 为商品数据表添加标准字段
- backend/database/migrations/2025_06_25_005847_add_deviation_rates_to_product_skus_history_table.php  —— 为SKU历史表添加偏差率字段
- backend/database/migrations/2025_06_25_041254_add_schedule_fields_to_monitoring_tasks_table.php  —— 为监控任务表添加调度字段
- backend/database/migrations/2025_06_25_042952_rename_target_products_in_monitoring_tasks_table.php  —— 重命名监控任务表目标产品字段
- backend/database/migrations/2025_06_25_055207_update_alert_rules_table_remove_unused_fields.php  —— 移除预警规则表无用字段
- backend/database/migrations/2025_06_25_061917_modify_notification_method_to_json_in_alert_rules_table.php  —— 修改预警规则表通知方式为JSON
- backend/database/migrations/2025_06_25_062440_modify_rule_type_to_json_in_alert_rules_table.php  —— 修改预警规则类型为JSON
- backend/database/migrations/2025_12_30_add_official_guide_price_to_product_data.php  —— 商品数据表添加官方指导价
- backend/database/migrations/2025_12_30_add_official_guide_price_to_product_skus.php  —— 商品SKU表添加官方指导价
- backend/database/migrations/2025_12_30_create_product_data_history_table.php —— 创建商品数据历史表
- backend/database/migrations/2025_12_30_create_product_skus_history_table.php —— 创建商品SKU历史表
- backend/database/migrations/2025_12_31_add_alert_rules_to_monitoring_tasks.php —— 监控任务表添加预警规则字段
- backend/database/migrations/performance/2025_06_22_172415_modify_product_data_table_for_task_relation_and_price.php  —— 优化商品数据表以支持任务关联和价格字段
- backend/database/migrations/performance/2025_06_22_172643_add_performance_indexes.php  —— 添加性能相关索引

### 3. 前端 frontend/src 主要代码文件

#### 3.1 views（页面组件）
- frontend/src/views/ChannelPriceDashboard.vue  —— 渠道价格监控大盘页面
- frontend/src/views/DataSourceManagement.vue  —— 数据源管理页面
- frontend/src/views/CreateDataSource.vue  —— 新建数据源页面
- frontend/src/views/AuditLogs.vue  —— 审计日志查看页面
- frontend/src/views/ChannelPriceTaskManagement.vue  —— 渠道价格监控任务管理页面
- frontend/src/views/TaskManagement.vue  —— 通用任务管理页面
- frontend/src/views/Home.vue  —— 首页
- frontend/src/views/TaskGroupManagement.vue  —— 任务分组管理页面
- frontend/src/views/ChannelPriceAlerts.vue  —— 渠道价格预警页面
- frontend/src/views/CompetitorTaskManagement.vue  —— 竞品监控任务管理页面
- frontend/src/views/SimilarLiveSearch.vue  —— 相似商品实时搜索页面
- frontend/src/views/SimilarMonitoring.vue  —— 相似商品监控页面
- frontend/src/views/SystemSettings.vue  —— 系统设置页面
- frontend/src/views/CompetitorAlerts.vue  —— 竞品预警页面
- frontend/src/views/ProfileMessages.vue  —— 个人消息页面
- frontend/src/views/SimilarDashboard.vue  —— 相似商品监控大盘页面
- frontend/src/views/ProductHistoryDetail.vue —— 商品历史详情页面
- frontend/src/views/UserManagement.vue  —— 用户管理页面
- frontend/src/views/ProfilePassword.vue  —— 个人密码修改页面
- frontend/src/views/RoleManagement.vue  —— 角色管理页面
- frontend/src/views/Login.vue  —— 登录页面
- frontend/src/views/AlertCenter.vue  —— 预警中心页面
- frontend/src/views/CompetitorDashboard.vue  —— 竞品监控大盘页面
- frontend/src/views/About.vue  —— 关于页面
- frontend/src/views/NotFound.vue —— 404页面
- frontend/src/views/Register.vue —— 注册页面

#### 3.2 api（API 封装）
- frontend/src/api/analytics.js  —— 数据分析相关API封装
- frontend/src/api/monitoringTask.js  —— 监控任务相关API封装
- frontend/src/api/dataSource.js  —— 数据源相关API封装
- frontend/src/api/productData.js  —— 商品数据相关API封装
- frontend/src/api/alerts.js  —— 预警相关API封装
- frontend/src/api/apiClient.js  —— 通用API请求封装

#### 3.3 components（可复用组件）
- frontend/src/components/AlertRulesManagement.vue  —— 预警规则管理组件
- frontend/src/components/AppLayout.vue  —— 应用主布局组件
- frontend/src/components/AlertHistory.vue  —— 预警历史展示组件
- frontend/src/components/HelloWorld.vue  —— 示例组件
- frontend/src/components/Captcha.vue —— 验证码组件

#### 3.4 stores（状态管理）
- frontend/src/stores/auth.ts —— 用户认证与权限状态
- frontend/src/stores/counter.ts —— 示例计数器状态

#### 3.5 router（路由配置）
- frontend/src/router/index.ts —— Vue Router主路由配置

#### 3.6 其它重要文件
- frontend/src/App.vue —— 根组件
- frontend/src/main.ts —— 应用入口
- frontend/src/styles/global.css —— 全局样式
- frontend/src/assets/vue.svg —— Vue Logo静态资源
