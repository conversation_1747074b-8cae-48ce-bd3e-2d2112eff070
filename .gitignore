# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log

# Dependency directories
node_modules/

# Environment variables
.env

# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS specific
.DS_Store

# Backend (PHP/Laravel/ThinkPHP)
/backend/vendor/
/backend/.env
/backend/.env.backup
/backend/.env.production
/backend/public/hot
/backend/public/storage
/backend/storage/*.key
/backend/storage/logs/*.log
/backend/storage/framework/cache/data/*
/backend/storage/framework/sessions/*
/backend/storage/framework/testing/*
/backend/storage/framework/views/*
/backend/bootstrap/cache/*.php
/backend/.phpunit.result.cache
/backend/Homestead.json
/backend/Homestead.yaml

# Frontend (Vue.js/Node.js)
/frontend/node_modules/
/frontend/dist/
/frontend/.env.local
/frontend/.env.development.local
/frontend/.env.test.local
/frontend/.env.production.local
/frontend/coverage/
/frontend/.nyc_output
/frontend/.cache
/frontend/.parcel-cache

# Task files
# tasks.json
# tasks/