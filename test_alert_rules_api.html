<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>预警规则API测试</title>
    <style>
        body { font-family: 'Microsoft YaHei', sans-serif; margin: 20px; }
        .container { max-width: 1200px; margin: 0 auto; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .test-section h3 { margin-top: 0; color: #333; }
        button { padding: 8px 16px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        button.danger { background: #dc3545; }
        button.danger:hover { background: #c82333; }
        .result { margin: 10px 0; padding: 10px; background: #f8f9fa; border-left: 4px solid #007bff; white-space: pre-wrap; }
        .error { border-left-color: #dc3545; background: #f8d7da; }
        .success { border-left-color: #28a745; background: #d4edda; }
        input, select, textarea { padding: 8px; margin: 5px; border: 1px solid #ddd; border-radius: 3px; }
        .form-group { margin: 10px 0; }
        label { display: inline-block; width: 120px; font-weight: bold; }
        .rule-item { padding: 10px; margin: 5px 0; border: 1px solid #eee; border-radius: 3px; background: #f9f9f9; }
        .rule-actions { margin-top: 10px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>预警规则API测试</h1>
        
        <!-- 登录区域 -->
        <div class="test-section">
            <h3>1. 用户登录</h3>
            <div class="form-group">
                <label>用户名:</label>
                <input type="text" id="username" value="admin" />
            </div>
            <div class="form-group">
                <label>密码:</label>
                <input type="password" id="password" value="password" />
            </div>
            <button onclick="login()">登录</button>
            <div id="loginResult" class="result" style="display:none;"></div>
        </div>

        <!-- 获取选项 -->
        <div class="test-section">
            <h3>2. 获取预警规则选项</h3>
            <button onclick="getOptions()">获取选项</button>
            <div id="optionsResult" class="result" style="display:none;"></div>
        </div>

        <!-- 创建预警规则 -->
        <div class="test-section">
            <h3>3. 创建预警规则</h3>
            <div class="form-group">
                <label>规则名称:</label>
                <input type="text" id="ruleName" value="测试预警规则" />
            </div>
            <div class="form-group">
                <label>规则描述:</label>
                <textarea id="ruleDescription" rows="2">这是一个测试预警规则</textarea>
            </div>
            <div class="form-group">
                <label>规则类型:</label>
                <select id="ruleType" onchange="onRuleTypeChange()">
                    <option value="">请选择</option>
                    <option value="promotion_price_deviation">促销价偏离率</option>
                    <option value="channel_price_deviation">渠道价格偏离率</option>
                    <option value="listing_status_change">上下架状态</option>
                </select>
            </div>
            <div class="form-group" id="operatorGroup" style="display:none;">
                <label>操作符:</label>
                <select id="operator">
                    <option value="">请选择</option>
                    <option value=">">大于</option>
                    <option value="<">小于</option>
                    <option value=">=">大于等于</option>
                    <option value="<=">小于等于</option>
                    <option value="=">等于</option>
                    <option value="!=">不等于</option>
                </select>
            </div>
            <div class="form-group" id="thresholdGroup" style="display:none;">
                <label>阈值:</label>
                <input type="number" id="threshold" min="0" max="100" step="0.01" />
            </div>
            <div class="form-group" id="statusInfo" style="display:none; color: #666;">
                上下架状态预警会监控商品的state字段，当商品下架（state=0）时触发预警。
            </div>
            <div class="form-group">
                <label>严重级别:</label>
                <select id="severity">
                    <option value="low">低</option>
                    <option value="medium" selected>中</option>
                    <option value="high">高</option>
                    <option value="critical">严重</option>
                </select>
            </div>
            <div class="form-group">
                <label>优先级:</label>
                <select id="priority">
                    <option value="low">低</option>
                    <option value="normal" selected>普通</option>
                    <option value="high">高</option>
                    <option value="urgent">紧急</option>
                </select>
            </div>
            <div class="form-group">
                <label>通知方式:</label>
                <select id="notificationMethod">
                    <option value="email" selected>邮件通知</option>
                    <option value="system">站内信</option>
                </select>
            </div>
            <button onclick="createRule()">创建规则</button>
            <div id="createResult" class="result" style="display:none;"></div>
        </div>

        <!-- 获取预警规则列表 -->
        <div class="test-section">
            <h3>4. 获取预警规则列表</h3>
            <button onclick="getRules()">获取规则列表</button>
            <div id="rulesResult" class="result" style="display:none;"></div>
            <div id="rulesList"></div>
        </div>

        <!-- 测试所有三种类型 -->
        <div class="test-section">
            <h3>5. 快速测试三种规则类型</h3>
            <button onclick="createTestRules()">创建测试规则</button>
            <div id="testRulesResult" class="result" style="display:none;"></div>
        </div>
    </div>

    <script>
        let authToken = '';
        const API_BASE = 'http://localhost:8000/api';

        function showResult(elementId, content, isError = false, isSuccess = false) {
            const element = document.getElementById(elementId);
            element.textContent = content;
            element.className = 'result ' + (isError ? 'error' : isSuccess ? 'success' : '');
            element.style.display = 'block';
        }

        async function apiRequest(endpoint, options = {}) {
            const url = `${API_BASE}${endpoint}`;
            const headers = {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                ...options.headers
            };

            if (authToken) {
                headers['Authorization'] = `Bearer ${authToken}`;
            }

            try {
                const response = await fetch(url, {
                    ...options,
                    headers
                });

                const data = await response.json();
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${data.message || '请求失败'}`);
                }

                return data;
            } catch (error) {
                console.error('API请求失败:', error);
                throw error;
            }
        }

        async function login() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            try {
                const data = await apiRequest('/auth/login', {
                    method: 'POST',
                    body: JSON.stringify({ username, password })
                });

                if (data.success && data.data.token) {
                    authToken = data.data.token;
                    showResult('loginResult', `登录成功！\n用户: ${data.data.user.name}\nToken: ${authToken.substring(0, 20)}...`, false, true);
                } else {
                    showResult('loginResult', `登录失败: ${data.message}`, true);
                }
            } catch (error) {
                showResult('loginResult', `登录失败: ${error.message}`, true);
            }
        }

        async function getOptions() {
            try {
                const data = await apiRequest('/alert-rules/options');
                showResult('optionsResult', `获取选项成功:\n${JSON.stringify(data.data, null, 2)}`, false, true);
            } catch (error) {
                showResult('optionsResult', `获取选项失败: ${error.message}`, true);
            }
        }

        function onRuleTypeChange() {
            const ruleType = document.getElementById('ruleType').value;
            const operatorGroup = document.getElementById('operatorGroup');
            const thresholdGroup = document.getElementById('thresholdGroup');
            const statusInfo = document.getElementById('statusInfo');

            if (ruleType === 'listing_status_change') {
                operatorGroup.style.display = 'none';
                thresholdGroup.style.display = 'none';
                statusInfo.style.display = 'block';
            } else if (ruleType) {
                operatorGroup.style.display = 'block';
                thresholdGroup.style.display = 'block';
                statusInfo.style.display = 'none';
                document.getElementById('operator').value = '>';
                document.getElementById('threshold').value = '10';
            } else {
                operatorGroup.style.display = 'none';
                thresholdGroup.style.display = 'none';
                statusInfo.style.display = 'none';
            }
        }

        async function createRule() {
            const ruleData = {
                name: document.getElementById('ruleName').value,
                description: document.getElementById('ruleDescription').value,
                rule_type: document.getElementById('ruleType').value,
                severity: document.getElementById('severity').value,
                priority: document.getElementById('priority').value,
                notification_method: document.getElementById('notificationMethod').value,
                status: true
            };

            if (ruleData.rule_type !== 'listing_status_change') {
                ruleData.operator = document.getElementById('operator').value;
                ruleData.threshold_values = {
                    value: parseFloat(document.getElementById('threshold').value)
                };
            }

            try {
                const data = await apiRequest('/alert-rules', {
                    method: 'POST',
                    body: JSON.stringify(ruleData)
                });

                showResult('createResult', `创建规则成功:\nID: ${data.data.id}\n名称: ${data.data.name}\n类型: ${data.data.rule_type}`, false, true);
            } catch (error) {
                showResult('createResult', `创建规则失败: ${error.message}`, true);
            }
        }

        async function getRules() {
            try {
                const data = await apiRequest('/alert-rules');
                showResult('rulesResult', `获取规则列表成功，共 ${data.data.total || data.data.length || 0} 条记录`, false, true);
                
                const rulesList = document.getElementById('rulesList');
                rulesList.innerHTML = '';

                const rules = data.data.data || data.data || [];
                rules.forEach(rule => {
                    const ruleDiv = document.createElement('div');
                    ruleDiv.className = 'rule-item';
                    
                    let conditionText = '';
                    if (rule.rule_type === 'listing_status_change') {
                        conditionText = '商品下架时预警';
                    } else {
                        const ruleTypeMap = {
                            'promotion_price_deviation': '促销价偏离率',
                            'channel_price_deviation': '渠道价格偏离率'
                        };
                        const operatorMap = {
                            '>': '大于', '<': '小于', '>=': '大于等于', 
                            '<=': '小于等于', '=': '等于', '!=': '不等于'
                        };
                        conditionText = `${ruleTypeMap[rule.rule_type]} ${operatorMap[rule.operator]} ${rule.threshold_values?.value || 0}%`;
                    }

                    ruleDiv.innerHTML = `
                        <strong>${rule.name}</strong> (ID: ${rule.id})
                        <br>类型: ${rule.rule_type}
                        <br>触发条件: ${conditionText}
                        <br>严重级别: ${rule.severity} | 通知方式: ${rule.notification_method}
                        <br>状态: ${rule.status ? '启用' : '禁用'}
                        <div class="rule-actions">
                            <button onclick="deleteRule(${rule.id})">删除</button>
                        </div>
                    `;
                    rulesList.appendChild(ruleDiv);
                });
            } catch (error) {
                showResult('rulesResult', `获取规则列表失败: ${error.message}`, true);
            }
        }

        async function deleteRule(id) {
            if (!confirm('确定要删除这个规则吗？')) return;

            try {
                await apiRequest(`/alert-rules/${id}`, { method: 'DELETE' });
                alert('删除成功');
                getRules(); // 刷新列表
            } catch (error) {
                alert(`删除失败: ${error.message}`);
            }
        }

        async function createTestRules() {
            const testRules = [
                {
                    name: '促销价偏离率测试',
                    description: '测试促销价偏离率预警功能',
                    rule_type: 'promotion_price_deviation',
                    operator: '>',
                    threshold_values: { value: 15 },
                    severity: 'high',
                    priority: 'normal',
                    notification_method: 'email',
                    status: true
                },
                {
                    name: '渠道价格偏离率测试',
                    description: '测试渠道价格偏离率预警功能',
                    rule_type: 'channel_price_deviation',
                    operator: '>=',
                    threshold_values: { value: 20 },
                    severity: 'medium',
                    priority: 'high',
                    notification_method: 'system',
                    status: true
                },
                {
                    name: '上下架状态测试',
                    description: '测试商品上下架状态预警功能',
                    rule_type: 'listing_status_change',
                    severity: 'critical',
                    priority: 'urgent',
                    notification_method: 'email',
                    status: true
                }
            ];

            let results = [];
            for (const rule of testRules) {
                try {
                    const data = await apiRequest('/alert-rules', {
                        method: 'POST',
                        body: JSON.stringify(rule)
                    });
                    results.push(`✓ ${rule.name}: 创建成功 (ID: ${data.data.id})`);
                } catch (error) {
                    results.push(`✗ ${rule.name}: 创建失败 - ${error.message}`);
                }
            }

            showResult('testRulesResult', results.join('\n'), false, true);
            setTimeout(() => getRules(), 1000); // 1秒后刷新列表
        }
    </script>
</body>
</html> 