<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎉 所有问题修复验证</title>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <style>
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            margin: 20px; 
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .test-section { 
            margin: 30px 0; 
            padding: 20px; 
            border: 2px solid #ddd; 
            border-radius: 8px; 
            background: #fafafa;
        }
        .success { 
            background-color: #f0f9ff; 
            border-color: #0369a1; 
            color: #0369a1;
        }
        .error { 
            background-color: #fef2f2; 
            border-color: #dc2626; 
            color: #dc2626;
        }
        .loading { 
            background-color: #fffbeb; 
            border-color: #d97706; 
            color: #d97706;
        }
        .pending {
            background-color: #f3f4f6;
            border-color: #6b7280;
            color: #6b7280;
        }
        h1 {
            color: #1f2937;
            text-align: center;
            margin-bottom: 40px;
            font-size: 2.5em;
        }
        h2 {
            color: #374151;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        button { 
            padding: 12px 20px; 
            margin: 8px; 
            border: none; 
            border-radius: 6px; 
            cursor: pointer; 
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .btn-primary { background-color: #3b82f6; color: white; }
        .btn-primary:hover { background-color: #2563eb; }
        .btn-success { background-color: #10b981; color: white; }
        .btn-success:hover { background-color: #059669; }
        .btn-warning { background-color: #f59e0b; color: white; }
        .btn-warning:hover { background-color: #d97706; }
        .btn-danger { background-color: #ef4444; color: white; }
        .btn-danger:hover { background-color: #dc2626; }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
            max-height: 300px;
            overflow-y: auto;
        }
        .status-icon {
            font-size: 1.5em;
            margin-right: 10px;
        }
        .progress-bar {
            background-color: #e5e7eb;
            border-radius: 4px;
            overflow: hidden;
            margin: 20px 0;
        }
        .progress-fill {
            height: 20px;
            background-color: #10b981;
            transition: width 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        .summary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 10px;
            margin-top: 30px;
            text-align: center;
        }
        .issue-card {
            background: white;
            border-left: 4px solid #10b981;
            padding: 15px;
            margin: 15px 0;
            border-radius: 0 6px 6px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .auto-test-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            font-size: 1.2em;
            padding: 15px 30px;
            margin: 20px auto;
            display: block;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎉 问题修复验证中心</h1>
        
        <div class="summary">
            <h3>修复进度</h3>
            <div class="progress-bar">
                <div class="progress-fill" id="overallProgress" style="width: 0%">0%</div>
            </div>
            <p id="progressText">点击"运行全部测试"开始验证</p>
        </div>

        <button class="auto-test-btn" onclick="runAllTests()">🚀 运行全部测试</button>

        <div class="test-section pending" id="test1">
            <h2><span class="status-icon">⏳</span>测试1: Priority验证修复</h2>
            <p><strong>问题</strong>: "The selected priority is invalid" - medium优先级不被支持</p>
            <p><strong>修复</strong>: 更新AlertRule模型支持medium优先级</p>
            <button class="btn-primary" onclick="testPriorityFix()">测试Priority修复</button>
            <div class="result" id="priority-result"></div>
        </div>

        <div class="test-section pending" id="test2">
            <h2><span class="status-icon">⏳</span>测试2: 预警规则统计API修复</h2>
            <p><strong>问题</strong>: /api/alert-rules/stats 返回404错误</p>
            <p><strong>修复</strong>: 修复路由顺序和添加is_read字段</p>
            <button class="btn-primary" onclick="testStatsAPI()">测试统计API</button>
            <div class="result" id="stats-result"></div>
        </div>

        <div class="test-section pending" id="test3">
            <h2><span class="status-icon">⏳</span>测试3: 监控任务预警规则加载</h2>
            <p><strong>问题</strong>: 监控任务页面预警规则加载不到数据</p>
            <p><strong>修复</strong>: 修复API响应处理和添加getRuleTypeLabel函数</p>
            <button class="btn-primary" onclick="testAlertRulesAPI()">测试预警规则列表</button>
            <div class="result" id="rules-result"></div>
        </div>

        <div class="test-section pending" id="test4">
            <h2><span class="status-icon">⏳</span>测试4: 通知方式配置</h2>
            <p><strong>问题</strong>: 前端显示短信选项但后端未配置</p>
            <p><strong>修复</strong>: 移除前端短信选项，只保留邮件和系统通知</p>
            <button class="btn-primary" onclick="testNotificationMethods()">测试通知方式</button>
            <div class="result" id="notification-result"></div>
        </div>

        <div class="test-section pending" id="test5">
            <h2><span class="status-icon">⏳</span>测试5: 创建预警规则完整流程</h2>
            <p><strong>测试</strong>: 端到端创建预警规则流程验证</p>
            <button class="btn-primary" onclick="testCreateRule()">测试创建预警规则</button>
            <div class="result" id="create-result"></div>
        </div>

        <div id="finalSummary" style="display: none;" class="summary">
            <h2>🎊 所有问题已修复！</h2>
            <div class="issue-card">
                <h4>✅ Priority验证错误</h4>
                <p>后端现在支持medium优先级，前端可以正常创建规则</p>
            </div>
            <div class="issue-card">
                <h4>✅ 预警规则统计API</h4>
                <p>路由配置正确，is_read字段已添加，API正常返回数据</p>
            </div>
            <div class="issue-card">
                <h4>✅ 监控任务预警规则加载</h4>
                <p>API响应处理正确，getRuleTypeLabel函数正常工作</p>
            </div>
            <div class="issue-card">
                <h4>✅ 通知方式配置</h4>
                <p>前端只显示已配置的通知方式（邮件和系统通知）</p>
            </div>
            <p style="margin-top: 20px; font-size: 1.2em;">
                🚀 现在可以正常使用所有功能了！
            </p>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000/api';
        const AUTH_TOKEN = '29|ISClMFwxeHDO7GgYYWFnL9eXUwPa4Yq6YQjRahONe314233f';

        axios.defaults.headers.common['Authorization'] = `Bearer ${AUTH_TOKEN}`;
        axios.defaults.headers.common['Accept'] = 'application/json';
        axios.defaults.headers.common['Content-Type'] = 'application/json';

        let completedTests = 0;
        const totalTests = 5;

        function updateProgress() {
            const percentage = (completedTests / totalTests) * 100;
            document.getElementById('overallProgress').style.width = percentage + '%';
            document.getElementById('overallProgress').textContent = Math.round(percentage) + '%';
            
            if (completedTests === totalTests) {
                document.getElementById('progressText').textContent = '🎉 所有测试已完成！';
                document.getElementById('finalSummary').style.display = 'block';
            } else {
                document.getElementById('progressText').textContent = `已完成 ${completedTests}/${totalTests} 项测试`;
            }
        }

        function setTestStatus(testId, status, message = '') {
            const testElement = document.getElementById(testId);
            testElement.className = `test-section ${status}`;
            
            const statusIcon = testElement.querySelector('.status-icon');
            switch (status) {
                case 'success':
                    statusIcon.textContent = '✅';
                    completedTests++;
                    break;
                case 'error':
                    statusIcon.textContent = '❌';
                    completedTests++;
                    break;
                case 'loading':
                    statusIcon.textContent = '⏳';
                    break;
            }
            
            if (message) {
                const resultDiv = testElement.querySelector('.result');
                resultDiv.innerHTML = message;
            }
            
            updateProgress();
        }

        async function testPriorityFix() {
            setTestStatus('test1', 'loading');
            
            try {
                // 测试获取选项，检查是否包含medium优先级
                const response = await axios.get(`${API_BASE}/alert-rules/options`);
                
                const priorities = response.data.data.priorities;
                const hasMedium = priorities.hasOwnProperty('medium');
                
                if (hasMedium) {
                    setTestStatus('test1', 'success', 
                        `✅ Priority修复成功！<br/>
                        支持的优先级: ${Object.keys(priorities).join(', ')}<br/>
                        medium优先级对应: ${priorities.medium}`
                    );
                } else {
                    setTestStatus('test1', 'error', 
                        `❌ Priority修复失败！<br/>
                        当前支持的优先级: ${Object.keys(priorities).join(', ')}<br/>
                        缺少medium优先级`
                    );
                }
            } catch (error) {
                setTestStatus('test1', 'error', 
                    `❌ 测试失败: ${error.response?.data?.message || error.message}`
                );
            }
        }

        async function testStatsAPI() {
            setTestStatus('test2', 'loading');
            
            try {
                const response = await axios.get(`${API_BASE}/alert-rules/stats`);
                
                if (response.data && typeof response.data.activeRules !== 'undefined') {
                    setTestStatus('test2', 'success', 
                        `✅ 统计API修复成功！<br/>
                        活跃规则: ${response.data.activeRules}<br/>
                        今日预警: ${response.data.todayAlerts}<br/>
                        未读预警: ${response.data.unreadAlerts}<br/>
                        总预警数: ${response.data.totalAlerts}`
                    );
                } else {
                    setTestStatus('test2', 'error', 
                        `❌ API响应格式不正确: ${JSON.stringify(response.data)}`
                    );
                }
            } catch (error) {
                setTestStatus('test2', 'error', 
                    `❌ 统计API测试失败: ${error.response?.data?.message || error.message}`
                );
            }
        }

        async function testAlertRulesAPI() {
            setTestStatus('test3', 'loading');
            
            try {
                const response = await axios.get(`${API_BASE}/alert-rules?status=1&per_page=100`);
                
                if (response.data.success && response.data.data && response.data.data.data) {
                    const rules = response.data.data.data;
                    const ruleTypes = rules.map(rule => rule.rule_type).flat();
                    
                    setTestStatus('test3', 'success', 
                        `✅ 预警规则列表API修复成功！<br/>
                        获取到 ${rules.length} 个活跃规则<br/>
                        规则类型: ${[...new Set(ruleTypes)].join(', ')}<br/>
                        API响应格式正确，包含分页信息`
                    );
                } else {
                    setTestStatus('test3', 'error', 
                        `❌ API响应格式不正确`
                    );
                }
            } catch (error) {
                setTestStatus('test3', 'error', 
                    `❌ 预警规则列表API测试失败: ${error.response?.data?.message || error.message}`
                );
            }
        }

        async function testNotificationMethods() {
            setTestStatus('test4', 'loading');
            
            try {
                const response = await axios.get(`${API_BASE}/alert-rules/options`);
                
                const notificationMethods = response.data.data.notification_methods;
                const hasSms = notificationMethods.hasOwnProperty('sms');
                const hasEmail = notificationMethods.hasOwnProperty('email');
                const hasSystem = notificationMethods.hasOwnProperty('system');
                
                if (!hasSms && hasEmail && hasSystem) {
                    setTestStatus('test4', 'success', 
                        `✅ 通知方式配置正确！<br/>
                        支持的通知方式: ${Object.keys(notificationMethods).join(', ')}<br/>
                        ✅ 已移除短信选项<br/>
                        ✅ 保留邮件和系统通知`
                    );
                } else {
                    const issues = [];
                    if (hasSms) issues.push('仍包含短信选项');
                    if (!hasEmail) issues.push('缺少邮件选项');
                    if (!hasSystem) issues.push('缺少系统通知选项');
                    
                    setTestStatus('test4', 'error', 
                        `❌ 通知方式配置问题: ${issues.join(', ')}<br/>
                        当前支持: ${Object.keys(notificationMethods).join(', ')}`
                    );
                }
            } catch (error) {
                setTestStatus('test4', 'error', 
                    `❌ 通知方式测试失败: ${error.response?.data?.message || error.message}`
                );
            }
        }

        async function testCreateRule() {
            setTestStatus('test5', 'loading');
            
            try {
                const testRule = {
                    name: '测试规则_' + Date.now(),
                    description: '自动化测试创建的规则',
                    rule_type: ['listing_status_change'],
                    conditions: {},
                    severity: 'medium',
                    priority: 'medium', // 测试medium优先级
                    notification_method: ['system'], // 只使用系统通知
                    status: true
                };
                
                const response = await axios.post(`${API_BASE}/alert-rules`, testRule);
                
                if (response.data.success && response.data.data) {
                    // 创建成功后删除测试规则
                    const ruleId = response.data.data.id;
                    await axios.delete(`${API_BASE}/alert-rules/${ruleId}`);
                    
                    setTestStatus('test5', 'success', 
                        `✅ 创建预警规则完整流程测试成功！<br/>
                        ✅ 成功创建规则 (ID: ${ruleId})<br/>
                        ✅ medium优先级验证通过<br/>
                        ✅ 系统通知方式验证通过<br/>
                        ✅ 测试规则已自动清理`
                    );
                } else {
                    setTestStatus('test5', 'error', 
                        `❌ 创建规则响应格式不正确: ${JSON.stringify(response.data)}`
                    );
                }
            } catch (error) {
                const errorMsg = error.response?.data?.message || error.message;
                const errors = error.response?.data?.errors;
                
                let errorDetail = errorMsg;
                if (errors) {
                    errorDetail += '<br/>详细错误: ' + JSON.stringify(errors);
                }
                
                setTestStatus('test5', 'error', 
                    `❌ 创建预警规则测试失败: ${errorDetail}`
                );
            }
        }

        async function runAllTests() {
            // 重置状态
            completedTests = 0;
            document.getElementById('finalSummary').style.display = 'none';
            
            // 重置所有测试状态
            for (let i = 1; i <= totalTests; i++) {
                setTestStatus(`test${i}`, 'pending');
                const testElement = document.getElementById(`test${i}`);
                testElement.querySelector('.status-icon').textContent = '⏳';
                testElement.querySelector('.result').innerHTML = '';
            }
            completedTests = 0;
            updateProgress();
            
            // 按顺序运行测试
            await new Promise(resolve => setTimeout(resolve, 500));
            await testPriorityFix();
            
            await new Promise(resolve => setTimeout(resolve, 500));
            await testStatsAPI();
            
            await new Promise(resolve => setTimeout(resolve, 500));
            await testAlertRulesAPI();
            
            await new Promise(resolve => setTimeout(resolve, 500));
            await testNotificationMethods();
            
            await new Promise(resolve => setTimeout(resolve, 500));
            await testCreateRule();
        }

        // 页面加载完成后显示初始状态
        window.onload = function() {
            updateProgress();
        };
    </script>
</body>
</html> 