<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>预警规则统计API最终验证测试</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .json-output { background: #f5f5f5; padding: 10px; border-radius: 3px; white-space: pre-wrap; font-family: monospace; }
        button { padding: 10px 15px; margin: 5px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #005a87; }
        input { padding: 8px; margin: 5px; border: 1px solid #ddd; border-radius: 3px; }
    </style>
</head>
<body>
    <h1>预警规则统计API最终验证测试</h1>
    
    <div class="test-section">
        <h3>🔐 认证设置</h3>
        <p>请先登录获取token，或手动输入有效的Bearer token：</p>
        <input type="text" id="tokenInput" placeholder="Bearer token (可选，将尝试自动获取)" style="width: 400px;">
        <br>
        <input type="text" id="baseUrlInput" value="http://localhost:8000/api" placeholder="API Base URL" style="width: 300px;">
        <button onclick="testLogin()">测试登录</button>
    </div>

    <div class="test-section">
        <h3>📊 统计API测试</h3>
        <button onclick="testStatsAPI()">测试 /alert-rules/stats API</button>
        <div id="statsResult"></div>
    </div>

    <div class="test-section">
        <h3>🔍 路由验证</h3>
        <button onclick="testRoutes()">验证相关路由</button>
        <div id="routeResult"></div>
    </div>

    <div class="test-section">
        <h3>✅ 创建规则测试</h3>
        <button onclick="testCreateRule()">测试创建预警规则</button>
        <div id="createResult"></div>
    </div>

    <div class="test-section">
        <h3>📝 测试结果总结</h3>
        <div id="summary"></div>
    </div>

    <script>
        let authToken = localStorage.getItem('authToken') || '';
        let baseUrl = 'http://localhost:8000/api';
        
        function updateSummary(message, type) {
            const summary = document.getElementById('summary');
            const now = new Date().toLocaleTimeString();
            summary.innerHTML += `<div class="${type}">[${now}] ${message}</div>`;
        }

        async function testLogin() {
            const customToken = document.getElementById('tokenInput').value.trim();
            baseUrl = document.getElementById('baseUrlInput').value.trim();
            
            if (customToken) {
                authToken = customToken.startsWith('Bearer ') ? customToken : `Bearer ${customToken}`;
                localStorage.setItem('authToken', authToken);
                updateSummary('已设置自定义token', 'info');
                return;
            }

            try {
                const response = await fetch(`${baseUrl}/auth/login`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        username: 'admin',  // 根据你的测试账户调整
                        password: 'password'
                    })
                });
                
                const data = await response.json();
                if (data.access_token) {
                    authToken = `Bearer ${data.access_token}`;
                    localStorage.setItem('authToken', authToken);
                    updateSummary('登录成功，已获取token', 'success');
                } else {
                    updateSummary('登录失败: ' + JSON.stringify(data), 'error');
                }
            } catch (error) {
                updateSummary('登录请求失败: ' + error.message, 'error');
            }
        }

        async function testStatsAPI() {
            const resultDiv = document.getElementById('statsResult');
            resultDiv.innerHTML = '<div class="info">测试中...</div>';
            
            try {
                const response = await fetch(`${baseUrl}/alert-rules/stats`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Authorization': authToken
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="success">✅ Stats API调用成功!</div>
                        <div class="json-output">${JSON.stringify(data, null, 2)}</div>
                    `;
                    updateSummary('Stats API测试通过', 'success');
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">❌ Stats API调用失败</div>
                        <div>状态码: ${response.status}</div>
                        <div class="json-output">${JSON.stringify(data, null, 2)}</div>
                    `;
                    updateSummary(`Stats API失败: ${response.status} - ${data.message || 'Unknown error'}`, 'error');
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 网络错误: ${error.message}</div>`;
                updateSummary('Stats API网络错误: ' + error.message, 'error');
            }
        }

        async function testRoutes() {
            const resultDiv = document.getElementById('routeResult');
            resultDiv.innerHTML = '<div class="info">验证路由中...</div>';
            
            const routes = [
                '/alert-rules',
                '/alert-rules/options',
                '/alert-rules/statistics'
            ];
            
            const results = [];
            
            for (const route of routes) {
                try {
                    const response = await fetch(`${baseUrl}${route}`, {
                        method: 'GET',
                        headers: {
                            'Accept': 'application/json',
                            'Authorization': authToken
                        }
                    });
                    
                    results.push({
                        route,
                        status: response.status,
                        success: response.ok
                    });
                } catch (error) {
                    results.push({
                        route,
                        status: 'ERROR',
                        success: false,
                        error: error.message
                    });
                }
            }
            
            let html = '<h4>路由验证结果:</h4>';
            results.forEach(result => {
                const statusClass = result.success ? 'success' : 'error';
                html += `<div class="${statusClass}">
                    ${result.route}: ${result.status} ${result.success ? '✅' : '❌'}
                    ${result.error ? ` (${result.error})` : ''}
                </div>`;
            });
            
            resultDiv.innerHTML = html;
            updateSummary(`路由验证完成: ${results.filter(r => r.success).length}/${results.length} 成功`, 'info');
        }

        async function testCreateRule() {
            const resultDiv = document.getElementById('createResult');
            resultDiv.innerHTML = '<div class="info">测试创建规则中...</div>';
            
            const testRule = {
                name: "测试预警规则" + Date.now(),
                description: "这是一个API测试规则",
                rule_type: ["promotion_price_deviation"],
                conditions: {
                    promotion_price_deviation: {
                        operator: ">",
                        threshold: 15
                    }
                },
                severity: "medium",
                priority: "medium",
                notification_method: ["system"],
                status: true
            };
            
            try {
                const response = await fetch(`${baseUrl}/alert-rules`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'Authorization': authToken
                    },
                    body: JSON.stringify(testRule)
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="success">✅ 规则创建成功!</div>
                        <div class="json-output">${JSON.stringify(data, null, 2)}</div>
                    `;
                    updateSummary('创建规则测试通过', 'success');
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">❌ 规则创建失败</div>
                        <div>状态码: ${response.status}</div>
                        <div class="json-output">${JSON.stringify(data, null, 2)}</div>
                    `;
                    updateSummary(`创建规则失败: ${response.status}`, 'error');
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 网络错误: ${error.message}</div>`;
                updateSummary('创建规则网络错误: ' + error.message, 'error');
            }
        }

        // 页面加载时初始化
        window.onload = function() {
            if (authToken) {
                document.getElementById('tokenInput').value = authToken;
                updateSummary('发现已保存的认证token', 'info');
            }
            updateSummary('测试页面已加载，请先设置认证信息', 'info');
        };
    </script>
</body>
</html> 