<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>预警规则功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .section {
            margin-bottom: 30px;
        }
        .section h2 {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .test-item {
            background: #f8f9fa;
            padding: 15px;
            border-left: 4px solid #007bff;
            margin-bottom: 15px;
        }
        .test-item h3 {
            margin-top: 0;
            color: #495057;
        }
        .result {
            background: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            border-left-color: #28a745;
        }
        .error {
            border-left-color: #dc3545;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .status {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>预警规则功能测试</h1>
        <p>本页面用于测试修改后的预警规则功能，包括三种预警类型的创建、查询和管理。</p>
    </div>

    <div class="container section">
        <h2>1. API连接测试</h2>
        <div class="test-item">
            <h3>测试后端API连接</h3>
            <button onclick="testAPIConnection()">测试连接</button>
            <div id="connection-result" class="result"></div>
        </div>
    </div>

    <div class="container section">
        <h2>2. 预警规则选项测试</h2>
        <div class="test-item">
            <h3>获取预警规则选项</h3>
            <button onclick="getAlertRuleOptions()">获取选项</button>
            <div id="options-result" class="result"></div>
        </div>
    </div>

    <div class="container section">
        <h2>3. 预警规则CRUD测试</h2>
        <div class="test-item">
            <h3>创建预警规则</h3>
            <button onclick="createPromotionRule()">创建促销价偏离率规则</button>
            <button onclick="createChannelRule()">创建渠道价格偏离率规则</button>
            <button onclick="createListingRule()">创建上下架状态规则</button>
            <div id="create-result" class="result"></div>
        </div>
        
        <div class="test-item">
            <h3>查询预警规则</h3>
            <button onclick="getAlertRules()">获取预警规则列表</button>
            <div id="list-result" class="result"></div>
        </div>
        
        <div class="test-item">
            <h3>清理测试数据</h3>
            <button onclick="cleanupTestData()">删除测试规则</button>
            <div id="cleanup-result" class="result"></div>
        </div>
    </div>

    <div class="container section">
        <h2>测试状态</h2>
        <div id="test-status">
            <span class="status">等待测试</span>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000/api';
        let authToken = null;
        let testRuleIds = [];

        // 设置状态
        function setStatus(message, type = 'info') {
            const statusEl = document.getElementById('test-status');
            statusEl.innerHTML = `<span class="status ${type}">${message}</span>`;
        }

        // API请求函数
        async function apiRequest(endpoint, options = {}) {
            const url = `${API_BASE}${endpoint}`;
            const headers = {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                ...options.headers
            };

            if (authToken) {
                headers['Authorization'] = `Bearer ${authToken}`;
            }

            try {
                const response = await fetch(url, {
                    ...options,
                    headers
                });

                const data = await response.json();
                return { success: response.ok, data, status: response.status };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        // 1. 测试API连接
        async function testAPIConnection() {
            setStatus('正在测试API连接...', 'info');
            const resultEl = document.getElementById('connection-result');
            
            try {
                // 先尝试获取dashboard统计（公开接口）
                const result = await apiRequest('/dashboard-stats');
                
                if (result.success) {
                    resultEl.textContent = `✅ API连接成功\n状态码: ${result.status}\n响应: ${JSON.stringify(result.data, null, 2)}`;
                    setStatus('API连接成功', 'success');
                } else {
                    resultEl.textContent = `❌ API连接失败\n状态码: ${result.status}\n错误: ${JSON.stringify(result.data, null, 2)}`;
                    setStatus('API连接失败', 'error');
                }
            } catch (error) {
                resultEl.textContent = `❌ 连接错误: ${error.message}`;
                setStatus('连接错误', 'error');
            }
        }

        // 2. 获取预警规则选项
        async function getAlertRuleOptions() {
            setStatus('正在获取预警规则选项...', 'info');
            const resultEl = document.getElementById('options-result');
            
            const result = await apiRequest('/alert-rules/options');
            
            if (result.success) {
                resultEl.textContent = `✅ 获取选项成功\n${JSON.stringify(result.data, null, 2)}`;
                setStatus('获取选项成功', 'success');
            } else {
                resultEl.textContent = `❌ 获取选项失败\n${JSON.stringify(result, null, 2)}`;
                setStatus('获取选项失败', 'error');
            }
        }

        // 3. 创建促销价偏离率规则
        async function createPromotionRule() {
            const resultEl = document.getElementById('create-result');
            
            const ruleData = {
                name: '测试-促销价偏离率规则',
                description: '用于测试的促销价偏离率预警规则',
                rule_type: 'promotion_price_deviation',
                operator: '>',
                threshold_values: { value: 15 },
                severity: 'medium',
                priority: 'normal',
                notification_method: 'email'
            };

            const result = await apiRequest('/alert-rules', {
                method: 'POST',
                body: JSON.stringify(ruleData)
            });

            if (result.success) {
                testRuleIds.push(result.data.data.id);
                resultEl.textContent += `✅ 创建促销价偏离率规则成功 (ID: ${result.data.data.id})\n`;
            } else {
                resultEl.textContent += `❌ 创建促销价偏离率规则失败: ${JSON.stringify(result, null, 2)}\n`;
            }
        }

        // 创建渠道价格偏离率规则
        async function createChannelRule() {
            const resultEl = document.getElementById('create-result');
            
            const ruleData = {
                name: '测试-渠道价格偏离率规则',
                description: '用于测试的渠道价格偏离率预警规则',
                rule_type: 'channel_price_deviation',
                operator: '>=',
                threshold_values: { value: 10 },
                severity: 'high',
                priority: 'high',
                notification_method: 'system'
            };

            const result = await apiRequest('/alert-rules', {
                method: 'POST',
                body: JSON.stringify(ruleData)
            });

            if (result.success) {
                testRuleIds.push(result.data.data.id);
                resultEl.textContent += `✅ 创建渠道价格偏离率规则成功 (ID: ${result.data.data.id})\n`;
            } else {
                resultEl.textContent += `❌ 创建渠道价格偏离率规则失败: ${JSON.stringify(result, null, 2)}\n`;
            }
        }

        // 创建上下架状态规则
        async function createListingRule() {
            const resultEl = document.getElementById('create-result');
            
            const ruleData = {
                name: '测试-上下架状态规则',
                description: '用于测试的商品下架预警规则',
                rule_type: 'listing_status_change',
                severity: 'critical',
                priority: 'urgent',
                notification_method: 'email'
            };

            const result = await apiRequest('/alert-rules', {
                method: 'POST',
                body: JSON.stringify(ruleData)
            });

            if (result.success) {
                testRuleIds.push(result.data.data.id);
                resultEl.textContent += `✅ 创建上下架状态规则成功 (ID: ${result.data.data.id})\n`;
            } else {
                resultEl.textContent += `❌ 创建上下架状态规则失败: ${JSON.stringify(result, null, 2)}\n`;
            }
        }

        // 获取预警规则列表
        async function getAlertRules() {
            setStatus('正在获取预警规则列表...', 'info');
            const resultEl = document.getElementById('list-result');
            
            const result = await apiRequest('/alert-rules?per_page=100');
            
            if (result.success) {
                const rules = result.data.data.data || result.data.data || [];
                resultEl.textContent = `✅ 获取规则列表成功 (共${rules.length}条)\n`;
                
                rules.forEach(rule => {
                    resultEl.textContent += `\n规则: ${rule.name} (${rule.rule_type})\n`;
                    resultEl.textContent += `  状态: ${rule.status ? '启用' : '禁用'}\n`;
                    if (rule.rule_type !== 'listing_status_change') {
                        resultEl.textContent += `  条件: ${rule.operator} ${rule.threshold_values?.value}%\n`;
                    } else {
                        resultEl.textContent += `  条件: 商品下架时预警\n`;
                    }
                    resultEl.textContent += `  通知: ${rule.notification_method}\n`;
                });
                
                setStatus(`获取规则列表成功 (${rules.length}条)`, 'success');
            } else {
                resultEl.textContent = `❌ 获取规则列表失败\n${JSON.stringify(result, null, 2)}`;
                setStatus('获取规则列表失败', 'error');
            }
        }

        // 清理测试数据
        async function cleanupTestData() {
            setStatus('正在清理测试数据...', 'info');
            const resultEl = document.getElementById('cleanup-result');
            let deletedCount = 0;

            for (const ruleId of testRuleIds) {
                const result = await apiRequest(`/alert-rules/${ruleId}`, {
                    method: 'DELETE'
                });
                
                if (result.success) {
                    deletedCount++;
                    resultEl.textContent += `✅ 删除规则 ${ruleId} 成功\n`;
                } else {
                    resultEl.textContent += `❌ 删除规则 ${ruleId} 失败: ${JSON.stringify(result, null, 2)}\n`;
                }
            }

            testRuleIds = [];
            resultEl.textContent += `\n清理完成，删除了 ${deletedCount} 个测试规则`;
            setStatus(`清理完成 (删除${deletedCount}条)`, 'success');
        }

        // 页面加载时自动测试连接
        window.addEventListener('load', () => {
            setTimeout(testAPIConnection, 1000);
        });
    </script>
</body>
</html> 