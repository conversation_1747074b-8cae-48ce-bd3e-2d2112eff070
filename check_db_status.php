<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\ProductData;
use App\Models\MonitoringTask;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

echo "=== 数据库状态检查 ===" . PHP_EOL;

// 检查ProductData表
$totalRecords = ProductData::count();
echo "ProductData总记录数: {$totalRecords}" . PHP_EOL;

if ($totalRecords > 0) {
    $latest = ProductData::latest()->first();
    echo "最新记录ID: {$latest->id}" . PHP_EOL;
    echo "商品ID: {$latest->item_id}" . PHP_EOL;
    echo "监控任务ID: {$latest->monitoring_task_id}" . PHP_EOL;
    echo "创建时间: {$latest->created_at}" . PHP_EOL;
    echo "更新时间: {$latest->updated_at}" . PHP_EOL;
    echo "最后采集时间: {$latest->last_collected_at}" . PHP_EOL;
}

// 检查monitoring_tasks表结构
echo PHP_EOL . "=== monitoring_tasks表结构 ===" . PHP_EOL;
$columns = DB::select('DESCRIBE monitoring_tasks');
foreach($columns as $col) {
    echo $col->Field . ' - ' . $col->Type . PHP_EOL;
}

// 检查MonitoringTask（使用正确的字段）
echo PHP_EOL . "=== 监控任务状态 ===" . PHP_EOL;
$tasks = MonitoringTask::all(['id', 'name', 'data_source_id', 'status']);
foreach ($tasks as $task) {
    echo "任务ID: {$task->id}, 名称: {$task->name}, 数据源ID: {$task->data_source_id}, 状态: {$task->status}" . PHP_EOL;
}

// 检查特定商品的记录
echo PHP_EOL . "=== 商品804460152151的记录 ===" . PHP_EOL;
$records = ProductData::where('item_id', '804460152151')->get(['id', 'monitoring_task_id', 'created_at', 'updated_at', 'last_collected_at']);
echo "该商品共有 {$records->count()} 条记录:" . PHP_EOL;
foreach ($records as $record) {
    echo "记录ID: {$record->id}, 任务ID: {$record->monitoring_task_id}, 创建: {$record->created_at}, 更新: {$record->updated_at}, 采集: {$record->last_collected_at}" . PHP_EOL;
}

// 检查是否有重复的测试
echo PHP_EOL . "=== 最近的数据采集记录 ===" . PHP_EOL;
$recentRecords = ProductData::orderBy('updated_at', 'desc')->take(5)->get(['id', 'item_id', 'monitoring_task_id', 'updated_at', 'last_collected_at']);
foreach ($recentRecords as $record) {
    echo "ID: {$record->id}, 商品: {$record->item_id}, 任务: {$record->monitoring_task_id}, 更新: {$record->updated_at}" . PHP_EOL;
} 