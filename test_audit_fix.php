<?php

require_once __DIR__ . '/vendor/autoload.php';

// 启动Laravel应用
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Services\DataCollectionService;
use App\Models\DataSource;
use App\Models\ProductData;

echo "=== 测试审计日志修复 ===\n";

try {
    // 获取数据源
    $dataSource = DataSource::find(3);
    if (!$dataSource) {
        echo "错误：未找到数据源ID为3的记录\n";
        exit(1);
    }

    echo "1. 数据源配置检查...\n";
    echo "认证类型: " . ($dataSource->config['auth_type'] ?? 'none') . "\n";
    echo "API Key名称: " . ($dataSource->config['api_key_name'] ?? 'N/A') . "\n";
    echo "API Key值: " . (isset($dataSource->config['api_key_value']) ? substr($dataSource->config['api_key_value'], 0, 8) . '...' : 'N/A') . "\n";

    // 检查修复前的记录数
    $beforeCount = ProductData::count();
    echo "\n2. 修复前ProductData记录数: {$beforeCount}\n";

    // 执行数据采集
    echo "\n3. 执行数据采集...\n";
    $dataCollectionService = new DataCollectionService();
    $testItemId = '804460152151';
    $monitoringTaskId = 2;

    $result = $dataCollectionService->collectAndStandardize(
        $dataSource->id,
        $testItemId,
        [],
        $monitoringTaskId
    );

    // 检查修复后的记录数
    $afterCount = ProductData::count();
    echo "修复后ProductData记录数: {$afterCount}\n";

    if ($result['success']) {
        echo "✓ 数据采集成功\n";
        echo "  原始数据字段数: " . count($result['raw_data']) . "\n";
        echo "  标准化数据字段数: " . count($result['standardized_data']) . "\n";
        echo "  产品数据ID: " . $result['product_data_id'] . "\n";
        
        // 检查最新记录的时间
        $latestRecord = ProductData::find($result['product_data_id']);
        echo "  最后采集时间: " . $latestRecord->last_collected_at . "\n";
        echo "  更新时间: " . $latestRecord->updated_at . "\n";
    } else {
        echo "✗ 数据采集失败: " . $result['message'] . "\n";
    }

} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    echo "堆栈跟踪: " . $e->getTraceAsString() . "\n";
}

echo "\n=== 测试完成 ===\n"; 