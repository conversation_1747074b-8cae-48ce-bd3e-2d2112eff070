<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>预警规则分页修复验证测试</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .json-output { background: #f5f5f5; padding: 10px; border-radius: 3px; white-space: pre-wrap; font-family: monospace; max-height: 300px; overflow-y: auto; }
        button { padding: 10px 15px; margin: 5px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #005a87; }
        input { padding: 8px; margin: 5px; border: 1px solid #ddd; border-radius: 3px; }
        .highlight { background-color: yellow; }
    </style>
</head>
<body>
    <h1>预警规则分页修复验证测试</h1>
    
    <div class="test-section">
        <h3>🔐 认证设置</h3>
        <input type="text" id="tokenInput" placeholder="Bearer token" style="width: 400px;">
        <input type="text" id="baseUrlInput" value="http://localhost:8000/api" placeholder="API Base URL" style="width: 300px;">
        <button onclick="setAuth()">设置认证</button>
    </div>

    <div class="test-section">
        <h3>📊 Stats API测试 (已修复)</h3>
        <button onclick="testStatsAPI()">测试 /alert-rules/stats</button>
        <div id="statsResult"></div>
    </div>

    <div class="test-section">
        <h3>📋 Rules列表API测试</h3>
        <button onclick="testRulesAPI()">测试 /alert-rules 列表</button>
        <div id="rulesResult"></div>
    </div>

    <div class="test-section">
        <h3>🔍 响应结构分析</h3>
        <button onclick="analyzeResponseStructure()">分析响应数据结构</button>
        <div id="structureResult"></div>
    </div>

    <div class="test-section">
        <h3>📝 测试结果总结</h3>
        <div id="summary"></div>
    </div>

    <script>
        let authToken = localStorage.getItem('authToken') || '';
        let baseUrl = 'http://localhost:8000/api';
        
        function updateSummary(message, type) {
            const summary = document.getElementById('summary');
            const now = new Date().toLocaleTimeString();
            summary.innerHTML += `<div class="${type}">[${now}] ${message}</div>`;
        }

        function setAuth() {
            const customToken = document.getElementById('tokenInput').value.trim();
            baseUrl = document.getElementById('baseUrlInput').value.trim();
            
            if (customToken) {
                authToken = customToken.startsWith('Bearer ') ? customToken : `Bearer ${customToken}`;
                localStorage.setItem('authToken', authToken);
                updateSummary('已设置认证信息', 'info');
            } else {
                updateSummary('请输入有效的token', 'error');
            }
        }

        async function testStatsAPI() {
            const resultDiv = document.getElementById('statsResult');
            resultDiv.innerHTML = '<div class="info">测试stats API...</div>';
            
            try {
                const response = await fetch(`${baseUrl}/alert-rules/stats`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Authorization': authToken
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="success">✅ Stats API正常工作!</div>
                        <div class="json-output">${JSON.stringify(data, null, 2)}</div>
                    `;
                    updateSummary('Stats API测试通过', 'success');
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">❌ Stats API失败: ${response.status}</div>
                        <div class="json-output">${JSON.stringify(data, null, 2)}</div>
                    `;
                    updateSummary(`Stats API失败: ${response.status}`, 'error');
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 网络错误: ${error.message}</div>`;
                updateSummary('Stats API网络错误: ' + error.message, 'error');
            }
        }

        async function testRulesAPI() {
            const resultDiv = document.getElementById('rulesResult');
            resultDiv.innerHTML = '<div class="info">测试rules列表API...</div>';
            
            try {
                const response = await fetch(`${baseUrl}/alert-rules?page=1&per_page=10`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Authorization': authToken
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="success">✅ Rules API正常工作!</div>
                        <div><strong>数据结构:</strong></div>
                        <div class="json-output">${JSON.stringify(data, null, 2)}</div>
                    `;
                    updateSummary('Rules API测试通过', 'success');
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">❌ Rules API失败: ${response.status}</div>
                        <div class="json-output">${JSON.stringify(data, null, 2)}</div>
                    `;
                    updateSummary(`Rules API失败: ${response.status}`, 'error');
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 网络错误: ${error.message}</div>`;
                updateSummary('Rules API网络错误: ' + error.message, 'error');
            }
        }

        async function analyzeResponseStructure() {
            const resultDiv = document.getElementById('structureResult');
            resultDiv.innerHTML = '<div class="info">分析响应结构...</div>';
            
            try {
                const response = await fetch(`${baseUrl}/alert-rules?page=1&per_page=5`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Authorization': authToken
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    let analysis = '<h4>📊 响应结构分析:</h4>';
                    
                    // 分析顶层结构
                    analysis += '<div><strong>顶层属性:</strong></div>';
                    analysis += '<ul>';
                    Object.keys(data).forEach(key => {
                        analysis += `<li><span class="highlight">${key}</span>: ${typeof data[key]}</li>`;
                    });
                    analysis += '</ul>';
                    
                    // 分析data属性
                    if (data.data) {
                        analysis += '<div><strong>data属性结构:</strong></div>';
                        analysis += '<ul>';
                        Object.keys(data.data).forEach(key => {
                            const value = data.data[key];
                            analysis += `<li><span class="highlight">${key}</span>: ${typeof value} ${Array.isArray(value) ? `(数组长度: ${value.length})` : ''}</li>`;
                        });
                        analysis += '</ul>';
                        
                        // 重点显示分页相关字段
                        analysis += '<div><strong>📄 分页相关字段:</strong></div>';
                        analysis += '<ul>';
                        ['total', 'per_page', 'current_page', 'last_page', 'from', 'to'].forEach(field => {
                            if (data.data.hasOwnProperty(field)) {
                                analysis += `<li>✅ <span class="highlight">${field}</span>: ${data.data[field]}</li>`;
                            } else {
                                analysis += `<li>❌ <span class="highlight">${field}</span>: 不存在</li>`;
                            }
                        });
                        analysis += '</ul>';
                        
                        // 显示前端应该如何访问
                        analysis += '<div><strong>🔧 前端访问方式:</strong></div>';
                        analysis += '<ul>';
                        analysis += '<li>规则数据: <code>response.data.data</code></li>';
                        analysis += '<li>总数: <code>response.data.total</code></li>';
                        analysis += '<li>当前页: <code>response.data.current_page</code></li>';
                        analysis += '</ul>';
                    }
                    
                    analysis += '<div><strong>完整响应:</strong></div>';
                    analysis += `<div class="json-output">${JSON.stringify(data, null, 2)}</div>`;
                    
                    resultDiv.innerHTML = analysis;
                    updateSummary('响应结构分析完成', 'info');
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ 无法获取数据进行分析: ${response.status}</div>`;
                    updateSummary('结构分析失败', 'error');
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 分析过程出错: ${error.message}</div>`;
                updateSummary('结构分析错误: ' + error.message, 'error');
            }
        }

        // 页面加载时初始化
        window.onload = function() {
            if (authToken) {
                document.getElementById('tokenInput').value = authToken;
                updateSummary('发现已保存的认证token', 'info');
            }
            updateSummary('测试页面已加载', 'info');
        };
    </script>
</body>
</html> 