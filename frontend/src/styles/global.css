/* 全局样式重置和优化 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  width: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
  background-color: #f5f5f5;
}

#app {
  height: 100vh;
  width: 100vw;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
}

/* Element Plus 全局样式修复 */
.el-container {
  height: 100% !important;
  width: 100% !important;
}

.el-header {
  height: 60px !important;
  line-height: 60px !important;
  padding: 0 20px !important;
  background: #fff !important;
  border-bottom: 1px solid #e6e6e6 !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.el-aside {
  background: #001529 !important;
  border-right: 1px solid #e6e6e6 !important;
}

.el-main {
  padding: 20px !important;
  background: #f0f2f5 !important;
  overflow-y: auto !important;
}

/* 修复消息提示位置 */
.el-message {
  top: 80px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  z-index: 3000 !important;
  min-width: 300px !important;
  text-align: center !important;
}

.el-message-box {
  z-index: 3000 !important;
}

.el-notification {
  z-index: 3000 !important;
  top: 80px !important;
}

/* 修复 loading 样式 */
.el-loading-mask {
  z-index: 2999 !important;
}

/* 菜单样式优化 */
.el-menu {
  border: none !important;
}

.el-menu--dark {
  background-color: #001529 !important;
}

.el-menu--dark .el-menu-item,
.el-menu--dark .el-sub-menu__title {
  color: #ffffff !important;
}

.el-menu--dark .el-menu-item:hover,
.el-menu--dark .el-sub-menu__title:hover {
  background-color: #1890ff !important;
  color: #ffffff !important;
}

.el-menu--dark .el-menu-item.is-active {
  background-color: #1890ff !important;
  color: #ffffff !important;
}

/* 卡片样式优化 */
.el-card {
  border-radius: 8px !important;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1) !important;
  border: none !important;
  margin-bottom: 20px !important;
}

.el-card__header {
  background-color: #fafafa !important;
  border-bottom: 1px solid #f0f0f0 !important;
  padding: 16px 20px !important;
}

.el-card__body {
  padding: 20px !important;
}

/* 表格样式优化 */
.el-table {
  border-radius: 8px !important;
  overflow: hidden !important;
}

.el-table th.el-table__cell {
  background-color: #fafafa !important;
  color: #606266 !important;
  font-weight: 600 !important;
}

.el-table--border,
.el-table--group {
  border: 1px solid #f0f0f0 !important;
}

/* 按钮样式优化 */
.el-button {
  border-radius: 6px !important;
  font-weight: 500 !important;
  transition: all 0.3s ease !important;
}

.el-button--primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border: none !important;
}

.el-button--primary:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4) !important;
}

/* 输入框样式优化 */
.el-input__wrapper {
  border-radius: 6px !important;
  transition: all 0.3s ease !important;
}

.el-input__wrapper:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

.el-input__wrapper.is-focus {
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2) !important;
}

/* 对话框样式优化 */
.el-dialog {
  border-radius: 12px !important;
  overflow: hidden !important;
}

.el-dialog__header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  color: #fff !important;
  padding: 20px !important;
}

.el-dialog__title {
  color: #fff !important;
  font-weight: 600 !important;
}

.el-dialog__headerbtn .el-dialog__close {
  color: #fff !important;
}

/* 面包屑样式 */
.el-breadcrumb {
  margin-bottom: 20px !important;
}

/* 分页样式 */
.el-pagination {
  margin-top: 20px !important;
  text-align: center !important;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
  transition: background 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .el-main {
    padding: 10px !important;
  }
  
  .el-card {
    margin-bottom: 10px !important;
  }
  
  .el-card__body {
    padding: 15px !important;
  }
}

/* 动画效果 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-fade-enter-active {
  transition: all 0.3s ease-out;
}

.slide-fade-leave-active {
  transition: all 0.3s cubic-bezier(1.0, 0.5, 0.8, 1.0);
}

.slide-fade-enter-from,
.slide-fade-leave-to {
  transform: translateX(20px);
  opacity: 0;
}

/* 自定义工具类 */
.text-center {
  text-align: center !important;
}

.text-left {
  text-align: left !important;
}

.text-right {
  text-align: right !important;
}

.mb-0 { margin-bottom: 0 !important; }
.mb-1 { margin-bottom: 8px !important; }
.mb-2 { margin-bottom: 16px !important; }
.mb-3 { margin-bottom: 24px !important; }
.mb-4 { margin-bottom: 32px !important; }

.mt-0 { margin-top: 0 !important; }
.mt-1 { margin-top: 8px !important; }
.mt-2 { margin-top: 16px !important; }
.mt-3 { margin-top: 24px !important; }
.mt-4 { margin-top: 32px !important; }

.p-0 { padding: 0 !important; }
.p-1 { padding: 8px !important; }
.p-2 { padding: 16px !important; }
.p-3 { padding: 24px !important; }
.p-4 { padding: 32px !important; }

.w-100 { width: 100% !important; }
.h-100 { height: 100% !important; }

.d-flex { display: flex !important; }
.flex-column { flex-direction: column !important; }
.justify-center { justify-content: center !important; }
.align-center { align-items: center !important; }
.flex-wrap { flex-wrap: wrap !important; }

.text-primary { color: #409eff !important; }
.text-success { color: #67c23a !important; }
.text-warning { color: #e6a23c !important; }
.text-danger { color: #f56c6c !important; }
.text-info { color: #909399 !important; }

.bg-primary { background-color: #409eff !important; }
.bg-success { background-color: #67c23a !important; }
.bg-warning { background-color: #e6a23c !important; }
.bg-danger { background-color: #f56c6c !important; }
.bg-info { background-color: #909399 !important; } 