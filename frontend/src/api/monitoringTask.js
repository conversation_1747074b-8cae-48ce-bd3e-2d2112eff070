import apiClient from './apiClient';

export const getAllMonitoringTasks = () => {
  return apiClient.get('/monitoring-tasks/all');
};

export const getMonitoringTasks = (params) => {
  return apiClient.get('/monitoring-tasks', { params });
};

export const getMonitoringTask = (id) => {
  return apiClient.get(`/monitoring-tasks/${id}`);
};

export const createMonitoringTask = (data) => {
  return apiClient.post('/monitoring-tasks', data);
};

export const updateMonitoringTask = (id, data) => {
  return apiClient.put(`/monitoring-tasks/${id}`, data);
};

export const deleteMonitoringTask = (id) => {
  return apiClient.delete(`/monitoring-tasks/${id}`);
};

export const startMonitoringTask = (id) => {
  return apiClient.post(`/monitoring-tasks/${id}/start`);
};

export const pauseMonitoringTask = (id) => {
  return apiClient.post(`/monitoring-tasks/${id}/pause`);
};

export const stopMonitoringTask = (id) => {
  return apiClient.post(`/monitoring-tasks/${id}/stop`);
};

export const duplicateMonitoringTask = (id) => {
  return apiClient.post(`/monitoring-tasks/${id}/duplicate`);
};

// 更新任务状态（start / pause / stop 等）
export const updateTaskStatus = (id, status) => {
  return apiClient.patch(`/monitoring-tasks/${id}/status`, { status });
};

// 立即执行一次任务
export const runTaskNow = (id) => {
  return apiClient.post(`/monitoring-tasks/${id}/run-now`);
};

// 默认导出对象，包含所有函数
export default {
  getAllTasks: getAllMonitoringTasks,
  getTasks: getMonitoringTasks,
  getTask: getMonitoringTask,
  createTask: createMonitoringTask,
  updateTask: updateMonitoringTask,
  deleteTask: deleteMonitoringTask,
  updateTaskStatus,
  runTaskNow,
  duplicateTask: duplicateMonitoringTask
};

// ... 您可以根据需要添加其他与监控任务相关的API函数 ... 