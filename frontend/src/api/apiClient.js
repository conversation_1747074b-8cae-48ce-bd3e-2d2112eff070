import axios from 'axios';
import { useAuthStore } from '../stores/auth';
import { ElMessage, ElMessageBox } from 'element-plus';

const apiClient = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api',
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

// 请求拦截器
apiClient.interceptors.request.use(
  (config) => {
    const authStore = useAuthStore();
    const token = authStore.token;
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
apiClient.interceptors.response.use(
  (response) => {
    // 对响应数据做点什么
    return response.data;
  },
  (error) => {
    const authStore = useAuthStore();

    if (error.response) {
      const { status, data } = error.response;

      switch (status) {
        case 401:
          // 未授权，可能是token过期
          authStore.logout();
          window.location.href = '/login';
          ElMessage.error('登录已过期，请重新登录');
          break;
        case 403:
          // 禁止访问
          ElMessage.error(data.message || '您没有权限执行此操作');
          break;
        case 404:
          ElMessage.error('请求的资源未找到');
          break;
        case 422:
          // 表单验证错误
          const errors = Object.values(data.errors).flat();
          ElMessage.error(errors.join('\n') || '表单验证失败');
          break;
        case 500:
        case 501:
        case 502:
        case 503:
          ElMessage.error(data.message || '服务器错误，请稍后再试');
          break;
        default:
          ElMessage.error(data.message || `请求错误: ${status}`);
      }
    } else if (error.request) {
      // 请求已发出，但没有收到响应
      ElMessage.error('无法连接到服务器，请检查您的网络连接');
    } else {
      // 发送请求时出了点问题
      ElMessage.error(`请求失败: ${error.message}`);
    }

    return Promise.reject(error);
  }
);

export default apiClient; 