import apiClient from './apiClient'

const notificationApi = {
  // 获取通知列表
  getNotifications(params) {
    return apiClient.get('/notifications', { params })
  },

  // 获取单个通知详情
  getNotification(id) {
    return apiClient.get(`/notifications/${id}`)
  },

  // 标记通知为已读
  markAsRead(id) {
    return apiClient.patch(`/notifications/${id}/read`)
  },

  // 标记所有通知为已读
  markAllAsRead() {
    return apiClient.post('/notifications/mark-all-read')
  },

  // 删除通知
  deleteNotification(id) {
    return apiClient.delete(`/notifications/${id}`)
  },

  // 批量删除通知
  batchDelete(ids) {
    return apiClient.delete('/notifications', { data: { ids } })
  },

  // 清空所有通知
  clearAll() {
    return apiClient.delete('/notifications/clear')
  },

  // 获取通知统计信息
  getStatistics() {
    return apiClient.get('/notifications/statistics')
  }
}

export default notificationApi
