<template>
  <div class="captcha-container">
    <canvas 
      ref="captchaCanvas" 
      :width="width" 
      :height="height"
      @click="refreshCaptcha"
      class="captcha-canvas"
    ></canvas>
    <el-button 
      type="text" 
      @click="refreshCaptcha"
      class="refresh-btn"
      size="small"
    >
      <el-icon><Refresh /></el-icon>
      刷新
    </el-button>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'

interface Props {
  width?: number
  height?: number
  length?: number
}

interface Emits {
  (e: 'update:value', value: string): void
}

const props = withDefaults(defineProps<Props>(), {
  width: 120,
  height: 40,
  length: 4
})

const emit = defineEmits<Emits>()

const captchaCanvas = ref<HTMLCanvasElement>()
const captchaValue = ref('')

// 生成随机字符
const generateRandomChar = (): string => {
  const chars = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678'
  return chars.charAt(Math.floor(Math.random() * chars.length))
}

// 生成随机颜色
const getRandomColor = (min: number = 0, max: number = 255): string => {
  const r = Math.floor(Math.random() * (max - min + 1)) + min
  const g = Math.floor(Math.random() * (max - min + 1)) + min
  const b = Math.floor(Math.random() * (max - min + 1)) + min
  return `rgb(${r}, ${g}, ${b})`
}

// 绘制验证码
const drawCaptcha = () => {
  if (!captchaCanvas.value) return
  
  const canvas = captchaCanvas.value
  const ctx = canvas.getContext('2d')
  if (!ctx) return
  
  // 清空画布
  ctx.clearRect(0, 0, props.width, props.height)
  
  // 设置背景
  ctx.fillStyle = getRandomColor(180, 230)
  ctx.fillRect(0, 0, props.width, props.height)
  
  // 生成验证码字符串
  let code = ''
  for (let i = 0; i < props.length; i++) {
    code += generateRandomChar()
  }
  captchaValue.value = code
  emit('update:value', code)
  
  // 绘制文字
  const fontSize = props.height * 0.6
  ctx.font = `${fontSize}px Arial`
  ctx.textBaseline = 'middle'
  
  for (let i = 0; i < code.length; i++) {
    const char = code[i]
    const x = (props.width / props.length) * i + (props.width / props.length) * 0.3
    const y = props.height / 2
    
    // 随机旋转角度
    const angle = (Math.random() - 0.5) * 0.4
    
    ctx.save()
    ctx.translate(x, y)
    ctx.rotate(angle)
    ctx.fillStyle = getRandomColor(20, 160)
    ctx.fillText(char, 0, 0)
    ctx.restore()
  }
  
  // 绘制干扰线
  for (let i = 0; i < 5; i++) {
    ctx.strokeStyle = getRandomColor(40, 180)
    ctx.lineWidth = 1
    ctx.beginPath()
    ctx.moveTo(Math.random() * props.width, Math.random() * props.height)
    ctx.lineTo(Math.random() * props.width, Math.random() * props.height)
    ctx.stroke()
  }
  
  // 绘制干扰点
  for (let i = 0; i < 40; i++) {
    ctx.fillStyle = getRandomColor(0, 255)
    ctx.beginPath()
    ctx.arc(Math.random() * props.width, Math.random() * props.height, 1, 0, 2 * Math.PI)
    ctx.fill()
  }
}

// 刷新验证码
const refreshCaptcha = () => {
  nextTick(() => {
    drawCaptcha()
  })
}

// 获取验证码值
const getCaptchaValue = (): string => {
  return captchaValue.value
}

// 验证验证码
const verifyCaptcha = (inputValue: string): boolean => {
  return inputValue.toLowerCase() === captchaValue.value.toLowerCase()
}

onMounted(() => {
  drawCaptcha()
})

// 暴露方法
defineExpose({
  refresh: refreshCaptcha,
  getValue: getCaptchaValue,
  verify: verifyCaptcha
})
</script>

<style scoped>
.captcha-container {
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.captcha-canvas {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  cursor: pointer;
  transition: border-color 0.3s ease;
}

.captcha-canvas:hover {
  border-color: #409eff;
}

.refresh-btn {
  padding: 0;
  color: #409eff;
  font-size: 12px;
}

.refresh-btn:hover {
  color: #66b1ff;
}
</style> 