<template>
  <div class="test-alert">
    <h2>测试预警规则表单</h2>
    
    <el-form :model="form" label-width="120px" style="max-width: 600px;">
      <el-form-item label="规则类型">
        <el-select v-model="form.rule_type" multiple placeholder="请选择规则类型" style="width: 100%">
          <el-option
            v-for="(label, value) in ruleTypes"
            :key="value"
            :label="label"
            :value="value"
          />
        </el-select>
      </el-form-item>

      <!-- 调试信息 -->
      <div style="background: #f0f0f0; padding: 10px; margin: 10px 0; font-size: 12px;">
        <strong>调试信息:</strong><br>
        选中的规则类型: {{ JSON.stringify(form.rule_type) }}<br>
        条件对象: {{ JSON.stringify(form.conditions) }}
      </div>

      <!-- 动态显示条件配置 -->
      <div v-for="ruleType in form.rule_type" :key="ruleType">
        <template v-if="needsCondition(ruleType)">
          <el-divider>{{ ruleTypes[ruleType] }} 配置</el-divider>
          
          <el-form-item :label="`操作符`">
            <el-select v-model="form.conditions[ruleType].operator" style="width: 100%">
              <el-option label="大于" value=">" />
              <el-option label="小于" value="<" />
              <el-option label="等于" value="=" />
            </el-select>
          </el-form-item>
          
          <el-form-item :label="`阈值`">
            <el-input-number v-model="form.conditions[ruleType].threshold" :min="0" :max="100" />
          </el-form-item>
        </template>
      </div>
    </el-form>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'

const form = ref({
  rule_type: [],
  conditions: {
    promotion_price_deviation: { operator: '>', threshold: 10 },
    channel_price_deviation: { operator: '>', threshold: 10 }
  }
})

const ruleTypes = {
  promotion_price_deviation: '促销价偏离率',
  channel_price_deviation: '渠道价格偏离率',
  listing_status_change: '上下架状态变化'
}

const needsCondition = (ruleType) => {
  return ruleType !== 'listing_status_change'
}

// 监听规则类型变化
watch(() => form.value.rule_type, (newTypes) => {
  console.log('规则类型变化:', newTypes)
  
  if (Array.isArray(newTypes)) {
    newTypes.forEach(type => {
      if (needsCondition(type) && !form.value.conditions[type]) {
        form.value.conditions[type] = { operator: '>', threshold: 10 }
        console.log('为规则类型添加条件:', type)
      }
    })
  }
}, { deep: true })
</script>

<style scoped>
.test-alert {
  padding: 20px;
}
</style>
