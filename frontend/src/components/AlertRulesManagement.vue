<template>
  <div class="alert-rules-management">
    <!-- 工具栏 -->
    <div class="toolbar mb-4 flex justify-between items-center">
      <el-button type="primary" :icon="Plus" @click="handleCreate">新建规则</el-button>
      <div>
        <el-input 
          v-model="searchKeyword" 
          placeholder="搜索规则名称" 
          class="w-64 mr-2" 
          clearable 
          @clear="fetchRules" 
          @keyup.enter="fetchRules" 
        />
        <el-button :icon="Search" @click="fetchRules" :loading="loading">搜索</el-button>
      </div>
    </div>

    <!-- 规则表格 -->
    <el-table :data="rules" v-loading="loading" style="width: 100%">
      <el-table-column prop="name" label="规则名称" width="200" />
      <el-table-column prop="rule_type" label="规则类型" width="250">
        <template #default="{ row }">
          <el-tag v-for="ruleType in (Array.isArray(row.rule_type) ? row.rule_type : [row.rule_type])"
                  :key="ruleType"
                  type="info"
                  size="small"
                  style="margin-right: 5px;">
            {{ getRuleTypeLabel(ruleType) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="触发条件" width="300">
        <template #default="{ row }">
          <div v-for="ruleType in (Array.isArray(row.rule_type) ? row.rule_type : [row.rule_type])" :key="ruleType">
            <span v-if="!needsOperatorAndThreshold(ruleType)">商品下架时预警</span>
            <span v-else>
              {{ getRuleTypeLabel(ruleType) }}
              {{ getConditionOperator(row, ruleType) }}
              {{ getConditionThreshold(row, ruleType) }}
            </span>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="severity" label="严重级别" width="100">
        <template #default="{ row }">
          <el-tag :type="getSeverityTagType(row.severity)" size="small">
            {{ getSeverityLabel(row.severity) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="notification_method" label="通知方式" width="180">
        <template #default="{ row }">
            <el-tag v-for="method in row.notification_method" :key="method" type="success" size="small" style="margin-right: 5px;">
                {{ getNotificationMethodLabel(method) }}
            </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="100">
        <template #default="{ row }">
          <el-switch 
            v-model="row.status" 
            :active-value="1" 
            :inactive-value="0"
            @change="handleStatusChange(row)" 
          />
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="{ row }">
          <el-button size="small" @click="handleViewDetails(row)" :icon="View">详情</el-button>
          <el-button size="small" type="primary" @click="handleEdit(row)" :icon="Edit">编辑</el-button>
          <el-button size="small" type="danger" @click="handleDelete(row)" :icon="Delete">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container mt-4 flex justify-end">
      <el-pagination
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 编辑/新建对话框 -->
    <el-dialog v-model="dialogVisible" :title="dialogTitle" width="600px" :close-on-click-modal="false">
      <el-form :model="currentRule" ref="ruleForm" label-width="120px" :rules="formRules">
        <el-form-item label="规则名称" prop="name">
          <el-input v-model="currentRule.name" placeholder="请输入规则名称" />
        </el-form-item>
        
        <el-form-item label="规则描述" prop="description">
          <el-input 
            v-model="currentRule.description" 
            type="textarea" 
            :rows="2"
            placeholder="请输入规则描述（可选）"
          />
        </el-form-item>
        
        <el-form-item label="规则类型" prop="rule_type">
          <el-select
            v-model="currentRule.rule_type"
            multiple
            placeholder="请选择规则类型（可多选）"
            style="width: 100%"
            @change="onRuleTypeChange"
          >
            <el-option
              v-for="(label, value) in ruleOptions.rule_types"
              :key="value"
              :label="label"
              :value="value"
            />
          </el-select>
        </el-form-item>

        <!-- 为每个选中的规则类型显示对应的操作符和阈值 -->
        <div v-for="ruleType in currentRule.rule_type" :key="ruleType">
          <template v-if="ruleType === 'promotion_price_deviation' || ruleType === 'channel_price_deviation'">
            <el-divider>{{ ruleOptions.rule_types[ruleType] }} 配置</el-divider>

            <el-form-item :label="`${ruleOptions.rule_types[ruleType]} 操作符`" :prop="`conditions.${ruleType}.operator`">
              <el-select
                v-model="getOrCreateCondition(ruleType).operator"
                placeholder="请选择操作符"
                style="width: 100%"
              >
                <el-option
                  v-for="(label, value) in ruleOptions.operators"
                  :key="value"
                  :label="label"
                  :value="value"
                />
              </el-select>
            </el-form-item>

            <el-form-item :label="`${ruleOptions.rule_types[ruleType]} 阈值`" :prop="`conditions.${ruleType}.threshold`">
              <el-input-number
                v-model="getOrCreateCondition(ruleType).threshold"
                placeholder="请输入阈值"
                :precision="2"
                :min="0"
                :max="100"
                style="width: 100%"
              />
              <small style="color:#909399;">
                <span v-if="ruleType === 'promotion_price_deviation'">促销价偏离率百分比，如10表示10%</span>
                <span v-else-if="ruleType === 'channel_price_deviation'">渠道价格偏离率百分比，如10表示10%</span>
              </small>
            </el-form-item>
          </template>
        </div>

        <!-- 上下架状态预警说明 -->
        <div v-if="currentRule.rule_type && currentRule.rule_type.includes('listing_status_change')">
          <el-alert
            title="上下架状态预警说明"
            type="info"
            :closable="false"
            show-icon
          >
            <p>此预警类型会监控商品的上下架状态变化，当商品下架时自动触发预警。</p>
            <p>多个商品下架会合并为一条预警通知。</p>
          </el-alert>
        </div>

        <el-form-item label="严重级别" prop="severity">
          <el-select v-model="currentRule.severity" placeholder="请选择严重级别" style="width: 100%">
            <el-option
              v-for="(label, value) in ruleOptions.severities"
              :key="value"
              :label="label"
              :value="value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="优先级" prop="priority">
          <el-select v-model="currentRule.priority" placeholder="请选择优先级" style="width: 100%">
            <el-option
              v-for="(label, value) in ruleOptions.priorities"
              :key="value"
              :label="label"
              :value="value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="通知方式" prop="notification_method">
          <el-select v-model="currentRule.notification_method" multiple placeholder="可选择多种通知方式" style="width: 100%">
            <el-option
              v-for="(label, value) in ruleOptions.notification_methods"
              :key="value"
              :label="label"
              :value="value"
            />
          </el-select>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSave" :loading="saveLoading">
            {{ currentRule.id ? '更新' : '创建' }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 详情对话框 -->
    <el-dialog v-model="detailsVisible" title="预警规则详情" width="700px">
      <el-descriptions v-if="selectedRule" :column="2" border>
        <el-descriptions-item label="规则名称">{{ selectedRule.name }}</el-descriptions-item>
        <el-descriptions-item label="规则类型" :span="2">
          <el-tag v-for="ruleType in (Array.isArray(selectedRule.rule_type) ? selectedRule.rule_type : [selectedRule.rule_type])"
                  :key="ruleType"
                  type="info"
                  size="small"
                  style="margin-right: 5px;">
            {{ getRuleTypeLabel(ruleType) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="触发条件" :span="2">
          <div v-for="ruleType in (Array.isArray(selectedRule.rule_type) ? selectedRule.rule_type : [selectedRule.rule_type])" :key="ruleType">
            <span v-if="!needsOperatorAndThreshold(ruleType)">商品下架时预警</span>
            <span v-else>
              {{ getRuleTypeLabel(ruleType) }}
              {{ getConditionOperator(selectedRule, ruleType) }}
              {{ getConditionThreshold(selectedRule, ruleType) }}
            </span>
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="严重级别">
          <el-tag :type="getSeverityTagType(selectedRule.severity)" size="small">
            {{ getSeverityLabel(selectedRule.severity) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="优先级">{{ getPriorityLabel(selectedRule.priority) }}</el-descriptions-item>
        <el-descriptions-item label="通知方式" :span="2">
            <el-tag v-for="method in selectedRule.notification_method" :key="method" type="success" size="small" style="margin-right: 5px;">
                {{ getNotificationMethodLabel(method) }}
            </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="selectedRule.status ? 'success' : 'danger'" size="small">
            {{ selectedRule.status ? '启用' : '禁用' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ formatDate(selectedRule.created_at) }}</el-descriptions-item>
        <el-descriptions-item label="描述" :span="2">{{ selectedRule.description || '暂无描述' }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, getCurrentInstance, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, View, Edit, Delete } from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'
import apiClient from '@/api/apiClient'

// 状态管理
const authStore = useAuthStore()

// 响应式数据
const loading = ref(false)
const saveLoading = ref(false)
const searchKeyword = ref('')
const rules = ref([])
const dialogVisible = ref(false)
const detailsVisible = ref(false)
const selectedRule = ref(null)

// 分页数据
const pagination = reactive({
  currentPage: 1,
  pageSize: 15,
  total: 0
})

// 当前编辑的规则
const currentRule = ref({
  name: '',
  description: '',
  rule_type: [],
  conditions: {
    promotion_price_deviation: { operator: '>', threshold: 10 },
    channel_price_deviation: { operator: '>', threshold: 10 }
  },
  severity: 'medium',
  priority: 'normal',
  notification_method: ['email'],
  status: 1
})

// 选项数据
const ruleOptions = reactive({
  rule_types: {
    'promotion_price_deviation': '促销价偏离率',
    'channel_price_deviation': '渠道价格偏离率',
    'listing_status_change': '上下架状态'
  },
  operators: {
    '>': '大于',
    '<': '小于',
    '>=': '大于等于',
    '<=': '小于等于',
    '=': '等于',
    '!=': '不等于'
  },
  severities: {
    'low': '低',
    'medium': '中',
    'high': '高',
    'critical': '严重'
  },
  priorities: {
    'low': '低',
    'normal': '普通',
    'high': '高',
    'urgent': '紧急'
  },
  notification_methods: {
    'email': '邮件通知',
    'system': '站内信'
  }
})

// 表单验证规则
const formRules = computed(() => {
  const rules = {
    name: [{ required: true, message: '请输入规则名称', trigger: 'blur' }],
    rule_type: [{ type: 'array', required: true, message: '请至少选择一个规则类型', trigger: 'change' }],
    severity: [{ required: true, message: '请选择严重级别', trigger: 'change' }],
    priority: [{ required: true, message: '请选择优先级', trigger: 'change' }],
    notification_method: [{ type: 'array', required: true, message: '请至少选择一个通知方式', trigger: 'change' }]
  };

  // 为每个选中的规则类型添加验证规则
  if (currentRule.value.rule_type && Array.isArray(currentRule.value.rule_type)) {
    currentRule.value.rule_type.forEach(ruleType => {
      if (needsOperatorAndThreshold(ruleType)) {
        rules[`conditions.${ruleType}.operator`] = [{ required: true, message: '请选择操作符', trigger: 'change' }];
        rules[`conditions.${ruleType}.threshold`] = [{ required: true, type: 'number', message: '请输入有效的阈值', trigger: 'blur' }];
      }
    });
  }

  return rules;
});

// 计算属性
const dialogTitle = computed(() => {
  return currentRule.value.id ? '编辑预警规则' : '新建预警规则'
})



const needsOperatorAndThreshold = (ruleType) => {
    return ruleType && ruleType !== 'listing_status_change';
};

// 获取或创建条件对象
const getOrCreateCondition = (ruleType) => {
  if (!currentRule.value.conditions) {
    currentRule.value.conditions = {};
  }

  if (!currentRule.value.conditions[ruleType]) {
    currentRule.value.conditions[ruleType] = {
      operator: '>',
      threshold: 10
    };
  }

  return currentRule.value.conditions[ruleType];
};

// 规则类型变化处理
const onRuleTypeChange = (value) => {
  // 为每个选中的规则类型创建条件对象
  if (Array.isArray(value)) {
    value.forEach(ruleType => {
      if (ruleType === 'promotion_price_deviation' || ruleType === 'channel_price_deviation') {
        getOrCreateCondition(ruleType);
      }
    });
  }
};



// 方法
const fetchRules = async () => {
  try {
    loading.value = true
    const params = {
      page: pagination.currentPage,
      per_page: pagination.pageSize,
      search: searchKeyword.value
    }
    
    const response = await apiClient.get('/alert-rules', { params })
    
    if (response.data.success) {
      rules.value = response.data.data.data
      pagination.total = response.data.data.total
    }
  } catch (error) {
    console.error('获取预警规则失败:', error)
    ElMessage.error('获取预警规则失败')
  } finally {
    loading.value = false
  }
}

const handleCreate = () => {
  currentRule.value = {
    name: '',
    description: '',
    rule_type: [],
    conditions: {
      promotion_price_deviation: { operator: '>', threshold: 10 },
      channel_price_deviation: { operator: '>', threshold: 10 }
    },
    severity: 'medium',
    priority: 'normal',
    notification_method: ['email'],
    status: 1
  };
  dialogVisible.value = true;
}

const handleEdit = (rule) => {
  currentRule.value = { ...rule };

  // 确保rule_type是数组
  if (!Array.isArray(currentRule.value.rule_type)) {
    currentRule.value.rule_type = currentRule.value.rule_type ? [currentRule.value.rule_type] : [];
  }

  // 确保conditions存在
  if (!currentRule.value.conditions) {
    currentRule.value.conditions = {
      promotion_price_deviation: { operator: '>', threshold: 10 },
      channel_price_deviation: { operator: '>', threshold: 10 }
    };
  }

  dialogVisible.value = true;
}

const handleViewDetails = (rule) => {
  selectedRule.value = rule
  detailsVisible.value = true
}

const handleSave = async () => {
  if (!ruleForm.value) return
  await ruleForm.value.validate(async (valid) => {
    if (valid) {
      saveLoading.value = true;
      try {
        const payload = { ...currentRule.value };

        let response;
        if (payload.id) {
          response = await apiClient.put(`/alert-rules/${payload.id}`, payload);
        } else {
          response = await apiClient.post('/alert-rules', payload);
        }

        if (response.data.success) {
          ElMessage.success(payload.id ? '更新成功' : '创建成功');
          dialogVisible.value = false;
          fetchRules();
        }
      } catch (error) {
        console.error('保存失败:', error);
        const errorMessage = error.response?.data?.message || '保存失败';
        ElMessage.error(errorMessage);
      } finally {
        saveLoading.value = false;
      }
    }
  });
}

const handleDelete = async (rule) => {
  try {
    await ElMessageBox.confirm('确定要删除这个预警规则吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const response = await apiClient.delete(`/alert-rules/${rule.id}`)
    
    if (response.data.success) {
      ElMessage.success('删除成功')
      fetchRules()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

const handleStatusChange = async (rule) => {
  try {
    await apiClient.put(`/alert-rules/${rule.id}/status`, { status: rule.status });
    ElMessage.success('状态更新成功');
  } catch (error) {
    console.error('状态更新失败:', error);
    ElMessage.error('状态更新失败');
    // Revert the switch state on error
    rule.status = rule.status === 1 ? 0 : 1;
  }
}

const handleSizeChange = (size) => {
  pagination.pageSize = size
  fetchRules()
}

const handleCurrentChange = (page) => {
  pagination.currentPage = page
  fetchRules()
}

// 格式化方法
const getRuleTypeLabel = (value) => ruleOptions.rule_types[value] || value
const getOperatorLabel = (value) => ruleOptions.operators[value] || value
const getNotificationMethodLabel = (value) => ruleOptions.notification_methods[value] || value;
const getSeverityLabel = (value) => ruleOptions.severities[value] || value
const getPriorityLabel = (value) => ruleOptions.priorities[value] || value

const getSeverityTagType = (severity) => {
  switch (severity) {
    case 'critical': return 'danger'
    case 'high': return 'warning'
    case 'medium': return 'primary'
    case 'low': return 'info'
    default: return 'info'
  }
}

const formatThresholdValue = (rule) => {
  if (!needsOperatorAndThreshold(rule.rule_type)) return '';
  const value = rule.threshold_values?.value ?? 'N/A';
  if (value === 'N/A') return value;
  
  if (rule.rule_type === 'promotion_price_deviation' || rule.rule_type === 'channel_price_deviation') {
    return `${value}%`;
  }
  return value;
}

const formatDate = (dateString) => {
  if (!dateString) return 'N/A';
  return new Date(dateString).toLocaleString('zh-CN');
}

// 获取条件操作符
const getConditionOperator = (rule, ruleType) => {
  if (rule.conditions && rule.conditions[ruleType] && rule.conditions[ruleType].operator) {
    return getOperatorLabel(rule.conditions[ruleType].operator);
  }
  return rule.operator ? getOperatorLabel(rule.operator) : 'N/A';
}

// 获取条件阈值
const getConditionThreshold = (rule, ruleType) => {
  let value = null;

  if (rule.conditions && rule.conditions[ruleType] && rule.conditions[ruleType].threshold !== undefined) {
    value = rule.conditions[ruleType].threshold;
  } else if (rule.threshold_values && rule.threshold_values.value !== undefined) {
    value = rule.threshold_values.value;
  }

  if (value === null || value === undefined) return 'N/A';

  if (ruleType === 'promotion_price_deviation' || ruleType === 'channel_price_deviation') {
    return `${value}%`;
  }
  return value;
}

// 生命周期
onMounted(() => {
  fetchRules()
})
</script>

<style scoped>
.alert-rules-management {
  padding: 20px;
}

.toolbar {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style> 