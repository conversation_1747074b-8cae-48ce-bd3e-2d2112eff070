<template>
  <div class="alert-history">
    <!-- 工具栏 -->
    <div class="toolbar mb-4 flex justify-between items-center">
      <div>
        <el-date-picker
          v-model="filters.dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="YYYY-MM-DD"
          class="w-64 mr-2"
        />
        <el-select v-model="filters.level" placeholder="预警级别" clearable class="w-32 mr-2">
           <el-option label="普通" value="info"></el-option>
           <el-option label="警告" value="warning"></el-option>
           <el-option label="严重" value="error"></el-option>
        </el-select>
        <el-button :icon="Search" @click="fetchAlerts" :loading="loading">搜索</el-button>
      </div>
      <el-button type="danger" :icon="Delete" @click="handleBatchDelete" :disabled="selectedAlerts.length === 0">
        批量删除
      </el-button>
    </div>

    <!-- 预警历史表格 -->
    <el-table :data="alerts" v-loading="loading" style="width: 100%" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" />
      <el-table-column prop="product_name" label="产品名称" width="200">
         <template #default="{ row }">
          <div>{{ row.product_data?.product_name || 'N/A' }}</div>
          <div class="text-xs text-gray-500">{{ row.product_data?.item_id }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="message" label="预警信息" />
      <el-table-column prop="level" label="级别" width="100">
        <template #default="{ row }">
          <el-tag :type="levelTagType(row.level)">{{ row.level }}</el-tag>
        </template>
      </el-table-column>
       <el-table-column prop="triggered_at" label="触发时间" width="180" />
      <el-table-column prop="is_read" label="状态" width="100">
        <template #default="{ row }">
          <el-tag :type="row.is_read ? 'success' : 'warning'">
            {{ row.is_read ? '已读' : '未读' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="180" fixed="right">
        <template #default="{ row }">
          <el-button size="small" type="primary" :icon="View" @click="handleView(row)">详情</el-button>
          <el-button size="small" v-if="!row.is_read" @click="handleMarkAsRead(row)">标为已读</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container mt-4 flex justify-end">
      <el-pagination
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    
     <!-- 详情对话框 -->
    <el-dialog v-model="detailsDialogVisible" title="预警详情" width="50%">
      <div v-if="currentAlert">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="产品名称">{{ currentAlert.product_data?.product_name }}</el-descriptions-item>
          <el-descriptions-item label="产品ID">{{ currentAlert.product_data?.item_id }}</el-descriptions-item>
          <el-descriptions-item label="预警信息">{{ currentAlert.message }}</el-descriptions-item>
          <el-descriptions-item label="预警级别">
            <el-tag :type="levelTagType(currentAlert.level)">{{ currentAlert.level }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="触发时间">{{ currentAlert.triggered_at }}</el-descriptions-item>
          <el-descriptions-item label="规则名称">{{ currentAlert.alert_rule?.name }}</el-descriptions-item>
          <el-descriptions-item label="触发值" :span="2">{{ currentAlert.triggered_value }}</el-descriptions-item>
          <el-descriptions-item label="上下文数据" :span="2">
            <pre>{{ JSON.stringify(currentAlert.context_data, null, 2) }}</pre>
          </el-descriptions-item>
        </el-descriptions>
      </div>
       <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="detailsDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Search, Delete, View } from '@element-plus/icons-vue';
import api from '../api/alerts'; // 假设的API模块

const loading = ref(false);
const alerts = ref([]);
const selectedAlerts = ref([]);
const filters = reactive({
  dateRange: [],
  level: '',
});
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0,
});

const detailsDialogVisible = ref(false);
const currentAlert = ref(null);

const fetchAlerts = async () => {
  loading.value = true;
  try {
    const params = {
      page: pagination.currentPage,
      per_page: pagination.pageSize,
      start_date: filters.dateRange[0] || null,
      end_date: filters.dateRange[1] || null,
      level: filters.level,
    };
    const response = await api.getAlerts(params);
    alerts.value = response.data;
    pagination.total = response.total;
  } catch (error) {
    ElMessage.error('获取预警历史失败');
  } finally {
    loading.value = false;
  }
};

const handleView = async (row) => {
    const alertDetails = await api.getAlert(row.id);
    currentAlert.value = alertDetails.data;
    detailsDialogVisible.value = true;
    if (!row.is_read) {
        handleMarkAsRead(row, true); // 静默标记为已读
    }
}

const handleMarkAsRead = async (row, silent = false) => {
    try {
        await api.markAlertAsRead(row.id);
        row.is_read = true;
        if (!silent) ElMessage.success('已标记为已读');
    } catch (error) {
        if (!silent) ElMessage.error('操作失败');
    }
}

const handleSelectionChange = (val) => {
  selectedAlerts.value = val;
};

const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(`确定要删除选中的 ${selectedAlerts.value.length} 条预警吗?`, '提示', {
      type: 'warning',
    });
    const ids = selectedAlerts.value.map(alert => alert.id);
    await api.batchDeleteAlerts({ ids });
    ElMessage.success('批量删除成功');
    fetchAlerts();
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败');
    }
  }
};

const handleSizeChange = (val) => {
  pagination.pageSize = val;
  fetchAlerts();
};

const handleCurrentChange = (val) => {
  pagination.currentPage = val;
  fetchAlerts();
};

const levelTagType = (level) => {
  if (level === 'error') return 'danger';
  if (level === 'warning') return 'warning';
  return 'info';
};

onMounted(() => {
  fetchAlerts();
});
</script>

<style scoped>
pre {
    background-color: #f5f5f5;
    padding: 10px;
    border-radius: 4px;
    white-space: pre-wrap;
    word-wrap: break-word;
}
</style> 