import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'

// 角色接口
export interface Role {
  id: number
  name: string
  display_name: string
  description?: string
}

// 用户信息接口
export interface User {
  id: number
  username: string
  name: string
  email: string
  real_name?: string
  status: number
  created_at: string
  updated_at: string
  roles?: Role[]
}

// 登录响应接口
export interface LoginResponse {
  success: boolean
  data: {
    user: User
    token: string
  }
  message: string
}

// API基础URL
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const user = ref<User | null>(null)
  const token = ref<string | null>(localStorage.getItem('auth_token'))
  const isLoading = ref(false)

  // 计算属性
  const isAuthenticated = computed(() => !!token.value && !!user.value)
  const isAdmin = computed(() => {
    if (!user.value || !user.value.roles) {
      return false
    }
    // 检查用户角色数组中是否有名为 'admin' 或 'super_admin' 的角色
    return user.value.roles.some(role => role.name === 'admin' || role.name === 'super_admin')
  })

  // HTTP请求工具函数
  const makeRequest = async (endpoint: string, options: RequestInit = {}) => {
    const url = `${API_BASE_URL}${endpoint}`
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      ...(options.headers as Record<string, string>),
    }

    if (token.value) {
      headers['Authorization'] = `Bearer ${token.value}`
    }

    try {
      const response = await fetch(url, {
        ...options,
        headers,
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      return await response.json()
    } catch (error) {
      console.error('API请求失败:', error)
      throw error
    }
  }

  // 登录
  const login = async (loginData: string, password: string): Promise<boolean> => {
    isLoading.value = true
    try {
      const response: LoginResponse = await makeRequest('/auth/login', {
        method: 'POST',
        body: JSON.stringify({ login: loginData, password }),
      })

      if (response.success) {
        user.value = response.data.user
        token.value = response.data.token
        localStorage.setItem('auth_token', response.data.token)
        ElMessage.success(response.message || '登录成功')
        return true
      } else {
        ElMessage.error(response.message || '登录失败')
        return false
      }
    } catch (error) {
      console.error('登录失败:', error)
      ElMessage.error('登录失败，请检查网络连接')
      return false
    } finally {
      isLoading.value = false
    }
  }

  // 登出
  const logout = async (): Promise<void> => {
    isLoading.value = true
    try {
      if (token.value) {
        await makeRequest('/auth/logout', {
          method: 'POST',
        })
      }
    } catch (error) {
      console.error('登出请求失败:', error)
    } finally {
      // 无论API调用是否成功，都清除本地状态
      user.value = null
      token.value = null
      localStorage.removeItem('auth_token')
      isLoading.value = false
      ElMessage.success('已退出登录')
    }
  }

  // 获取用户信息
  const fetchUser = async (): Promise<boolean> => {
    if (!token.value) {
      return false
    }

    try {
      const response = await makeRequest('/auth/me')
      if (response.success) {
        user.value = response.data.user
        return true
      } else {
        // token可能已过期
        await logout()
        return false
      }
    } catch (error) {
      console.error('获取用户信息失败:', error)
      await logout()
      return false
    }
  }

  // 初始化认证状态
  const initAuth = async (): Promise<void> => {
    if (token.value) {
      await fetchUser()
    }
  }

  // 检查token是否有效
  const checkAuth = async (): Promise<boolean> => {
    if (!token.value) {
      return false
    }
    return await fetchUser()
  }

  // 通用请求方法（对外暴露）
  const request = async (endpoint: string, options: RequestInit = {}) => {
    return await makeRequest(endpoint, options)
  }

  return {
    // 状态
    user,
    token,
    isLoading,
    // 计算属性
    isAuthenticated,
    isAdmin,
    // 方法
    login,
    logout,
    fetchUser,
    initAuth,
    checkAuth,
    makeRequest,
    request,
  }
}) 
