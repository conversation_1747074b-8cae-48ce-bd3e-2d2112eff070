<template>
  <div class="user-management">
    <div class="page-header">
      <h1>用户管理</h1>
      <p>管理系统用户账号和基本信息</p>
    </div>

    <!-- 操作工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-button type="primary" :icon="Plus" @click="showCreateDialog = true">
          添加用户
        </el-button>
        <el-button type="danger" :disabled="selectedUsers.length === 0" @click="handleBatchDelete">
          批量删除
        </el-button>
      </div>
      <div class="toolbar-right">
        <el-input
          v-model="searchQuery"
          placeholder="搜索用户名或邮箱"
          :prefix-icon="Search"
          style="width: 300px"
          @input="handleSearch"
          clearable
        />
      </div>
    </div>

    <!-- 用户列表 -->
    <el-card class="table-card">
      <el-table
        :data="filteredUsers"
        v-loading="loading"
        @selection-change="handleSelectionChange"
        stripe
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="avatar" label="头像" width="80">
          <template #default="{ row }">
            <el-avatar :size="40" :src="row.avatar">
              <el-icon><User /></el-icon>
            </el-avatar>
          </template>
        </el-table-column>
        <el-table-column prop="username" label="用户名" sortable />
        <el-table-column prop="name" label="姓名" sortable />
        <el-table-column prop="email" label="邮箱" sortable />
        <el-table-column prop="roles" label="角色" width="150">
          <template #default="{ row }">
            <el-tag
              v-for="role in row.roles"
              :key="role.id"
              :type="role.name === 'admin' ? 'danger' : 'primary'"
              size="small"
              style="margin-right: 4px"
            >
              {{ role.display_name }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status === 'active' ? 'success' : 'danger'" size="small">
              {{ row.status === 'active' ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180" sortable>
          <template #default="{ row }">
            {{ new Date(row.created_at).toLocaleString() }}
          </template>
        </el-table-column>
        <el-table-column prop="last_login" label="最后登录" width="180">
          <template #default="{ row }">
            {{ row.last_login ? new Date(row.last_login).toLocaleString() : '从未登录' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="editUser(row)">编辑</el-button>
            <el-button size="small" @click="manageRoles(row)">角色</el-button>
            <el-button
              size="small"
              :type="row.status === 'active' ? 'warning' : 'success'"
              @click="toggleUserStatus(row)"
            >
              {{ row.status === 'active' ? '禁用' : '启用' }}
            </el-button>
            <el-button size="small" type="danger" @click="deleteUser(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="totalUsers"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 创建/编辑用户对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editingUser ? '编辑用户' : '添加用户'"
      width="600px"
      @close="resetForm"
    >
      <el-form
        ref="userFormRef"
        :model="userForm"
        :rules="userFormRules"
        label-width="100px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input v-model="userForm.username" placeholder="请输入用户名" />
        </el-form-item>
        <el-form-item label="姓名" prop="name">
          <el-input v-model="userForm.name" placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="userForm.email" placeholder="请输入邮箱" />
        </el-form-item>
        <el-form-item label="密码" prop="password" v-if="!editingUser">
          <el-input
            v-model="userForm.password"
            type="password"
            placeholder="请输入密码"
            show-password
          />
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword" v-if="!editingUser">
          <el-input
            v-model="userForm.confirmPassword"
            type="password"
            placeholder="请确认密码"
            show-password
          />
        </el-form-item>
        <el-form-item label="角色" prop="roleIds">
          <el-select v-model="userForm.roleIds" multiple placeholder="请选择角色" style="width: 100%">
            <el-option
              v-for="role in availableRoles"
              :key="role.id"
              :label="role.display_name"
              :value="role.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="userForm.status">
            <el-radio value="active">启用</el-radio>
            <el-radio value="inactive">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="saveUser" :loading="saving">
          {{ editingUser ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 角色管理对话框 -->
    <el-dialog v-model="showRoleDialog" title="管理用户角色" width="500px">
      <div class="role-management">
        <p>为用户 <strong>{{ selectedUser?.name }}</strong> 分配角色：</p>
        <el-checkbox-group v-model="selectedRoleIds" style="margin-top: 20px">
          <el-checkbox
            v-for="role in availableRoles"
            :key="role.id"
            :label="role.id"
            style="display: block; margin-bottom: 10px"
          >
            <div class="role-item">
              <strong>{{ role.display_name }}</strong>
              <span class="role-desc">{{ role.description }}</span>
            </div>
          </el-checkbox>
        </el-checkbox-group>
      </div>
      <template #footer>
        <el-button @click="showRoleDialog = false">取消</el-button>
        <el-button type="primary" @click="saveUserRoles" :loading="saving">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, User } from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const searchQuery = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const totalUsers = ref(0)
const selectedUsers = ref([])

// 对话框状态
const showCreateDialog = ref(false)
const showRoleDialog = ref(false)
const editingUser = ref(null)
const selectedUser = ref(null)

// 表单数据
const userForm = ref({
  username: '',
  name: '',
  email: '',
  password: '',
  confirmPassword: '',
  roleIds: [],
  status: 'active'
})

const selectedRoleIds = ref([])

// 表单验证规则
const userFormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== userForm.value.password) {
          callback(new Error('两次输入密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 模拟数据
const users = ref([
  {
    id: 1,
    username: 'admin',
    name: '系统管理员',
    email: '<EMAIL>',
    avatar: '',
    roles: [{ id: 1, name: 'admin', display_name: '管理员' }],
    status: 'active',
    created_at: '2024-01-01T00:00:00Z',
    last_login: '2024-01-15T10:30:00Z'
  },
  {
    id: 2,
    username: 'user1',
    name: '普通用户',
    email: '<EMAIL>',
    avatar: '',
    roles: [{ id: 2, name: 'user', display_name: '普通用户' }],
    status: 'active',
    created_at: '2024-01-02T00:00:00Z',
    last_login: '2024-01-14T15:20:00Z'
  }
])

const availableRoles = ref([
  { id: 1, name: 'admin', display_name: '管理员', description: '拥有系统所有权限' },
  { id: 2, name: 'user', display_name: '普通用户', description: '只能使用基本功能' }
])

// 计算属性
const filteredUsers = computed(() => {
  if (!searchQuery.value) return users.value
  return users.value.filter(user => 
    user.username.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
    user.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
    user.email.toLowerCase().includes(searchQuery.value.toLowerCase())
  )
})

// 方法
const handleSearch = () => {
  currentPage.value = 1
}

const handleSelectionChange = (selection) => {
  selectedUsers.value = selection
}

const handleSizeChange = (size) => {
  pageSize.value = size
  loadUsers()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  loadUsers()
}

const editUser = (user) => {
  editingUser.value = user
  userForm.value = {
    username: user.username,
    name: user.name,
    email: user.email,
    password: '',
    confirmPassword: '',
    roleIds: user.roles.map(role => role.id),
    status: user.status
  }
  showCreateDialog.value = true
}

const manageRoles = (user) => {
  selectedUser.value = user
  selectedRoleIds.value = user.roles.map(role => role.id)
  showRoleDialog.value = true
}

const toggleUserStatus = async (user) => {
  try {
    // TODO: 调用API切换用户状态
    user.status = user.status === 'active' ? 'inactive' : 'active'
    ElMessage.success(`用户状态已${user.status === 'active' ? '启用' : '禁用'}`)
  } catch (error) {
    ElMessage.error('操作失败')
  }
}

const deleteUser = async (user) => {
  try {
    await ElMessageBox.confirm(`确定要删除用户 "${user.name}" 吗？`, '确认删除', {
      type: 'warning'
    })
    
    // TODO: 调用API删除用户
    const index = users.value.findIndex(u => u.id === user.id)
    if (index > -1) {
      users.value.splice(index, 1)
    }
    
    ElMessage.success('删除成功')
  } catch (error) {
    // 用户取消
  }
}

const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(`确定要删除选中的 ${selectedUsers.value.length} 个用户吗？`, '确认批量删除', {
      type: 'warning'
    })
    
    // TODO: 调用API批量删除用户
    const idsToDelete = selectedUsers.value.map(user => user.id)
    users.value = users.value.filter(user => !idsToDelete.includes(user.id))
    
    ElMessage.success('批量删除成功')
  } catch (error) {
    // 用户取消
  }
}

const saveUser = async () => {
  // TODO: 表单验证和API调用
  saving.value = true
  try {
    if (editingUser.value) {
      // 更新用户
      const index = users.value.findIndex(u => u.id === editingUser.value.id)
      if (index > -1) {
        users.value[index] = {
          ...users.value[index],
          ...userForm.value,
          roles: availableRoles.value.filter(role => userForm.value.roleIds.includes(role.id))
        }
      }
      ElMessage.success('用户更新成功')
    } else {
      // 创建用户
      const newUser = {
        id: Date.now(),
        ...userForm.value,
        roles: availableRoles.value.filter(role => userForm.value.roleIds.includes(role.id)),
        created_at: new Date().toISOString(),
        last_login: null
      }
      users.value.push(newUser)
      ElMessage.success('用户创建成功')
    }
    showCreateDialog.value = false
    resetForm()
  } catch (error) {
    ElMessage.error('操作失败')
  } finally {
    saving.value = false
  }
}

const saveUserRoles = async () => {
  saving.value = true
  try {
    // TODO: 调用API更新用户角色
    const userIndex = users.value.findIndex(u => u.id === selectedUser.value.id)
    if (userIndex > -1) {
      users.value[userIndex].roles = availableRoles.value.filter(role => 
        selectedRoleIds.value.includes(role.id)
      )
    }
    
    ElMessage.success('角色更新成功')
    showRoleDialog.value = false
  } catch (error) {
    ElMessage.error('操作失败')
  } finally {
    saving.value = false
  }
}

const resetForm = () => {
  userForm.value = {
    username: '',
    name: '',
    email: '',
    password: '',
    confirmPassword: '',
    roleIds: [],
    status: 'active'
  }
  editingUser.value = null
}

const loadUsers = async () => {
  loading.value = true
  try {
    // TODO: 调用API加载用户列表
    totalUsers.value = users.value.length
  } catch (error) {
    ElMessage.error('加载用户列表失败')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadUsers()
})
</script>

<style scoped lang="scss">
.user-management {
  .page-header {
    margin-bottom: 24px;
    
    h1 {
      font-size: 24px;
      font-weight: 600;
      margin-bottom: 8px;
      color: #303133;
    }
    
    p {
      color: #606266;
      margin: 0;
    }
  }
  
  .toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    .toolbar-left {
      display: flex;
      gap: 12px;
    }
  }
  
  .table-card {
    .pagination-wrapper {
      display: flex;
      justify-content: flex-end;
      margin-top: 16px;
    }
  }
  
  .role-management {
    .role-item {
      display: flex;
      flex-direction: column;
      
      .role-desc {
        font-size: 12px;
        color: #909399;
        margin-top: 2px;
      }
    }
  }
}
</style> 