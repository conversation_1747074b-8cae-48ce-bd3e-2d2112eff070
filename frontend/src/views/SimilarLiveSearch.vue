<template>
  <div class="similar-live-search">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <div class="header-icon">
          <el-icon size="32" color="#67C23A"><Search /></el-icon>
        </div>
        <div class="header-text">
          <h1 class="page-title">相似同款查询 - 实时搜索</h1>
          <p class="page-description">智能搜索同款商品，支持多种搜索方式和高级设置</p>
        </div>
      </div>
      <div class="header-stats">
        <div class="stat-item">
          <div class="stat-value">{{ searchStats.totalSearches }}</div>
          <div class="stat-label">今日搜索</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ searchStats.avgResults }}</div>
          <div class="stat-label">平均结果</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ searchStats.successRate }}%</div>
          <div class="stat-label">成功率</div>
        </div>
      </div>
    </div>

    <!-- 搜索表单 -->
    <div class="search-section">
      <el-card class="search-card" shadow="never">
        <template #header>
          <div class="search-header">
            <div class="search-title">
              <el-icon size="20"><Lightning /></el-icon>
              <span>智能搜索</span>
            </div>
            <div class="search-modes">
              <el-radio-group v-model="searchMode" size="small">
                          <el-radio-button value="text">文本搜索</el-radio-button>
          <el-radio-button value="image">图片搜索</el-radio-button>
          <el-radio-button value="url">链接搜索</el-radio-button>
              </el-radio-group>
            </div>
          </div>
        </template>

        <div class="search-form">
          <!-- 文本搜索 -->
          <div v-if="searchMode === 'text'" class="search-input-section">
            <div class="input-group">
              <el-input
                v-model="searchForm.keyword"
                placeholder="请输入商品标题关键词..."
                size="large"
                clearable
                @keyup.enter="handleSearch"
                class="search-input"
              >
                <template #prepend>
                  <el-icon><EditPen /></el-icon>
                </template>
                <template #append>
                  <el-button 
                    type="primary" 
                    :loading="searching" 
                    @click="handleSearch"
                    :disabled="!searchForm.keyword.trim()"
                  >
                    <el-icon><Search /></el-icon>
                    搜索
                  </el-button>
                </template>
              </el-input>
            </div>
            <div class="search-tips">
              <el-tag size="small" type="info">提示：</el-tag>
              <span>支持商品标题、品牌名称、型号等关键词搜索</span>
            </div>
          </div>

          <!-- 图片搜索 -->
          <div v-if="searchMode === 'image'" class="search-input-section">
            <div class="upload-area">
              <el-upload
                class="image-uploader"
                :action="uploadUrl"
                :show-file-list="false"
                :before-upload="beforeImageUpload"
                :on-success="handleImageUploadSuccess"
                :on-error="handleImageUploadError"
                accept="image/*"
                drag
              >
                <div v-if="!searchForm.imageUrl" class="upload-placeholder">
                  <el-icon size="48" color="#409EFF"><Picture /></el-icon>
                  <div class="upload-text">
                    <p>点击或拖拽图片到此处上传</p>
                    <p class="upload-hint">支持 JPG、PNG、WEBP 格式，大小不超过 5MB</p>
                  </div>
                </div>
                <div v-else class="uploaded-image">
                  <img :src="searchForm.imageUrl" alt="搜索图片" />
                  <div class="image-actions">
                    <el-button size="small" @click.stop="removeImage">重新上传</el-button>
                    <el-button 
                      size="small" 
                      type="primary" 
                      :loading="searching" 
                      @click.stop="handleSearch"
                    >
                      以图搜图
                    </el-button>
                  </div>
                </div>
              </el-upload>
            </div>
            <div class="search-tips">
              <el-tag size="small" type="info">提示：</el-tag>
              <span>上传商品图片，系统将自动识别并搜索相似商品</span>
            </div>
          </div>

          <!-- 链接搜索 -->
          <div v-if="searchMode === 'url'" class="search-input-section">
            <div class="input-group">
              <el-input
                v-model="searchForm.productUrl"
                placeholder="请输入商品详情页链接..."
                size="large"
                clearable
                @keyup.enter="handleSearch"
                class="search-input"
              >
                <template #prepend>
                  <el-icon><Link /></el-icon>
                </template>
                <template #append>
                  <el-button 
                    type="primary" 
                    :loading="searching" 
                    @click="handleSearch"
                    :disabled="!isValidUrl(searchForm.productUrl)"
                  >
                    <el-icon><Search /></el-icon>
                    搜索
                  </el-button>
                </template>
              </el-input>
            </div>
            <div class="search-tips">
              <el-tag size="small" type="info">提示：</el-tag>
              <span>支持淘宝、京东、天猫等主流电商平台商品链接</span>
            </div>
          </div>

          <!-- 高级设置 -->
          <div class="advanced-settings">
            <el-collapse v-model="activeAdvanced">
              <el-collapse-item title="高级设置" name="advanced">
                <div class="advanced-content">
                  <el-row :gutter="24">
                    <el-col :span="8">
                      <div class="setting-item">
                        <label>搜索平台</label>
                        <el-select v-model="searchForm.platforms" multiple placeholder="选择搜索平台" style="width: 100%">
                          <el-option label="淘宝" value="taobao" />
                          <el-option label="京东" value="jd" />
                          <el-option label="天猫" value="tmall" />
                          <el-option label="拼多多" value="pdd" />
                        </el-select>
                      </div>
                    </el-col>
                    <el-col :span="8">
                      <div class="setting-item">
                        <label>价格范围</label>
                        <div class="price-range">
                          <el-input-number v-model="searchForm.priceMin" :min="0" placeholder="最低价" />
                          <span class="range-separator">-</span>
                          <el-input-number v-model="searchForm.priceMax" :min="0" placeholder="最高价" />
                        </div>
                      </div>
                    </el-col>
                    <el-col :span="8">
                      <div class="setting-item">
                        <label>相似度阈值</label>
                        <el-slider 
                          v-model="searchForm.similarity" 
                          :min="0" 
                          :max="100" 
                          show-tooltip
                          :format-tooltip="(val) => `${val}%`"
                        />
                      </div>
                    </el-col>
                  </el-row>
                </div>
              </el-collapse-item>
            </el-collapse>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 搜索结果 -->
    <div v-if="searchResults.length > 0 || searching" class="results-section">
      <el-card class="results-card" shadow="never">
        <template #header>
          <div class="results-header">
            <div class="results-title">
              <el-icon size="20"><DataBoard /></el-icon>
              <span>搜索结果</span>
              <el-tag v-if="!searching" type="primary" size="small">
                共找到 {{ totalResults }} 个相似商品
              </el-tag>
            </div>
            <div class="results-actions">
              <el-button-group>
                <el-button 
                  :type="viewMode === 'grid' ? 'primary' : 'default'" 
                  :icon="Grid"
                  @click="viewMode = 'grid'"
                  size="small"
                />
                <el-button 
                  :type="viewMode === 'list' ? 'primary' : 'default'" 
                  :icon="List"
                  @click="viewMode = 'list'"
                  size="small"
                />
              </el-button-group>
              <el-button :icon="Download" @click="exportResults" size="small">导出</el-button>
            </div>
          </div>
        </template>

        <!-- 加载状态 -->
        <div v-if="searching" class="loading-state">
          <el-skeleton :rows="4" animated />
          <div class="loading-text">
            <el-icon class="is-loading"><Loading /></el-icon>
            <span>正在搜索相似商品...</span>
          </div>
        </div>

        <!-- 网格视图 -->
        <div v-else-if="viewMode === 'grid'" class="results-grid">
          <div 
            v-for="(item, index) in searchResults" 
            :key="index" 
            class="result-card"
            @click="viewProductDetail(item)"
          >
            <div class="product-image">
              <img :src="item.image" :alt="item.title" />
              <div class="similarity-badge">
                <span>{{ item.similarity }}%</span>
              </div>
            </div>
            <div class="product-info">
              <h3 class="product-title">{{ item.title }}</h3>
              <div class="product-meta">
                <div class="price-info">
                  <span class="current-price">¥{{ item.price }}</span>
                  <span v-if="item.originalPrice && item.originalPrice > item.price" class="original-price">
                    ¥{{ item.originalPrice }}
                  </span>
                </div>
                <div class="platform-info">
                  <el-tag :type="getPlatformTagType(item.platform)" size="small">
                    {{ getPlatformName(item.platform) }}
                  </el-tag>
                </div>
              </div>
              <div class="shop-info">
                <el-icon size="14"><Shop /></el-icon>
                <span>{{ item.shopName }}</span>
              </div>
              <div class="product-actions">
                <el-button size="small" type="primary" @click.stop="viewProductDetail(item)">
                  查看详情
                </el-button>
                <el-button size="small" @click.stop="addToMonitoring(item)">
                  加入监控
                </el-button>
              </div>
            </div>
          </div>
        </div>

        <!-- 列表视图 -->
        <div v-else class="results-list">
          <el-table :data="searchResults" style="width: 100%">
            <el-table-column label="商品信息" min-width="300">
              <template #default="{ row }">
                <div class="product-summary">
                  <img :src="row.image" :alt="row.title" class="product-thumb" />
                  <div class="product-details">
                    <h4 class="product-title">{{ row.title }}</h4>
                    <div class="product-meta">
                      <el-tag :type="getPlatformTagType(row.platform)" size="small">
                        {{ getPlatformName(row.platform) }}
                      </el-tag>
                      <span class="shop-name">{{ row.shopName }}</span>
                    </div>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="价格" width="150" align="center">
              <template #default="{ row }">
                <div class="price-cell">
                  <div class="current-price">¥{{ row.price }}</div>
                  <div v-if="row.originalPrice && row.originalPrice > row.price" class="original-price">
                    ¥{{ row.originalPrice }}
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="相似度" width="120" align="center">
              <template #default="{ row }">
                <div class="similarity-cell">
                  <el-progress 
                    :percentage="row.similarity" 
                    :color="getSimilarityColor(row.similarity)"
                    :stroke-width="8"
                  />
                  <span class="similarity-text">{{ row.similarity }}%</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200" align="center">
              <template #default="{ row }">
                <div class="action-buttons">
                  <el-button size="small" type="primary" @click="viewProductDetail(row)">
                    查看详情
                  </el-button>
                  <el-button size="small" @click="addToMonitoring(row)">
                    加入监控
                  </el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 分页 -->
        <div v-if="totalResults > pageSize" class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[12, 24, 48, 96]"
            :total="totalResults"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            background
          />
        </div>
      </el-card>
    </div>

    <!-- 空状态 -->
    <div v-if="!searching && searchResults.length === 0 && hasSearched" class="empty-state">
      <el-empty description="未找到相似商品">
        <el-button type="primary" @click="resetSearch">重新搜索</el-button>
      </el-empty>
    </div>

    <!-- 商品详情对话框 -->
    <el-dialog
      v-model="showProductDetail"
      title="商品详情"
      width="800px"
      :close-on-click-modal="false"
    >
      <div v-if="selectedProduct" class="product-detail">
        <div class="detail-header">
          <img :src="selectedProduct.image" :alt="selectedProduct.title" class="detail-image" />
          <div class="detail-info">
            <h2>{{ selectedProduct.title }}</h2>
            <div class="detail-meta">
              <div class="price-section">
                <span class="current-price">¥{{ selectedProduct.price }}</span>
                <span v-if="selectedProduct.originalPrice" class="original-price">
                  ¥{{ selectedProduct.originalPrice }}
                </span>
              </div>
              <div class="platform-section">
                <el-tag :type="getPlatformTagType(selectedProduct.platform)">
                  {{ getPlatformName(selectedProduct.platform) }}
                </el-tag>
              </div>
            </div>
            <div class="shop-section">
              <el-icon><Shop /></el-icon>
              <span>{{ selectedProduct.shopName }}</span>
            </div>
            <div class="similarity-section">
              <span>相似度：</span>
              <el-progress 
                :percentage="selectedProduct.similarity" 
                :color="getSimilarityColor(selectedProduct.similarity)"
                :stroke-width="6"
                style="width: 200px;"
              />
              <span>{{ selectedProduct.similarity }}%</span>
            </div>
          </div>
        </div>
        <div class="detail-actions">
          <el-button type="primary" @click="openProductUrl(selectedProduct)">
            <el-icon><Link /></el-icon>
            查看原商品
          </el-button>
          <el-button @click="addToMonitoring(selectedProduct)">
            <el-icon><Plus /></el-icon>
            加入监控
          </el-button>
        </div>
      </div>

      <template #footer>
        <el-button @click="showProductDetail = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  Lightning,
  EditPen,
  Picture,
  Link,
  DataBoard,
  Grid,
  List,
  Download,
  Loading,
  Shop,
  Plus
} from '@element-plus/icons-vue'

// 响应式数据
const searchMode = ref('text')
const searching = ref(false)
const hasSearched = ref(false)
const viewMode = ref('grid')
const activeAdvanced = ref([])
const showProductDetail = ref(false)
const selectedProduct = ref(null)

// 搜索表单
const searchForm = reactive({
  keyword: '',
  imageUrl: '',
  productUrl: '',
  platforms: ['taobao', 'jd', 'tmall'],
  priceMin: null,
  priceMax: null,
  similarity: 80
})

// 搜索统计
const searchStats = reactive({
  totalSearches: 156,
  avgResults: 23,
  successRate: 94
})

// 搜索结果
const searchResults = ref([])
const totalResults = ref(0)
const currentPage = ref(1)
const pageSize = ref(12)

// 上传配置
const uploadUrl = '/api/upload/image'

// 计算属性
const isValidUrl = (url: string) => {
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}

// 方法
const handleSearch = async () => {
  if (searching.value) return
  
  let searchData = {}
  
  switch (searchMode.value) {
    case 'text':
      if (!searchForm.keyword.trim()) {
        ElMessage.warning('请输入搜索关键词')
        return
      }
      searchData = { keyword: searchForm.keyword }
      break
    case 'image':
      if (!searchForm.imageUrl) {
        ElMessage.warning('请上传搜索图片')
        return
      }
      searchData = { imageUrl: searchForm.imageUrl }
      break
    case 'url':
      if (!isValidUrl(searchForm.productUrl)) {
        ElMessage.warning('请输入有效的商品链接')
        return
      }
      searchData = { productUrl: searchForm.productUrl }
      break
  }

  searching.value = true
  hasSearched.value = true
  
  try {
    // TODO: 调用搜索API
    // const response = await searchSimilarProducts({
    //   ...searchData,
    //   platforms: searchForm.platforms,
    //   priceRange: [searchForm.priceMin, searchForm.priceMax],
    //   similarity: searchForm.similarity,
    //   page: currentPage.value,
    //   pageSize: pageSize.value
    // })
    
    // 模拟数据
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    const mockResults = [
      {
        id: '1',
        title: 'iPhone 14 Pro Max 256GB 深空黑色 5G手机',
        image: 'https://via.placeholder.com/200x200',
        price: 8999,
        originalPrice: 9999,
        platform: 'taobao',
        shopName: '苹果官方旗舰店',
        similarity: 95,
        url: 'https://item.taobao.com/item.htm?id=123456789'
      },
      {
        id: '2',
        title: 'Apple iPhone 14 Pro Max 256G 深空黑',
        image: 'https://via.placeholder.com/200x200',
        price: 8899,
        originalPrice: null,
        platform: 'jd',
        shopName: '京东自营',
        similarity: 92,
        url: 'https://item.jd.com/123456.html'
      }
      // ... 更多模拟数据
    ]
    
    searchResults.value = mockResults
    totalResults.value = mockResults.length
    
    ElMessage.success(`搜索完成，找到 ${mockResults.length} 个相似商品`)
  } catch (error) {
    ElMessage.error('搜索失败，请重试')
    console.error('Search error:', error)
  } finally {
    searching.value = false
  }
}

const beforeImageUpload = (file: File) => {
  const isValidType = ['image/jpeg', 'image/png', 'image/webp'].includes(file.type)
  const isLt5M = file.size / 1024 / 1024 < 5

  if (!isValidType) {
    ElMessage.error('只支持 JPG、PNG、WEBP 格式的图片')
    return false
  }
  if (!isLt5M) {
    ElMessage.error('图片大小不能超过 5MB')
    return false
  }
  return true
}

const handleImageUploadSuccess = (response: any) => {
  searchForm.imageUrl = response.url
  ElMessage.success('图片上传成功')
}

const handleImageUploadError = () => {
  ElMessage.error('图片上传失败')
}

const removeImage = () => {
  searchForm.imageUrl = ''
}

const getPlatformTagType = (platform: string) => {
  const typeMap: Record<string, string> = {
    taobao: 'warning',
    jd: 'danger',
    tmall: 'success',
    pdd: 'info'
  }
  return typeMap[platform] || 'default'
}

const getPlatformName = (platform: string) => {
  const nameMap: Record<string, string> = {
    taobao: '淘宝',
    jd: '京东',
    tmall: '天猫',
    pdd: '拼多多'
  }
  return nameMap[platform] || platform
}

const getSimilarityColor = (similarity: number) => {
  if (similarity >= 90) return '#67C23A'
  if (similarity >= 80) return '#E6A23C'
  if (similarity >= 70) return '#F56C6C'
  return '#909399'
}

const viewProductDetail = (product: any) => {
  selectedProduct.value = product
  showProductDetail.value = true
}

const addToMonitoring = (product: any) => {
  ElMessage.success(`已将商品"${product.title}"加入监控列表`)
}

const openProductUrl = (product: any) => {
  window.open(product.url, '_blank')
}

const exportResults = () => {
  ElMessage.success('导出功能开发中...')
}

const resetSearch = () => {
  searchForm.keyword = ''
  searchForm.imageUrl = ''
  searchForm.productUrl = ''
  searchResults.value = []
  hasSearched.value = false
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  handleSearch()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  handleSearch()
}

onMounted(() => {
  // 初始化
})
</script>

<style scoped>
.similar-live-search {
  min-height: 100%;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding: 24px 32px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-icon {
  width: 64px;
  height: 64px;
  background: linear-gradient(135deg, #67C23A, #85ce61);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 16px rgba(103, 194, 58, 0.3);
}

.header-text {
  flex: 1;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 8px 0;
  background: linear-gradient(135deg, #2c3e50, #34495e);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-description {
  font-size: 16px;
  color: #7f8c8d;
  margin: 0;
  line-height: 1.5;
}

.header-stats {
  display: flex;
  gap: 32px;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: #2c3e50;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #7f8c8d;
  font-weight: 500;
}

.search-section {
  margin-bottom: 32px;
}

.search-card {
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: none;
}

.search-card :deep(.el-card__header) {
  padding: 24px 32px;
  border-bottom: 1px solid #f0f0f0;
}

.search-card :deep(.el-card__body) {
  padding: 32px;
}

.search-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.search-input-section {
  margin-bottom: 24px;
}

.input-group {
  margin-bottom: 12px;
}

.search-input {
  width: 100%;
}

.search-tips {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #7f8c8d;
  font-size: 14px;
}

.upload-area {
  margin-bottom: 12px;
}

.image-uploader {
  width: 100%;
}

.image-uploader :deep(.el-upload) {
  width: 100%;
}

.image-uploader :deep(.el-upload-dragger) {
  width: 100%;
  height: 200px;
  border-radius: 12px;
  border: 2px dashed #d9d9d9;
  transition: all 0.3s ease;
}

.image-uploader :deep(.el-upload-dragger:hover) {
  border-color: #409EFF;
  background-color: #f5f7fa;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
}

.upload-text p {
  margin: 8px 0;
  color: #606266;
}

.upload-hint {
  font-size: 12px;
  color: #909399;
}

.uploaded-image {
  position: relative;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.uploaded-image img {
  max-width: 100%;
  max-height: 180px;
  border-radius: 8px;
}

.image-actions {
  position: absolute;
  bottom: 10px;
  right: 10px;
  display: flex;
  gap: 8px;
}

.advanced-settings {
  margin-top: 24px;
}

.advanced-content {
  padding: 20px 0;
}

.setting-item {
  margin-bottom: 16px;
}

.setting-item label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #606266;
}

.price-range {
  display: flex;
  align-items: center;
  gap: 8px;
}

.range-separator {
  color: #909399;
}

.results-section {
  margin-bottom: 32px;
}

.results-card {
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: none;
}

.results-card :deep(.el-card__header) {
  padding: 24px 32px;
  border-bottom: 1px solid #f0f0f0;
}

.results-card :deep(.el-card__body) {
  padding: 32px;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.results-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.results-actions {
  display: flex;
  gap: 12px;
}

.loading-state {
  text-align: center;
  padding: 40px 0;
}

.loading-text {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-top: 20px;
  color: #606266;
  font-size: 16px;
}

.results-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 24px;
}

.result-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  cursor: pointer;
  overflow: hidden;
}

.result-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

.product-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.similarity-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

.product-info {
  padding: 16px;
}

.product-title {
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 12px 0;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.product-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.price-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.current-price {
  font-size: 18px;
  font-weight: 700;
  color: #e74c3c;
}

.original-price {
  font-size: 14px;
  color: #909399;
  text-decoration: line-through;
}

.shop-info {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 16px;
  font-size: 12px;
  color: #909399;
}

.product-actions {
  display: flex;
  gap: 8px;
}

.results-list {
  background: white;
  border-radius: 12px;
  overflow: hidden;
}

.product-summary {
  display: flex;
  gap: 12px;
}

.product-thumb {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  object-fit: cover;
}

.product-details {
  flex: 1;
}

.product-details .product-title {
  font-size: 14px;
  margin: 0 0 8px 0;
}

.product-details .product-meta {
  display: flex;
  gap: 12px;
  align-items: center;
}

.shop-name {
  font-size: 12px;
  color: #909399;
}

.price-cell {
  text-align: center;
}

.price-cell .current-price {
  font-size: 16px;
  font-weight: 600;
  color: #e74c3c;
}

.price-cell .original-price {
  font-size: 12px;
  color: #909399;
  text-decoration: line-through;
}

.similarity-cell {
  text-align: center;
}

.similarity-text {
  font-size: 12px;
  color: #606266;
  margin-top: 4px;
  display: block;
}

.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.pagination-container {
  padding: 24px 0 0 0;
  display: flex;
  justify-content: center;
  border-top: 1px solid #f0f0f0;
}

.empty-state {
  text-align: center;
  padding: 80px 0;
}

.product-detail {
  padding: 20px 0;
}

.detail-header {
  display: flex;
  gap: 24px;
  margin-bottom: 24px;
}

.detail-image {
  width: 200px;
  height: 200px;
  border-radius: 12px;
  object-fit: cover;
}

.detail-info {
  flex: 1;
}

.detail-info h2 {
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 16px 0;
  line-height: 1.4;
}

.detail-meta {
  display: flex;
  gap: 24px;
  margin-bottom: 16px;
}

.price-section .current-price {
  font-size: 24px;
  font-weight: 700;
  color: #e74c3c;
  margin-right: 12px;
}

.price-section .original-price {
  font-size: 16px;
  color: #909399;
  text-decoration: line-through;
}

.shop-section {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  color: #606266;
}

.similarity-section {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #606266;
}

.detail-actions {
  display: flex;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .results-grid {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  }
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .header-stats {
    width: 100%;
    justify-content: space-around;
  }
  
  .search-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .results-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .results-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 16px;
  }
  
  .detail-header {
    flex-direction: column;
    text-align: center;
  }
  
  .detail-meta {
    flex-direction: column;
    gap: 12px;
  }
}
</style> 