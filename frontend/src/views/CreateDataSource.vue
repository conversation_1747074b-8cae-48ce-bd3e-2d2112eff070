<template>
  <div>
    <h1>创建新的数据源</h1>
    <el-form :model="form" label-width="120px" :rules="rules" ref="formRef">
      <el-form-item label="数据源名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入数据源名称"></el-input>
      </el-form-item>
      
      <el-form-item label="数据源类型" prop="type">
        <el-select v-model="form.type" placeholder="请选择类型" @change="onTypeChange">
          <el-option label="API" value="api"></el-option>
          <el-option label="网站" value="website"></el-option>
          <el-option label="文件" value="file"></el-option>
        </el-select>
      </el-form-item>

      <!-- API类型的详细配置 -->
      <template v-if="form.type === 'api'">
        <el-form-item label="API URL" prop="apiUrl">
          <el-input v-model="form.apiUrl" placeholder="例如: http://api.example.com/products"></el-input>
          <div class="form-tip">API的基础URL地址</div>
        </el-form-item>

        <el-form-item label="请求方法">
          <el-select v-model="form.method" placeholder="选择请求方法">
            <el-option label="GET" value="GET"></el-option>
            <el-option label="POST" value="POST"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="商品ID参数名">
          <el-input v-model="form.itemIdParam" placeholder="例如: item_id, product_id, id">
            <template #suffix>
              <el-tooltip content="API中用于传递商品ID的参数名称" placement="top">
                <i class="el-icon-question"></i>
              </el-tooltip>
            </template>
          </el-input>
          <div class="form-tip">商品ID在API请求中的参数名称，默认为 item_id</div>
        </el-form-item>

        <el-form-item label="API Token">
          <el-input v-model="form.token" placeholder="请输入API访问令牌" show-password>
            <template #suffix>
              <el-tooltip content="用于API身份验证的令牌" placement="top">
                <i class="el-icon-question"></i>
              </el-tooltip>
            </template>
          </el-input>
          <div class="form-tip">API访问所需的身份验证令牌</div>
        </el-form-item>

        <el-form-item label="其他参数">
          <el-input v-model="form.extraParams" type="textarea" placeholder='例如: {"format": "json", "version": "v1"}'>
            <template #suffix>
              <el-tooltip content="其他需要传递的参数，JSON格式" placement="top">
                <i class="el-icon-question"></i>
              </el-tooltip>
            </template>
          </el-input>
          <div class="form-tip">其他API参数，JSON格式，例如: {"format": "json"}</div>
        </el-form-item>

        <el-form-item label="请求头">
          <el-input v-model="form.headers" type="textarea" placeholder='例如: {"Content-Type": "application/json"}'>
            <template #suffix>
              <el-tooltip content="HTTP请求头，JSON格式" placement="top">
                <i class="el-icon-question"></i>
              </el-tooltip>
            </template>
          </el-input>
          <div class="form-tip">HTTP请求头，JSON格式，留空表示不设置额外请求头</div>
        </el-form-item>

        <el-form-item label="超时时间(秒)">
          <el-input-number v-model="form.timeout" :min="5" :max="300" placeholder="30"></el-input-number>
          <div class="form-tip">API请求超时时间，默认30秒</div>
        </el-form-item>
      </template>

      <!-- 其他类型保持原有的配置方式 -->
      <template v-else>
        <el-form-item label="配置信息" prop="config">
          <el-input v-model="form.config" type="textarea" placeholder="请输入JSON格式的配置信息"></el-input>
          <div class="form-tip">JSON格式的配置信息</div>
        </el-form-item>
      </template>

      <el-form-item label="描述">
        <el-input v-model="form.description" type="textarea" placeholder="请输入数据源描述"></el-input>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="onSubmit" :loading="loading">立即创建</el-button>
        <el-button @click="onCancel">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { ElMessage } from 'element-plus'

const router = useRouter()
const authStore = useAuthStore()
const formRef = ref()
const loading = ref(false)

const form = reactive({
  name: '',
  type: '',
  config: '',
  description: '',
  // API特有字段
  apiUrl: '',
  method: 'GET',
  itemIdParam: 'item_id',
  token: '',
  extraParams: '',
  headers: '',
  timeout: 30
})

const rules = {
  name: [
    { required: true, message: '请输入数据源名称', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择数据源类型', trigger: 'change' }
  ],
  apiUrl: [
    { required: true, message: '请输入API URL', trigger: 'blur' }
  ]
}

const onTypeChange = () => {
  // 当类型改变时，重置相关字段
  if (form.type !== 'api') {
    form.apiUrl = ''
    form.method = 'GET'
    form.itemIdParam = 'item_id'
    form.token = ''
    form.extraParams = ''
    form.headers = ''
    form.timeout = 30
  } else {
    form.config = ''
  }
}

const buildApiConfig = () => {
  const config = {
    url: form.apiUrl,
    method: form.method,
    item_id_param: form.itemIdParam,
    timeout: form.timeout
  }

  // 添加token参数
  if (form.token) {
    config.params = { token: form.token }
  }

  // 添加其他参数
  if (form.extraParams) {
    try {
      const extraParams = JSON.parse(form.extraParams)
      config.params = { ...config.params, ...extraParams }
    } catch (error) {
      throw new Error('其他参数格式错误，请输入有效的JSON格式')
    }
  }

  // 添加请求头
  if (form.headers) {
    try {
      config.headers = JSON.parse(form.headers)
    } catch (error) {
      throw new Error('请求头格式错误，请输入有效的JSON格式')
    }
  }

  return config
}

const onSubmit = async () => {
  try {
    // 表单验证
    await formRef.value.validate()
    
    loading.value = true

    let config
    if (form.type === 'api') {
      config = buildApiConfig()
    } else {
      try {
        config = form.config ? JSON.parse(form.config) : {}
      } catch (error) {
        ElMessage.error('配置信息格式错误，请输入有效的JSON格式')
        return
      }
    }

    const requestData = {
      name: form.name,
      type: form.type,
      config: config,
      description: form.description
    }
    
    // 为API类型的数据源添加默认的字段映射
    if (form.type === 'api') {
      requestData.field_mapping = {
        fields: {
          // 现有其他默认映射，如果需要，可以在这里添加
          // 'main_image': 'mainImageUrl',
          // 'api_status': 'code',
          // 'product_status': 'state',
          'promotions': {
            'path': 'promotionDisplays'
          }
        }
      };
    }
    
    const response = await authStore.request('/data-sources', {
      method: 'POST',
      body: JSON.stringify(requestData)
    })
    
    if (response.success) {
      ElMessage.success('数据源创建成功')
      router.push('/data-sources')
    } else {
      ElMessage.error(response.message || '创建失败')
    }
  } catch (error) {
    console.error('创建数据源失败:', error)
    ElMessage.error(error.message || '创建数据源失败')
  } finally {
    loading.value = false
  }
}

const onCancel = () => {
  router.push('/data-sources')
}
</script>

<style scoped>
.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.el-form-item {
  margin-bottom: 22px;
}

.el-input-number {
  width: 200px;
}
</style> 