<template>
  <div class="register-container">
    <div class="register-form">
      <div class="register-header">
        <h1>电商市场动态监测系统</h1>
        <p>用户注册</p>
      </div>
      
      <el-form
        ref="registerFormRef"
        :model="registerForm"
        :rules="registerRules"
        class="register-form-content"
        @submit.prevent="handleRegister"
      >
        <el-form-item prop="username">
          <el-input
            v-model="registerForm.username"
            placeholder="请输入用户名"
            size="large"
            prefix-icon="User"
            clearable
          >
            <template #suffix>
              <el-tooltip content="用户名长度为3-20个字符，只能包含字母、数字和下划线" placement="top">
                <el-icon class="input-tip"><QuestionFilled /></el-icon>
              </el-tooltip>
            </template>
          </el-input>
        </el-form-item>
        
        <el-form-item prop="email">
          <el-input
            v-model="registerForm.email"
            type="email"
            placeholder="请输入邮箱地址"
            size="large"
            prefix-icon="Message"
            clearable
          />
        </el-form-item>
        
        <el-form-item prop="real_name">
          <el-input
            v-model="registerForm.real_name"
            placeholder="请输入真实姓名"
            size="large"
            prefix-icon="UserFilled"
            clearable
          />
        </el-form-item>
        
        <el-form-item prop="password">
          <el-input
            v-model="registerForm.password"
            type="password"
            placeholder="请输入密码"
            size="large"
            prefix-icon="Lock"
            show-password
          >
            <template #suffix>
              <el-tooltip content="密码长度为6-50个字符，建议包含字母、数字和特殊字符" placement="top">
                <el-icon class="input-tip"><QuestionFilled /></el-icon>
              </el-tooltip>
            </template>
          </el-input>
        </el-form-item>
        
        <el-form-item prop="confirmPassword">
          <el-input
            v-model="registerForm.confirmPassword"
            type="password"
            placeholder="请确认密码"
            size="large"
            prefix-icon="Lock"
            show-password
            @keyup.enter="handleRegister"
          />
        </el-form-item>
        
        <el-form-item prop="captcha">
          <div class="captcha-row">
            <el-input
              v-model="registerForm.captcha"
              placeholder="请输入验证码"
              size="large"
              prefix-icon="Key"
              clearable
              style="flex: 1;"
            />
            <Captcha ref="captchaRef" @update:value="handleCaptchaUpdate" />
          </div>
        </el-form-item>
        
        <el-form-item>
          <el-checkbox v-model="registerForm.agreement" size="large">
            我已阅读并同意
            <el-link type="primary" @click="showAgreement">《用户协议》</el-link>
            和
            <el-link type="primary" @click="showPrivacy">《隐私政策》</el-link>
          </el-checkbox>
        </el-form-item>
        
        <el-form-item>
          <el-button
            type="primary"
            size="large"
            :loading="isLoading"
            @click="handleRegister"
            class="register-button"
            :disabled="!registerForm.agreement"
          >
            {{ isLoading ? '注册中...' : '立即注册' }}
          </el-button>
        </el-form-item>
      </el-form>
      
      <div class="register-footer">
        <p>
          已有账号？
          <el-link type="primary" @click="goToLogin">立即登录</el-link>
        </p>
      </div>
    </div>
    
    <!-- 用户协议对话框 -->
    <el-dialog
      v-model="agreementVisible"
      title="用户协议"
      width="60%"
      :before-close="handleAgreementClose"
    >
      <div class="agreement-content">
        <h3>1. 服务条款</h3>
        <p>欢迎使用电商市场动态监测系统。本协议是您与本系统之间的法律协议。</p>
        
        <h3>2. 账户责任</h3>
        <p>您有责任保护您的账户信息安全，包括用户名和密码。</p>
        
        <h3>3. 使用规范</h3>
        <p>您同意合法、合规地使用本系统，不得进行任何违法或损害系统的行为。</p>
        
        <h3>4. 数据隐私</h3>
        <p>我们严格保护您的个人数据，详情请参阅《隐私政策》。</p>
        
        <h3>5. 服务变更</h3>
        <p>我们保留随时修改或终止服务的权利，恕不另行通知。</p>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="agreementVisible = false">关闭</el-button>
          <el-button type="primary" @click="acceptAgreement">同意并关闭</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 隐私政策对话框 -->
    <el-dialog
      v-model="privacyVisible"
      title="隐私政策"
      width="60%"
      :before-close="handlePrivacyClose"
    >
      <div class="privacy-content">
        <h3>1. 信息收集</h3>
        <p>我们收集您主动提供的信息，如注册信息、使用记录等。</p>
        
        <h3>2. 信息使用</h3>
        <p>我们使用收集的信息为您提供更好的服务体验。</p>
        
        <h3>3. 信息保护</h3>
        <p>我们采用industry标准的安全措施保护您的个人信息。</p>
        
        <h3>4. 信息共享</h3>
        <p>除法律要求外，我们不会与第三方分享您的个人信息。</p>
        
        <h3>5. 联系我们</h3>
        <p>如有隐私相关问题，请联系我们的客服团队。</p>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="privacyVisible = false">关闭</el-button>
          <el-button type="primary" @click="acceptPrivacy">同意并关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElForm, ElMessage, ElMessageBox } from 'element-plus'
import Captcha from '../components/Captcha.vue'

const router = useRouter()

// 表单引用
const registerFormRef = ref<InstanceType<typeof ElForm>>()
const captchaRef = ref<InstanceType<typeof Captcha>>()

// 加载状态
const isLoading = ref(false)

// 对话框状态
const agreementVisible = ref(false)
const privacyVisible = ref(false)

// 验证码值
const captchaValue = ref('')

// 表单数据
const registerForm = reactive({
  username: '',
  email: '',
  real_name: '',
  password: '',
  confirmPassword: '',
  captcha: '',
  agreement: false,
})

// 自定义验证器
const validateUsername = (rule: any, value: string, callback: any) => {
  if (!value) {
    callback(new Error('请输入用户名'))
  } else if (value.length < 3 || value.length > 20) {
    callback(new Error('用户名长度在 3 到 20 个字符'))
  } else if (!/^[a-zA-Z0-9_]+$/.test(value)) {
    callback(new Error('用户名只能包含字母、数字和下划线'))
  } else {
    callback()
  }
}

const validatePassword = (rule: any, value: string, callback: any) => {
  if (!value) {
    callback(new Error('请输入密码'))
  } else if (value.length < 6 || value.length > 50) {
    callback(new Error('密码长度在 6 到 50 个字符'))
  } else {
    callback()
  }
}

const validateConfirmPassword = (rule: any, value: string, callback: any) => {
  if (!value) {
    callback(new Error('请确认密码'))
  } else if (value !== registerForm.password) {
    callback(new Error('两次输入的密码不一致'))
  } else {
    callback()
  }
}

const validateCaptcha = (rule: any, value: string, callback: any) => {
  if (!value) {
    callback(new Error('请输入验证码'))
  } else if (!captchaRef.value?.verify(value)) {
    callback(new Error('验证码不正确'))
  } else {
    callback()
  }
}

// 表单验证规则
const registerRules = {
  username: [
    { validator: validateUsername, trigger: 'blur' },
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' },
  ],
  real_name: [
    { required: true, message: '请输入真实姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '姓名长度在 2 到 20 个字符', trigger: 'blur' },
  ],
  password: [
    { validator: validatePassword, trigger: 'blur' },
  ],
  confirmPassword: [
    { validator: validateConfirmPassword, trigger: 'blur' },
  ],
  captcha: [
    { validator: validateCaptcha, trigger: 'blur' },
  ],
}

// 处理验证码更新
const handleCaptchaUpdate = (value: string) => {
  captchaValue.value = value
}

// 处理注册
const handleRegister = async () => {
  if (!registerFormRef.value) return
  
  try {
    // 验证表单
    await registerFormRef.value.validate()
    
    if (!registerForm.agreement) {
      ElMessage.warning('请先同意用户协议和隐私政策')
      return
    }
    
    isLoading.value = true
    
    // 模拟注册API调用
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // 注册成功
    ElMessage.success('注册成功！请登录您的账户')
    
    // 跳转到登录页面
    router.push('/login')
    
  } catch (error) {
    console.error('注册表单验证失败:', error)
    // 验证码错误时刷新验证码
    if (registerForm.captcha && !captchaRef.value?.verify(registerForm.captcha)) {
      captchaRef.value?.refresh()
      registerForm.captcha = ''
    }
  } finally {
    isLoading.value = false
  }
}

// 显示用户协议
const showAgreement = () => {
  agreementVisible.value = true
}

// 显示隐私政策
const showPrivacy = () => {
  privacyVisible.value = true
}

// 同意用户协议
const acceptAgreement = () => {
  registerForm.agreement = true
  agreementVisible.value = false
  ElMessage.success('已同意用户协议')
}

// 同意隐私政策
const acceptPrivacy = () => {
  privacyVisible.value = false
  ElMessage.success('已了解隐私政策')
}

// 处理协议对话框关闭
const handleAgreementClose = (done: () => void) => {
  done()
}

// 处理隐私政策对话框关闭
const handlePrivacyClose = (done: () => void) => {
  done()
}

// 跳转到登录页面
const goToLogin = () => {
  router.push('/login')
}

// 组件挂载时的处理
onMounted(() => {
  // 可以在这里做一些初始化工作
})
</script>

<style scoped>
.register-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.register-form {
  background: white;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 480px;
  animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.register-header {
  text-align: center;
  margin-bottom: 30px;
}

.register-header h1 {
  color: #303133;
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 10px 0;
}

.register-header p {
  color: #606266;
  font-size: 16px;
  margin: 0;
}

.register-form-content {
  margin-bottom: 20px;
}

.captcha-row {
  display: flex;
  gap: 10px;
  align-items: center;
}

.input-tip {
  color: #909399;
  cursor: help;
}

.register-button {
  width: 100%;
  height: 50px;
  font-size: 16px;
  font-weight: 500;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  transition: all 0.3s ease;
}

.register-button:hover:not(:disabled) {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
}

.register-button:disabled {
  background: #c0c4cc;
  cursor: not-allowed;
}

.register-footer {
  text-align: center;
  color: #909399;
  font-size: 14px;
}

.register-footer p {
  margin: 0;
}

.agreement-content,
.privacy-content {
  max-height: 400px;
  overflow-y: auto;
  line-height: 1.6;
}

.agreement-content h3,
.privacy-content h3 {
  color: #303133;
  font-size: 16px;
  margin: 20px 0 10px 0;
}

.agreement-content p,
.privacy-content p {
  color: #606266;
  margin: 0 0 15px 0;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .register-form {
    padding: 30px 20px;
    margin: 10px;
  }
  
  .register-header h1 {
    font-size: 20px;
  }
  
  .register-header p {
    font-size: 14px;
  }
  
  .captcha-row {
    flex-direction: column;
    align-items: stretch;
  }
}
</style> 