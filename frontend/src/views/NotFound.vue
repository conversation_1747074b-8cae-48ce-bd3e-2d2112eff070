<template>
  <div class="not-found-container">
    <div class="not-found-content">
      <div class="error-animation">
        <div class="error-number">
          <span class="four">4</span>
          <span class="zero">0</span>
          <span class="four">4</span>
        </div>
        <div class="error-icon">
          <el-icon class="icon-animation"><Warning /></el-icon>
        </div>
      </div>
      
      <div class="error-text">
        <h1>页面走丢了</h1>
        <p class="error-description">
          抱歉，您访问的页面不存在或已被移除
        </p>
        <p class="error-suggestion">
          请检查网址是否正确，或返回首页重新开始
        </p>
      </div>
      
      <div class="error-actions">
        <el-button 
          type="primary" 
          size="large" 
          @click="goHome"
          class="action-button primary-action"
        >
          <el-icon><House /></el-icon>
          返回首页
        </el-button>
        
        <el-button 
          size="large" 
          @click="goBack"
          class="action-button secondary-action"
        >
          <el-icon><Back /></el-icon>
          返回上页
        </el-button>
      </div>
      
      <div class="helpful-links">
        <h3>常用功能</h3>
        <div class="links-grid">
          <el-link 
            class="help-link" 
            @click="$router.push('/channel-price/dashboard')"
          >
            <el-icon><TrendCharts /></el-icon>
            渠道价格监测
          </el-link>
          
          <el-link 
            class="help-link" 
            @click="$router.push('/competitor/dashboard')"
          >
            <el-icon><View /></el-icon>
            竞品动态监测
          </el-link>
          
          <el-link 
            class="help-link" 
            @click="$router.push('/similar/live-search')"
          >
            <el-icon><Search /></el-icon>
            相似同款查询
          </el-link>
          
          <el-link 
            class="help-link" 
            @click="$router.push('/system/settings')"
            v-if="authStore.isAdmin"
          >
            <el-icon><Setting /></el-icon>
            系统设置
          </el-link>
        </div>
      </div>
    </div>
    
    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="floating-shape shape-1"></div>
      <div class="floating-shape shape-2"></div>
      <div class="floating-shape shape-3"></div>
      <div class="floating-shape shape-4"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'

const router = useRouter()
const authStore = useAuthStore()

const goHome = () => {
  router.push('/')
}

const goBack = () => {
  if (window.history.length > 1) {
    router.go(-1)
  } else {
    router.push('/')
  }
}
</script>

<style scoped>
.not-found-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  position: relative;
  overflow: hidden;
}

.not-found-content {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 60px 40px;
  border-radius: 20px;
  box-shadow: 0 30px 60px rgba(0, 0, 0, 0.2);
  text-align: center;
  max-width: 600px;
  width: 100%;
  position: relative;
  z-index: 1;
  animation: slideUp 0.8s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.error-animation {
  margin-bottom: 40px;
}

.error-number {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  margin-bottom: 20px;
}

.error-number span {
  font-size: 120px;
  font-weight: 800;
  line-height: 1;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: bounce 2s ease-in-out infinite;
}

.error-number .four:first-child {
  animation-delay: 0s;
}

.error-number .zero {
  animation-delay: 0.2s;
}

.error-number .four:last-child {
  animation-delay: 0.4s;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

.error-icon {
  font-size: 80px;
  color: #f56c6c;
}

.icon-animation {
  animation: shake 1s ease-in-out infinite;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
  20%, 40%, 60%, 80% { transform: translateX(2px); }
}

.error-text h1 {
  font-size: 36px;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 16px;
}

.error-description {
  font-size: 18px;
  color: #64748b;
  margin-bottom: 12px;
  font-weight: 500;
}

.error-suggestion {
  font-size: 14px;
  color: #94a3b8;
  margin-bottom: 40px;
}

.error-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  margin-bottom: 40px;
  flex-wrap: wrap;
}

.action-button {
  padding: 12px 32px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 12px;
  transition: all 0.3s ease;
  min-width: 140px;
}

.primary-action {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
}

.primary-action:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
}

.secondary-action {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  color: #64748b;
}

.secondary-action:hover {
  background: #f1f5f9;
  color: #475569;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.helpful-links h3 {
  font-size: 18px;
  color: #374151;
  margin-bottom: 20px;
  font-weight: 600;
}

.links-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
  gap: 12px;
}

.help-link {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: #f8fafc;
  border-radius: 8px;
  text-decoration: none;
  color: #64748b;
  font-weight: 500;
  transition: all 0.3s ease;
  border: 1px solid #e2e8f0;
}

.help-link:hover {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(102, 126, 234, 0.3);
}

.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.floating-shape {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.shape-1 {
  width: 80px;
  height: 80px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.shape-2 {
  width: 120px;
  height: 120px;
  top: 20%;
  right: 15%;
  animation-delay: 2s;
}

.shape-3 {
  width: 60px;
  height: 60px;
  bottom: 30%;
  left: 20%;
  animation-delay: 4s;
}

.shape-4 {
  width: 100px;
  height: 100px;
  bottom: 20%;
  right: 10%;
  animation-delay: 1s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .not-found-content {
    padding: 40px 24px;
    margin: 16px;
  }
  
  .error-number span {
    font-size: 80px;
  }
  
  .error-text h1 {
    font-size: 28px;
  }
  
  .error-description {
    font-size: 16px;
  }
  
  .error-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .action-button {
    width: 100%;
    max-width: 280px;
  }
  
  .links-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .error-number span {
    font-size: 60px;
  }
  
  .error-text h1 {
    font-size: 24px;
  }
  
  .error-icon {
    font-size: 60px;
  }
}
</style> 