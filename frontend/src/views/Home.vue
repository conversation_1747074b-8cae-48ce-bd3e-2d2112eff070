<template>
  <div class="home">
    <!-- 欢迎横幅 -->
    <div class="welcome-banner">
      <div class="banner-content">
        <div class="banner-text">
          <h1 class="banner-title">欢迎使用智能电商市场动态监测系统</h1>
          <p class="banner-subtitle">实时监控竞品价格，智能分析市场动态，助力电商决策</p>
          <div class="banner-stats">
            <div class="stat-item">
              <span class="stat-value">{{ stats.totalProducts }}</span>
              <span class="stat-label">监控商品</span>
            </div>
            <div class="stat-item">
              <span class="stat-value">{{ stats.todayUpdates }}</span>
              <span class="stat-label">今日更新</span>
            </div>
            <div class="stat-item">
              <span class="stat-value">{{ stats.activeAlerts }}</span>
              <span class="stat-label">活跃预警</span>
            </div>
          </div>
        </div>
        <div class="banner-visual">
          <div class="visual-chart">
            <el-icon size="120" class="chart-icon"><TrendCharts /></el-icon>
          </div>
        </div>
      </div>
    </div>

    <!-- 核心功能卡片 -->
    <div class="feature-section">
      <h2 class="section-title">核心功能</h2>
      <div class="feature-grid">
        <el-card class="feature-card" shadow="hover" @click="navigateTo('/channel-price/tasks')">
          <div class="feature-content">
            <div class="feature-icon channel-price">
              <el-icon size="32"><TrendCharts /></el-icon>
            </div>
            <h3 class="feature-title">渠道价格监测</h3>
            <p class="feature-desc">监控自营渠道商品价格变动，及时发现价格异常</p>
            <div class="feature-stats">
              <span class="feature-stat">{{ stats.channelTasks }} 个任务</span>
              <span class="feature-stat">{{ stats.channelProducts }} 个商品</span>
            </div>
          </div>
        </el-card>

        <el-card class="feature-card" shadow="hover" @click="navigateTo('/competitor/tasks')">
          <div class="feature-content">
            <div class="feature-icon competitor">
              <el-icon size="32"><View /></el-icon>
            </div>
            <h3 class="feature-title">竞品动态监测</h3>
            <p class="feature-desc">全方位分析竞品价格策略和促销活动</p>
            <div class="feature-stats">
              <span class="feature-stat">{{ stats.competitorTasks }} 个任务</span>
              <span class="feature-stat">{{ stats.competitorProducts }} 个竞品</span>
            </div>
          </div>
        </el-card>

        <el-card class="feature-card" shadow="hover" @click="navigateTo('/similar/live-search')">
          <div class="feature-content">
            <div class="feature-icon similar-search">
              <el-icon size="32"><Search /></el-icon>
            </div>
            <h3 class="feature-title">相似同款查询</h3>
            <p class="feature-desc">智能搜索同款商品，监控价格和侵权行为</p>
            <div class="feature-stats">
              <span class="feature-stat">{{ stats.similarSearches }} 次搜索</span>
              <span class="feature-stat">{{ stats.similarMonitoring }} 个监控</span>
            </div>
          </div>
        </el-card>

        <el-card class="feature-card" shadow="hover" @click="navigateTo('/channel-price/alerts')" v-if="authStore.isAdmin">
          <div class="feature-content">
            <div class="feature-icon system-management">
              <el-icon size="32"><Tools /></el-icon>
            </div>
            <h3 class="feature-title">系统管理</h3>
            <p class="feature-desc">管理数据源、用户权限和系统配置</p>
            <div class="feature-stats">
              <span class="feature-stat">{{ stats.dataSources }} 个数据源</span>
              <span class="feature-stat">{{ stats.totalUsers }} 个用户</span>
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 数据概览 -->
    <div class="overview-section">
      <h2 class="section-title">数据概览</h2>
      <div class="overview-grid">
        <el-card class="overview-card">
          <div class="overview-content">
            <div class="overview-header">
              <h3>监控任务统计</h3>
              <el-icon class="overview-icon"><Monitor /></el-icon>
            </div>
            <div class="overview-stats">
              <div class="stat-row">
                <span class="stat-label">运行中</span>
                <span class="stat-value running">{{ stats.runningTasks }}</span>
              </div>
              <div class="stat-row">
                <span class="stat-label">已暂停</span>
                <span class="stat-value paused">{{ stats.pausedTasks }}</span>
              </div>
              <div class="stat-row">
                <span class="stat-label">已完成</span>
                <span class="stat-value completed">{{ stats.completedTasks }}</span>
              </div>
            </div>
          </div>
        </el-card>

        <el-card class="overview-card">
          <div class="overview-content">
            <div class="overview-header">
              <h3>预警统计</h3>
              <el-icon class="overview-icon"><Bell /></el-icon>
            </div>
            <div class="overview-stats">
              <div class="stat-row">
                <span class="stat-label">严重预警</span>
                <span class="stat-value critical">{{ stats.criticalAlerts }}</span>
              </div>
              <div class="stat-row">
                <span class="stat-label">一般预警</span>
                <span class="stat-value warning">{{ stats.warningAlerts }}</span>
              </div>
              <div class="stat-row">
                <span class="stat-label">信息提醒</span>
                <span class="stat-value info">{{ stats.infoAlerts }}</span>
              </div>
            </div>
          </div>
        </el-card>

        <el-card class="overview-card">
          <div class="overview-content">
            <div class="overview-header">
              <h3>数据采集</h3>
              <el-icon class="overview-icon"><DataBoard /></el-icon>
            </div>
            <div class="overview-stats">
              <div class="stat-row">
                <span class="stat-label">今日采集</span>
                <span class="stat-value">{{ stats.todayCollected }}</span>
              </div>
              <div class="stat-row">
                <span class="stat-label">本周采集</span>
                <span class="stat-value">{{ stats.weekCollected }}</span>
              </div>
              <div class="stat-row">
                <span class="stat-label">成功率</span>
                <span class="stat-value success">{{ stats.successRate }}%</span>
              </div>
            </div>
          </div>
        </el-card>

        <el-card class="overview-card">
          <div class="overview-content">
            <div class="overview-header">
              <h3>系统状态</h3>
              <el-icon class="overview-icon"><Connection /></el-icon>
            </div>
            <div class="overview-stats">
              <div class="stat-row">
                <span class="stat-label">数据源状态</span>
                <el-tag :type="stats.dataSourceStatus === 'healthy' ? 'success' : 'danger'" size="small">
                  {{ stats.dataSourceStatus === 'healthy' ? '正常' : '异常' }}
                </el-tag>
              </div>
              <div class="stat-row">
                <span class="stat-label">队列状态</span>
                <el-tag :type="stats.queueStatus === 'healthy' ? 'success' : 'danger'" size="small">
                  {{ stats.queueStatus === 'healthy' ? '正常' : '异常' }}
                </el-tag>
              </div>
              <div class="stat-row">
                <span class="stat-label">API响应</span>
                <span class="stat-value">{{ stats.apiResponseTime }}ms</span>
              </div>
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 快速操作 -->
    <div class="quick-actions-section">
      <h2 class="section-title">快速操作</h2>
      <div class="quick-actions-grid">
        <el-button type="primary" size="large" @click="navigateTo('/channel-price/tasks')" class="quick-action-btn">
          <el-icon><Plus /></el-icon>
          创建监控任务
        </el-button>
        <el-button type="success" size="large" @click="navigateTo('/task-group-management')" class="quick-action-btn">
          <el-icon><FolderAdd /></el-icon>
          管理任务组
        </el-button>
        <el-button type="info" size="large" @click="navigateTo('/channel-price/dashboard')" class="quick-action-btn">
          <el-icon><DataAnalysis /></el-icon>
          查看数据看板
        </el-button>
        <el-button type="warning" size="large" @click="testApiConnection" class="quick-action-btn" :loading="apiTesting">
          <el-icon><Connection /></el-icon>
          测试API连接
        </el-button>
      </div>
    </div>

    <!-- API测试结果显示 -->
    <div class="api-test-section" v-if="apiTestResults.length > 0">
      <h2 class="section-title">API连接测试结果</h2>
      <div class="test-results">
        <el-alert
          v-for="(result, index) in apiTestResults"
          :key="index"
          :title="result.title"
          :description="result.description"
          :type="result.type"
          :closable="false"
          style="margin-bottom: 8px;"
        />
      </div>
    </div>

    <!-- 最近活动 -->
    <div class="recent-activity-section">
      <h2 class="section-title">最近活动</h2>
      <el-card class="activity-card">
        <el-timeline>
          <el-timeline-item
            v-for="activity in recentActivities"
            :key="activity.id"
            :timestamp="activity.timestamp"
            :type="activity.type"
            :icon="activity.icon"
          >
            <div class="activity-content">
              <h4 class="activity-title">{{ activity.title }}</h4>
              <p class="activity-desc">{{ activity.description }}</p>
            </div>
          </el-timeline-item>
        </el-timeline>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'
import { ElMessage } from 'element-plus'
import analyticsApi from '../api/analytics'
import dataSourceApi from '../api/dataSource'
import monitoringTaskApi from '../api/monitoringTask'
import {
  TrendCharts,
  View,
  Search,
  Tools,
  Monitor,
  Bell,
  DataBoard,
  Connection,
  Plus,
  Setting,
  Timer,
  Warning,
  Check,
  InfoFilled,
  FolderAdd,
  DataAnalysis
} from '@element-plus/icons-vue'

const router = useRouter()
const authStore = useAuthStore()

// 统计数据
const stats = ref({
  // 横幅统计
  totalProducts: 0,
  todayUpdates: 0,
  activeAlerts: 0,
  
  // 功能统计
  channelTasks: 0,
  channelProducts: 0,
  competitorTasks: 0,
  competitorProducts: 0,
  similarSearches: 0,
  similarMonitoring: 0,
  dataSources: 0,
  totalUsers: 0,
  
  // 任务统计
  runningTasks: 0,
  pausedTasks: 0,
  completedTasks: 0,
  
  // 预警统计
  criticalAlerts: 0,
  warningAlerts: 0,
  infoAlerts: 0,
  
  // 数据采集
  todayCollected: 0,
  weekCollected: 0,
  successRate: 0,
  
  // 系统状态
  dataSourceStatus: 'healthy',
  queueStatus: 'healthy',
  apiResponseTime: 0
})

// 最近活动
const recentActivities = ref([
  {
    id: 1,
    title: '新增监控任务',
    description: '创建了"品牌A官方旗舰店"价格监控任务',
    timestamp: '2024-01-15 14:30',
    type: 'success',
    icon: Plus
  },
  {
    id: 2,
    title: '预警触发',
    description: '商品"智能手机X"价格偏离超过15%',
    timestamp: '2024-01-15 13:45',
    type: 'warning',
    icon: Warning
  },
  {
    id: 3,
    title: '数据采集完成',
    description: '完成了156个商品的价格数据采集',
    timestamp: '2024-01-15 12:00',
    type: 'primary',
    icon: Check
  },
  {
    id: 4,
    title: '用户登录',
    description: `${authStore.user?.name || authStore.user?.username} 登录系统`,
    timestamp: new Date().toLocaleString(),
    type: 'info',
    icon: InfoFilled
  }
])

// 导航到指定页面
const navigateTo = (path: string) => {
  router.push(path)
}

// 加载统计数据
const loadStats = async () => {
  try {
    // TODO: 这里应该调用API获取真实的统计数据
    // 暂时使用模拟数据
    stats.value = {
      // 横幅统计
      totalProducts: 1248,
      todayUpdates: 156,
      activeAlerts: 8,
      
      // 功能统计
      channelTasks: 12,
      channelProducts: 456,
      competitorTasks: 8,
      competitorProducts: 234,
      similarSearches: 89,
      similarMonitoring: 23,
      dataSources: 5,
      totalUsers: 15,
      
      // 任务统计
      runningTasks: 18,
      pausedTasks: 3,
      completedTasks: 45,
      
      // 预警统计
      criticalAlerts: 2,
      warningAlerts: 5,
      infoAlerts: 12,
      
      // 数据采集
      todayCollected: 1256,
      weekCollected: 8934,
      successRate: 98.5,
      
      // 系统状态
      dataSourceStatus: 'healthy',
      queueStatus: 'healthy',
      apiResponseTime: 125
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

onMounted(() => {
  loadStats()
})

// API测试相关
const apiTestResults = ref([])
const apiTesting = ref(false)

const testApiConnection = async () => {
  apiTesting.value = true
  apiTestResults.value = []

  const tests = [
    {
      name: '用户认证状态',
      test: async () => {
        const user = authStore.user
        if (user) {
          return { success: true, message: `当前用户: ${user.name || user.username}` }
        } else {
          throw new Error('用户未登录')
        }
      }
    },
    {
      name: '获取统计数据',
      test: async () => {
        const response = await analyticsApi.getDashboardStatistics()
        return { success: true, message: `获取到 ${Object.keys(response).length} 项统计数据` }
      }
    },
    {
      name: '获取数据源列表',
      test: async () => {
        const response = await dataSourceApi.getAll()
        return { success: true, message: `获取到 ${response.data?.length || 0} 个数据源` }
      }
    },
    {
      name: '获取监控任务',
      test: async () => {
        const response = await monitoringTaskApi.getAllTasks()
        return { success: true, message: `获取到 ${response.data?.length || 0} 个监控任务` }
      }
    }
  ]

  for (const { name, test } of tests) {
    try {
      const result = await test()
      apiTestResults.value.push({
        title: `✅ ${name}`,
        description: result.message,
        type: 'success'
      })
    } catch (error) {
      apiTestResults.value.push({
        title: `❌ ${name}`,
        description: error.response?.data?.message || error.message || '测试失败',
        type: 'error'
      })
    }
  }

  apiTesting.value = false
  ElMessage.success('API连接测试完成')
}
</script>

<style scoped>
.home {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0;
}

/* 欢迎横幅 */
.welcome-banner {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  margin-bottom: 32px;
  overflow: hidden;
}

.banner-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 48px;
  color: white;
}

.banner-text {
  flex: 1;
  max-width: 60%;
}

.banner-title {
  font-size: 32px;
  font-weight: 700;
  margin: 0 0 16px 0;
  line-height: 1.2;
}

.banner-subtitle {
  font-size: 18px;
  opacity: 0.9;
  margin: 0 0 32px 0;
  line-height: 1.5;
}

.banner-stats {
  display: flex;
  gap: 48px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  opacity: 0.8;
}

.banner-visual {
  flex: 0 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
}

.visual-chart {
  width: 200px;
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  backdrop-filter: blur(10px);
}

.chart-icon {
  opacity: 0.8;
}

/* 章节标题 */
.section-title {
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 24px 0;
  color: #1f2937;
  position: relative;
  padding-left: 16px;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 2px;
}

/* 功能卡片 */
.feature-section {
  margin-bottom: 48px;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.feature-card {
  cursor: pointer;
  transition: all 0.3s;
  border: 1px solid #e5e7eb;
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
}

.feature-content {
  padding: 24px;
}

.feature-icon {
  width: 64px;
  height: 64px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
  color: white;
}

.feature-icon.channel-price {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.feature-icon.competitor {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.feature-icon.similar-search {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.feature-icon.system-management {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
}

.feature-title {
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #1f2937;
}

.feature-desc {
  font-size: 14px;
  color: #6b7280;
  margin: 0 0 16px 0;
  line-height: 1.5;
}

.feature-stats {
  display: flex;
  gap: 16px;
}

.feature-stat {
  font-size: 12px;
  color: #9ca3af;
  background: #f3f4f6;
  padding: 4px 8px;
  border-radius: 4px;
}

/* 数据概览 */
.overview-section {
  margin-bottom: 48px;
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
}

.overview-card {
  border: 1px solid #e5e7eb;
}

.overview-content {
  padding: 24px;
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.overview-header h3 {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  color: #1f2937;
}

.overview-icon {
  font-size: 20px;
  color: #6b7280;
}

.overview-stats {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.stat-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.stat-value.running {
  color: #10b981;
}

.stat-value.paused {
  color: #f59e0b;
}

.stat-value.completed {
  color: #6b7280;
}

.stat-value.critical {
  color: #ef4444;
}

.stat-value.warning {
  color: #f59e0b;
}

.stat-value.info {
  color: #3b82f6;
}

.stat-value.success {
  color: #10b981;
}

/* 快速操作 */
.quick-actions-section {
  margin-bottom: 48px;
}

.quick-actions-grid {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.quick-action-btn {
  flex: 1;
  min-width: 200px;
  height: 48px;
  font-size: 16px;
  border-radius: 8px;
}

/* 最近活动 */
.recent-activity-section {
  margin-bottom: 32px;
}

.activity-card {
  border: 1px solid #e5e7eb;
}

.activity-content {
  margin-left: 16px;
}

.activity-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 4px 0;
  color: #1f2937;
}

.activity-desc {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
  line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .banner-content {
    flex-direction: column;
    text-align: center;
    padding: 32px 24px;
  }
  
  .banner-text {
    max-width: 100%;
    margin-bottom: 32px;
  }
  
  .banner-title {
    font-size: 24px;
  }
  
  .banner-subtitle {
    font-size: 16px;
  }
  
  .banner-stats {
    justify-content: center;
    gap: 32px;
  }
  
  .visual-chart {
    width: 150px;
    height: 150px;
  }
  
  .feature-grid {
    grid-template-columns: 1fr;
  }
  
  .overview-grid {
    grid-template-columns: 1fr;
  }
  
  .quick-actions-grid {
    flex-direction: column;
  }
  
  .quick-action-btn {
    width: 100%;
  }
}
</style> 