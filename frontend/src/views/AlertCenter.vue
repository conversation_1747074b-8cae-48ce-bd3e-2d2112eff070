<template>
  <div class="alert-center p-4">
    <el-card shadow="never">
      <el-tabs v-model="activeTab" class="demo-tabs">
        <el-tab-pane label="预警规则管理" name="rules">
          <AlertRulesManagement />
        </el-tab-pane>
        <el-tab-pane label="预警历史记录" name="history">
          <AlertHistory />
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import AlertRulesManagement from '../components/AlertRulesManagement.vue';
import AlertHistory from '../components/AlertHistory.vue';

const activeTab = ref('rules');
</script>

<style scoped>
.alert-center {
  height: 100%;
}
</style> 