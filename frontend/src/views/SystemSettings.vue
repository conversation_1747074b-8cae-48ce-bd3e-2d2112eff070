<template>
  <div class="system-settings">
    <div class="page-header">
      <h1>系统设置</h1>
      <p>管理系统全局配置和参数</p>
    </div>

    <!-- 设置分类标签 -->
    <el-tabs v-model="activeTab" class="settings-tabs">
      <!-- 基本设置 -->
      <el-tab-pane label="基本设置" name="basic">
        <el-card>
          <template #header>
            <h3>系统基本信息</h3>
          </template>
          <el-form :model="basicSettings" label-width="120px" class="settings-form">
            <el-form-item label="系统名称">
              <el-input v-model="basicSettings.systemName" placeholder="请输入系统名称" />
            </el-form-item>
            <el-form-item label="系统版本">
              <el-input v-model="basicSettings.version" placeholder="请输入系统版本" readonly />
            </el-form-item>
            <el-form-item label="系统描述">
              <el-input
                v-model="basicSettings.description"
                type="textarea"
                :rows="3"
                placeholder="请输入系统描述"
              />
            </el-form-item>
            <el-form-item label="公司名称">
              <el-input v-model="basicSettings.companyName" placeholder="请输入公司名称" />
            </el-form-item>
            <el-form-item label="联系邮箱">
              <el-input v-model="basicSettings.contactEmail" placeholder="请输入联系邮箱" />
            </el-form-item>
            <el-form-item label="时区设置">
              <el-select v-model="basicSettings.timezone" placeholder="请选择时区">
                <el-option label="北京时间 (UTC+8)" value="Asia/Shanghai" />
                <el-option label="东京时间 (UTC+9)" value="Asia/Tokyo" />
                <el-option label="纽约时间 (UTC-5)" value="America/New_York" />
                <el-option label="伦敦时间 (UTC+0)" value="Europe/London" />
              </el-select>
            </el-form-item>
            <el-form-item label="语言设置">
              <el-select v-model="basicSettings.language" placeholder="请选择语言">
                <el-option label="简体中文" value="zh-CN" />
                <el-option label="English" value="en-US" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="saveBasicSettings" :loading="saving">
                保存基本设置
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-tab-pane>

      <!-- 邮件设置 -->
      <el-tab-pane label="邮件设置" name="email">
        <el-card>
          <template #header>
            <div class="card-header">
              <h3>SMTP 邮件配置</h3>
              <el-button type="primary" size="small" @click="testEmailConnection">
                测试连接
              </el-button>
            </div>
          </template>
          <el-form :model="emailSettings" label-width="120px" class="settings-form">
            <el-form-item label="SMTP 服务器">
              <el-input v-model="emailSettings.smtpHost" placeholder="例如：smtp.gmail.com" />
            </el-form-item>
            <el-form-item label="SMTP 端口">
              <el-input-number v-model="emailSettings.smtpPort" :min="1" :max="65535" />
            </el-form-item>
                    <el-form-item label="加密方式">
          <el-radio-group v-model="emailSettings.encryption">
            <el-radio value="none">无加密</el-radio>
            <el-radio value="tls">TLS</el-radio>
            <el-radio value="ssl">SSL</el-radio>
          </el-radio-group>
        </el-form-item>
            <el-form-item label="发送邮箱">
              <el-input v-model="emailSettings.fromEmail" placeholder="请输入发送邮箱" />
            </el-form-item>
            <el-form-item label="发送者名称">
              <el-input v-model="emailSettings.fromName" placeholder="请输入发送者名称" />
            </el-form-item>
            <el-form-item label="用户名">
              <el-input v-model="emailSettings.username" placeholder="请输入SMTP用户名" />
            </el-form-item>
            <el-form-item label="密码">
              <el-input
                v-model="emailSettings.password"
                type="password"
                placeholder="请输入SMTP密码"
                show-password
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="saveEmailSettings" :loading="saving">
                保存邮件设置
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-tab-pane>

      <!-- 通知设置 -->
      <el-tab-pane label="通知设置" name="notification">
        <el-card>
          <template #header>
            <h3>通知渠道配置</h3>
          </template>
          <el-form :model="notificationSettings" label-width="120px" class="settings-form">
            <el-form-item label="邮件通知">
              <el-switch v-model="notificationSettings.emailEnabled" />
              <span class="setting-desc">启用邮件预警通知</span>
            </el-form-item>
            <el-form-item label="站内消息">
              <el-switch v-model="notificationSettings.internalEnabled" />
              <span class="setting-desc">启用站内消息通知</span>
            </el-form-item>
            <el-form-item label="微信通知" v-if="false">
              <el-switch v-model="notificationSettings.wechatEnabled" />
              <span class="setting-desc">启用微信企业号通知（企业版功能）</span>
            </el-form-item>
            <el-form-item label="钉钉通知" v-if="false">
              <el-switch v-model="notificationSettings.dingTalkEnabled" />
              <span class="setting-desc">启用钉钉机器人通知（企业版功能）</span>
            </el-form-item>
            <el-form-item label="通知频率限制">
              <el-select v-model="notificationSettings.frequency" placeholder="请选择通知频率">
                <el-option label="实时通知" value="realtime" />
                <el-option label="每5分钟汇总" value="5min" />
                <el-option label="每15分钟汇总" value="15min" />
                <el-option label="每小时汇总" value="1hour" />
              </el-select>
            </el-form-item>
            <el-form-item label="静默时间段">
              <el-time-picker
                v-model="notificationSettings.silentPeriod"
                is-range
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                format="HH:mm"
                value-format="HH:mm"
              />
              <div class="setting-desc">在此时间段内不发送通知</div>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="saveNotificationSettings" :loading="saving">
                保存通知设置
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-tab-pane>

      <!-- 数据设置 -->
      <el-tab-pane label="数据设置" name="data">
        <el-card>
          <template #header>
            <h3>数据采集与存储配置</h3>
          </template>
          <el-form :model="dataSettings" label-width="140px" class="settings-form">
            <el-form-item label="数据保留期限">
              <el-select v-model="dataSettings.retentionPeriod" placeholder="请选择数据保留期限">
                <el-option label="30天" value="30" />
                <el-option label="90天" value="90" />
                <el-option label="180天" value="180" />
                <el-option label="1年" value="365" />
                <el-option label="永久保留" value="forever" />
              </el-select>
              <div class="setting-desc">超过期限的数据将被自动清理</div>
            </el-form-item>
            <el-form-item label="最大并发采集任务">
              <el-input-number v-model="dataSettings.maxConcurrentTasks" :min="1" :max="100" />
              <div class="setting-desc">同时运行的数据采集任务数量限制</div>
            </el-form-item>
            <el-form-item label="请求超时时间">
              <el-input-number v-model="dataSettings.requestTimeout" :min="5" :max="300" />
              <span class="unit">秒</span>
              <div class="setting-desc">API请求的最大等待时间</div>
            </el-form-item>
            <el-form-item label="重试次数">
              <el-input-number v-model="dataSettings.retryCount" :min="0" :max="10" />
              <div class="setting-desc">请求失败时的重试次数</div>
            </el-form-item>
            <el-form-item label="数据缓存">
              <el-switch v-model="dataSettings.cacheEnabled" />
              <span class="setting-desc">启用数据缓存以提高查询性能</span>
            </el-form-item>
            <el-form-item label="缓存过期时间" v-if="dataSettings.cacheEnabled">
              <el-input-number v-model="dataSettings.cacheExpiry" :min="1" :max="1440" />
              <span class="unit">分钟</span>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="saveDataSettings" :loading="saving">
                保存数据设置
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-tab-pane>

      <!-- 安全设置 -->
      <el-tab-pane label="安全设置" name="security">
        <el-card>
          <template #header>
            <h3>安全与权限配置</h3>
          </template>
          <el-form :model="securitySettings" label-width="140px" class="settings-form">
            <el-form-item label="会话超时时间">
              <el-input-number v-model="securitySettings.sessionTimeout" :min="30" :max="1440" />
              <span class="unit">分钟</span>
              <div class="setting-desc">用户无操作时的自动退出时间</div>
            </el-form-item>
            <el-form-item label="密码强度要求">
              <el-checkbox-group v-model="securitySettings.passwordRules">
                          <el-checkbox value="minLength">最少8位字符</el-checkbox>
          <el-checkbox value="requireUppercase">包含大写字母</el-checkbox>
          <el-checkbox value="requireLowercase">包含小写字母</el-checkbox>
          <el-checkbox value="requireNumbers">包含数字</el-checkbox>
          <el-checkbox value="requireSpecialChars">包含特殊字符</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <el-form-item label="登录失败锁定">
              <el-switch v-model="securitySettings.loginLockEnabled" />
              <span class="setting-desc">连续登录失败后锁定账户</span>
            </el-form-item>
            <el-form-item label="最大失败次数" v-if="securitySettings.loginLockEnabled">
              <el-input-number v-model="securitySettings.maxLoginAttempts" :min="3" :max="10" />
            </el-form-item>
            <el-form-item label="锁定时间" v-if="securitySettings.loginLockEnabled">
              <el-input-number v-model="securitySettings.lockDuration" :min="5" :max="1440" />
              <span class="unit">分钟</span>
            </el-form-item>
            <el-form-item label="IP白名单">
              <el-input
                v-model="securitySettings.ipWhitelist"
                type="textarea"
                :rows="3"
                placeholder="每行一个IP地址或IP段，例如：***********/24"
              />
              <div class="setting-desc">留空表示允许所有IP访问</div>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="saveSecuritySettings" :loading="saving">
                保存安全设置
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-tab-pane>

      <!-- 系统信息 -->
      <el-tab-pane label="系统信息" name="info">
        <el-card>
          <template #header>
            <h3>系统运行状态</h3>
          </template>
          <div class="system-info">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="系统版本">{{ systemInfo.version }}</el-descriptions-item>
              <el-descriptions-item label="运行时间">{{ systemInfo.uptime }}</el-descriptions-item>
              <el-descriptions-item label="服务器时间">{{ systemInfo.serverTime }}</el-descriptions-item>
              <el-descriptions-item label="数据库版本">{{ systemInfo.dbVersion }}</el-descriptions-item>
              <el-descriptions-item label="PHP版本">{{ systemInfo.phpVersion }}</el-descriptions-item>
              <el-descriptions-item label="内存使用">{{ systemInfo.memoryUsage }}</el-descriptions-item>
              <el-descriptions-item label="磁盘使用">{{ systemInfo.diskUsage }}</el-descriptions-item>
              <el-descriptions-item label="CPU使用率">{{ systemInfo.cpuUsage }}</el-descriptions-item>
            </el-descriptions>
          </div>
        </el-card>

        <el-card style="margin-top: 20px">
          <template #header>
            <h3>许可证信息</h3>
          </template>
          <div class="license-info">
            <el-descriptions :column="1" border>
              <el-descriptions-item label="许可证类型">{{ licenseInfo.type }}</el-descriptions-item>
              <el-descriptions-item label="授权公司">{{ licenseInfo.company }}</el-descriptions-item>
              <el-descriptions-item label="到期时间">{{ licenseInfo.expiry }}</el-descriptions-item>
              <el-descriptions-item label="最大用户数">{{ licenseInfo.maxUsers }}</el-descriptions-item>
              <el-descriptions-item label="当前用户数">{{ licenseInfo.currentUsers }}</el-descriptions-item>
            </el-descriptions>
          </div>
        </el-card>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

// 响应式数据
const activeTab = ref('basic')
const saving = ref(false)

// 基本设置
const basicSettings = ref({
  systemName: '电商市场动态监测系统',
  version: 'v1.0.0',
  description: '智能电商市场动态监测与分析系统，实时监控竞品价格，智能分析市场动态',
  companyName: '',
  contactEmail: '',
  timezone: 'Asia/Shanghai',
  language: 'zh-CN'
})

// 邮件设置
const emailSettings = ref({
  smtpHost: '',
  smtpPort: 587,
  encryption: 'tls',
  fromEmail: '',
  fromName: '',
  username: '',
  password: ''
})

// 通知设置
const notificationSettings = ref({
  emailEnabled: true,
  internalEnabled: true,
  wechatEnabled: false,
  dingTalkEnabled: false,
  frequency: 'realtime',
  silentPeriod: ['22:00', '08:00']
})

// 数据设置
const dataSettings = ref({
  retentionPeriod: '90',
  maxConcurrentTasks: 10,
  requestTimeout: 30,
  retryCount: 3,
  cacheEnabled: true,
  cacheExpiry: 60
})

// 安全设置
const securitySettings = ref({
  sessionTimeout: 120,
  passwordRules: ['minLength', 'requireNumbers'],
  loginLockEnabled: true,
  maxLoginAttempts: 5,
  lockDuration: 30,
  ipWhitelist: ''
})

// 系统信息
const systemInfo = ref({
  version: 'v1.0.0',
  uptime: '7天 12小时 35分钟',
  serverTime: new Date().toLocaleString(),
  dbVersion: 'MySQL 8.0.28',
  phpVersion: 'PHP 8.1.0',
  memoryUsage: '256MB / 512MB (50%)',
  diskUsage: '2.1GB / 10GB (21%)',
  cpuUsage: '15%'
})

// 许可证信息
const licenseInfo = ref({
  type: '标准版',
  company: '示例公司',
  expiry: '2025-12-31',
  maxUsers: 50,
  currentUsers: 8
})

// 方法
const saveBasicSettings = async () => {
  saving.value = true
  try {
    // TODO: 调用API保存基本设置
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('基本设置保存成功')
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

const saveEmailSettings = async () => {
  saving.value = true
  try {
    // TODO: 调用API保存邮件设置
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('邮件设置保存成功')
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

const saveNotificationSettings = async () => {
  saving.value = true
  try {
    // TODO: 调用API保存通知设置
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('通知设置保存成功')
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

const saveDataSettings = async () => {
  saving.value = true
  try {
    // TODO: 调用API保存数据设置
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('数据设置保存成功')
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

const saveSecuritySettings = async () => {
  saving.value = true
  try {
    // TODO: 调用API保存安全设置
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('安全设置保存成功')
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

const testEmailConnection = async () => {
  try {
    // TODO: 调用API测试邮件连接
    await new Promise(resolve => setTimeout(resolve, 2000))
    ElMessage.success('邮件服务器连接测试成功')
  } catch (error) {
    ElMessage.error('邮件服务器连接失败')
  }
}

const loadSettings = async () => {
  try {
    // TODO: 调用API加载设置
    // 更新系统时间
    setInterval(() => {
      systemInfo.value.serverTime = new Date().toLocaleString()
    }, 1000)
  } catch (error) {
    ElMessage.error('加载设置失败')
  }
}

onMounted(() => {
  loadSettings()
})
</script>

<style scoped lang="scss">
.system-settings {
  .page-header {
    margin-bottom: 24px;
    
    h1 {
      font-size: 24px;
      font-weight: 600;
      margin-bottom: 8px;
      color: #303133;
    }
    
    p {
      color: #606266;
      margin: 0;
    }
  }
  
  .settings-tabs {
    :deep(.el-tabs__content) {
      padding-top: 20px;
    }
  }
  
  .settings-form {
    max-width: 600px;
    
    .setting-desc {
      font-size: 12px;
      color: #909399;
      margin-left: 12px;
    }
    
    .unit {
      margin-left: 8px;
      color: #909399;
      font-size: 12px;
    }
  }
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
    }
  }
  
  .system-info {
    :deep(.el-descriptions__body) {
      background-color: #fafafa;
      
      .el-descriptions__table {
        border-collapse: separate;
        border-spacing: 0;
        
        .el-descriptions__cell {
          padding: 12px 16px;
        }
      }
    }
  }
  
  .license-info {
    :deep(.el-descriptions__body) {
      background-color: #f0f9ff;
    }
  }
}
</style> 