<template>
  <div class="product-history-detail">
    <!-- 页面头部 -->
    <div class="page-header">
      <el-page-header @back="goBack">
        <template #content>
          <div class="header-content">
            <h1 class="page-title">产品历史详情</h1>
            <p class="page-description" v-if="productDetail">
              {{ productDetail.title }} - {{ formatDateTime(productDetail.collected_at) }}
            </p>
          </div>
        </template>
      </el-page-header>
    </div>

    <!-- 加载状态 -->
    <el-skeleton v-if="loading" :rows="10" animated />

    <!-- 产品详情内容 -->
    <div v-else-if="productDetail" class="detail-content">
      <!-- 基本信息卡片 -->
      <el-card class="info-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <h3>基本信息</h3>
            <el-tag type="info" size="small">{{ formatDateTime(productDetail.collected_at) }}</el-tag>
          </div>
        </template>
        
        <el-row :gutter="20">
          <el-col :span="8" style="display: flex; justify-content: center; align-items: center;">
            <el-image
              v-if="productDetail.product_image"
              :src="productDetail.product_image"
              fit="contain"
              style="width: 100%; height: 300px; border-radius: 8px;"
            />
            <div v-else class="no-image-placeholder">
              <el-icon size="60"><Picture /></el-icon>
              <span>暂无图片</span>
            </div>
          </el-col>
          <el-col :span="16">
            <div class="detail-info">
              <div class="info-row">
                <label>商品标题:</label>
                <span>{{ productDetail.title }}</span>
              </div>
              <div class="info-row">
                <label>商品ID:</label>
                <span>{{ productDetail.item_id }}</span>
              </div>
              <div class="info-row">
                <label>数据源:</label>
                <el-tag type="primary">{{ productDetail.data_source }}</el-tag>
              </div>
              <div class="info-row">
                <label>价格:</label>
                <span class="price">¥{{ formatPrice(productDetail.price) }}</span>
                <span v-if="productDetail.original_price && productDetail.original_price > productDetail.price" class="original-price">
                  原价: ¥{{ formatPrice(productDetail.original_price) }}
                </span>
              </div>
              <div class="info-row">
                <label>库存:</label>
                <span :class="stockClass(productDetail.stock)">{{ productDetail.stock || 0 }}</span>
              </div>
              <div class="info-row">
                <label>状态:</label>
                <el-tag :type="getStatusTagType(productDetail.status)">{{ getStatusText(productDetail.status) }}</el-tag>
              </div>
              <div class="info-row" v-if="productDetail.brand">
                <label>品牌:</label>
                <span>{{ productDetail.brand }}</span>
              </div>
              <div class="info-row" v-if="productDetail.category">
                <label>分类:</label>
                <span>{{ productDetail.category }}</span>
              </div>
              <div class="info-row" v-if="productDetail.shop_id">
                <label>店铺ID:</label>
                <span>{{ productDetail.shop_id }}</span>
              </div>
              <div class="info-row" v-if="productDetail.delivery_location">
                <label>发货地:</label>
                <span>{{ productDetail.delivery_location }}</span>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 商品描述卡片 -->
      <el-card v-if="productDetail.description" class="info-card" shadow="hover">
        <template #header>
          <h3>商品描述</h3>
        </template>
        <div class="description-content">
          {{ productDetail.description }}
        </div>
      </el-card>

      <!-- 商品属性卡片 -->
      <el-card v-if="productDetail.props && hasProps(productDetail.props)" class="info-card" shadow="hover">
        <template #header>
          <h3>商品属性</h3>
        </template>
        <div class="props-grid">
          <template v-if="Array.isArray(productDetail.props)">
            <div v-for="(prop, index) in productDetail.props" :key="index" class="prop-item">
              <span class="prop-name">{{ prop.prop_name || prop.name }}:</span>
              <div class="prop-values">
                <el-tag 
                  v-for="(value, vIndex) in (prop.values || [prop.value])" 
                  :key="vIndex" 
                  size="small" 
                  class="prop-value-tag"
                >
                  {{ value.name || value }}
                </el-tag>
              </div>
            </div>
          </template>
          <template v-else>
            <div v-for="(value, key) in productDetail.props" :key="key" class="prop-item">
              <span class="prop-name">{{ key }}:</span>
              <div class="prop-values">
                <el-tag size="small" class="prop-value-tag">{{ value }}</el-tag>
              </div>
            </div>
          </template>
        </div>
      </el-card>

      <!-- SKU详情卡片 -->
      <el-card v-if="productDetail.skus && productDetail.skus.length > 0" class="info-card" shadow="hover">
        <template #header>
          <h3>SKU 详情 ({{ productDetail.skus.length }}个)</h3>
        </template>
        <el-table :data="productDetail.skus" stripe border>
          <el-table-column prop="sku_id" label="SKU ID" width="150" show-overflow-tooltip />
          <el-table-column prop="name" label="SKU 名称" min-width="200" show-overflow-tooltip />
          <el-table-column prop="price" label="价格" width="100">
            <template #default="{ row }">
              <span v-if="row.price">¥{{ formatPrice(row.price) }}</span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="sub_price" label="到手价" width="100">
            <template #default="{ row }">
              <span v-if="row.sub_price">¥{{ formatPrice(row.sub_price) }}</span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="official_guide_price" label="官方指导价" width="120">
            <template #default="{ row }">
              <span v-if="row.official_guide_price">¥{{ formatPrice(row.official_guide_price) }}</span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="promotion_deviation_rate" label="促销价偏离率" width="130">
            <template #default="{ row }">
              <span v-if="row.promotion_deviation_rate !== null" :class="getDeviationRateClass(row.promotion_deviation_rate, true)">
                {{ row.promotion_deviation_rate }}%
              </span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="channel_price_deviation_rate" label="渠道价偏离率" width="130">
            <template #default="{ row }">
              <span v-if="row.channel_price_deviation_rate !== null" :class="getDeviationRateClass(row.channel_price_deviation_rate, false)">
                {{ row.channel_price_deviation_rate }}%
              </span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="quantity" label="库存" width="80">
            <template #default="{ row }">
              <span :class="stockClass(row.quantity)">{{ row.quantity || 0 }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusTagType(row.status)" size="small">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="spec_name" label="规格" min-width="150" show-overflow-tooltip />
          <el-table-column prop="sku_url" label="操作" width="100">
            <template #default="{ row }">
              <el-button v-if="row.sku_url" text size="small" type="primary" @click="openUrl(row.sku_url)">
                查看
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <!-- 其他技术信息 -->
      <el-card class="info-card" shadow="hover">
        <template #header>
          <h3>技术信息</h3>
        </template>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="info-row">
              <label>监控任务ID:</label>
              <span>{{ productDetail.monitoring_task_id }}</span>
            </div>
            <div class="info-row" v-if="productDetail.code">
              <label>API状态码:</label>
              <el-tag :type="productDetail.code === 200 ? 'success' : 'danger'" size="small">
                {{ productDetail.code }}
              </el-tag>
            </div>
            <div class="info-row" v-if="productDetail.has_sku !== null">
              <label>是否有SKU:</label>
              <el-tag :type="productDetail.has_sku ? 'success' : 'info'" size="small">
                {{ productDetail.has_sku ? '是' : '否' }}
              </el-tag>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-row" v-if="productDetail.category_id">
              <label>分类ID:</label>
              <span>{{ productDetail.category_id }}</span>
            </div>
            <div class="info-row" v-if="productDetail.category_path">
              <label>分类路径:</label>
              <span>{{ productDetail.category_path }}</span>
            </div>
            <div class="info-row" v-if="productDetail.item_type">
              <label>商品类型:</label>
              <el-tag size="small" type="info">{{ productDetail.item_type }}</el-tag>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <el-button @click="goBack">返回列表</el-button>
        <el-button v-if="productDetail.url" type="primary" @click="openUrl(productDetail.url)">
          访问商品页面
        </el-button>
      </div>
    </div>

    <!-- 无数据状态 -->
    <el-empty v-else description="未找到产品历史详情" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Picture } from '@element-plus/icons-vue'
import productDataApi from '@/api/productData'

const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(true)
const productDetail = ref(null)

// 路由参数
const taskId = route.params.taskId
const itemId = route.params.itemId
const historyId = route.params.historyId

// 方法
const fetchProductHistoryDetail = async () => {
  loading.value = true
  try {
    const response = await productDataApi.getProductHistoryDetail(historyId)
    if (response.data) {
      productDetail.value = response.data
    } else {
      ElMessage.error(response?.message || '加载产品历史详情失败')
    }
  } catch (error) {
    console.error('加载产品历史详情失败:', error)
    ElMessage.error('加载产品历史详情失败：' + (error.message || '网络错误'))
  } finally {
    loading.value = false
  }
}

const goBack = () => {
  router.back()
}

function openUrl(url) {
  window.open(url, '_blank')
}

function formatPrice(price) {
  if (price === null || price === undefined) return 'N/A'
  return Number(price).toFixed(2)
}

function formatDateTime(dateTime) {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

function getStatusTagType(status) {
  if (status === 1 || status === true || status === '上架' || status === 'active') return 'success'
  if (status === 0 || status === false || status === '下架' || status === 'inactive') return 'danger'
  return 'info'
}

function getStatusText(status) {
  if (status === 1 || status === true || status === '上架' || status === 'active') return '上架'
  if (status === 0 || status === false || status === '下架' || status === 'inactive') return '下架'
  return '未知'
}

function stockClass(stock) {
  const qty = parseInt(stock || 0)
  if (qty === 0) return 'text-red-600'
  if (qty < 10) return 'text-orange-600'
  return 'text-green-600'
}

function hasProps(props) {
  if (!props) return false
  if (Array.isArray(props)) return props.length > 0
  return Object.keys(props).length > 0
}

function getDeviationRateClass(rate, isPromo) {
  if (rate === null || rate === undefined) return '';
  // 促销价偏离率：正数（价格高于促销价）是坏事，用红色。但一般促销价低于原价，所以rate是正数，是好事，用绿色。
  // 渠道价偏离率：正数（渠道价低于官方价）是好事，用绿色。
  if (isPromo) {
    return rate > 0 ? 'text-green-500 font-semibold' : 'text-red-500 font-semibold';
  }
  return rate > 0 ? 'text-green-500 font-semibold' : 'text-red-500 font-semibold';
}

// 生命周期
onMounted(() => {
  if (historyId) {
    fetchProductHistoryDetail()
  } else {
    loading.value = false
  }
})
</script>

<style scoped>
.product-history-detail {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 20px;
}

.header-content {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-description {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.detail-content {
  max-width: 1200px;
  margin: 0 auto;
}

.info-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.no-image-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  background: #f5f7fa;
  border-radius: 8px;
  color: #909399;
}

.detail-info {
  padding-left: 20px;
}

.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  min-height: 24px;
}

.info-row label {
  font-weight: 600;
  color: #606266;
  width: 120px;
  flex-shrink: 0;
}

.info-row span {
  color: #303133;
}

.price {
  font-size: 18px;
  font-weight: 600;
  color: #e6a23c;
  margin-right: 10px;
}

.original-price {
  font-size: 14px;
  color: #909399;
  text-decoration: line-through;
}

.text-red-600 {
  color: #f56565;
}

.text-orange-600 {
  color: #ed8936;
}

.text-green-600 {
  color: #38a169;
}

.description-content {
  line-height: 1.6;
  color: #606266;
}

.props-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.prop-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.prop-name {
  font-weight: 600;
  color: #606266;
}

.prop-values {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.prop-value-tag {
  margin: 0;
}

.action-buttons {
  text-align: center;
  margin-top: 30px;
  padding: 20px;
}

.action-buttons .el-button {
  margin: 0 10px;
}
</style> 