<template>
  <div class="task-group-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">任务组管理</h1>
        <p class="page-description">管理监控任务组，组织和分类您的监控任务</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="showCreateDialog" :loading="loading">
          <el-icon><Plus /></el-icon>
          新增任务组
        </el-button>
        <el-button @click="refreshData" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <div class="stats-card">
        <div class="stats-icon primary">
          <el-icon><FolderOpened /></el-icon>
        </div>
        <div class="stats-content">
          <div class="stats-number">{{ taskGroups.length }}</div>
          <div class="stats-label">任务组总数</div>
        </div>
      </div>
      <div class="stats-card">
        <div class="stats-icon success">
          <el-icon><Monitor /></el-icon>
        </div>
        <div class="stats-content">
          <div class="stats-number">{{ totalTasks }}</div>
          <div class="stats-label">总任务数</div>
        </div>
      </div>
      <div class="stats-card">
        <div class="stats-icon warning">
          <el-icon><Timer /></el-icon>
        </div>
        <div class="stats-content">
          <div class="stats-number">{{ activeGroups }}</div>
          <div class="stats-label">活跃任务组</div>
        </div>
      </div>
      <div class="stats-card">
        <div class="stats-icon info">
          <el-icon><Calendar /></el-icon>
        </div>
        <div class="stats-content">
          <div class="stats-number">{{ recentGroups }}</div>
          <div class="stats-label">本周新增</div>
        </div>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="filter-section">
      <el-input
        v-model="searchKeyword"
        placeholder="搜索任务组名称或描述..."
        prefix-icon="Search"
        clearable
        style="width: 300px; margin-right: 16px;"
        @input="handleSearch"
      />
      <el-select
        v-model="statusFilter"
        placeholder="状态筛选"
        clearable
        style="width: 150px;"
        @change="handleFilter"
      >
        <el-option label="全部" value="" />
        <el-option label="活跃" value="active" />
        <el-option label="暂停" value="paused" />
        <el-option label="已完成" value="completed" />
      </el-select>
    </div>

    <!-- 任务组列表 -->
    <div class="task-groups-section">
      <div class="task-groups-grid" v-loading="loading">
        <el-card
          v-for="group in filteredGroups"
          :key="group.id"
          class="group-card"
          shadow="hover"
          @click="viewGroupDetails(group)"
        >
          <template #header>
            <div class="group-header">
              <div class="group-title">
                <h3>{{ group.name }}</h3>
                <el-tag :type="getStatusTagType(group.status)" size="small">
                  {{ getStatusLabel(group.status) }}
                </el-tag>
              </div>
              <el-dropdown @command="(cmd) => handleGroupAction(cmd, group)" @click.stop="">
                <el-button size="small" text>
                  <el-icon><MoreFilled /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="edit">编辑</el-dropdown-item>
                    <el-dropdown-item command="view">查看详情</el-dropdown-item>
                    <el-dropdown-item command="tasks">管理任务</el-dropdown-item>
                    <el-dropdown-item command="duplicate">复制</el-dropdown-item>
                    <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
          
          <div class="group-content">
            <p class="group-description">{{ group.description || '暂无描述' }}</p>
            
            <div class="group-stats">
              <div class="stat-item">
                <el-icon><Monitor /></el-icon>
                <span>{{ group.monitoring_tasks_count || 0 }} 个任务</span>
              </div>
              <div class="stat-item">
                <el-icon><Clock /></el-icon>
                <span>{{ formatDateTime(group.updated_at) }}</span>
              </div>
            </div>

            <!-- 任务预览 -->
            <div class="tasks-preview" v-if="group.latest_tasks && group.latest_tasks.length > 0">
              <div class="preview-title">最近任务:</div>
              <div class="task-tags">
                <el-tag
                  v-for="task in group.latest_tasks.slice(0, 3)"
                  :key="task.id"
                  size="small"
                  class="task-tag"
                >
                  {{ task.name }}
                </el-tag>
                <el-tag v-if="group.latest_tasks.length > 3" size="small" type="info">
                  +{{ group.latest_tasks.length - 3 }} 更多
                </el-tag>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 空状态 -->
        <div class="empty-state" v-if="!loading && filteredGroups.length === 0">
          <el-empty description="暂无任务组">
            <el-button type="primary" @click="showCreateDialog">创建第一个任务组</el-button>
          </el-empty>
        </div>
      </div>
    </div>

    <!-- 创建/编辑任务组对话框 -->
    <el-dialog
      :title="isEdit ? '编辑任务组' : '新增任务组'"
      v-model="dialogVisible"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form :model="groupForm" :rules="groupRules" ref="groupFormRef" label-width="100px">
        <el-form-item label="任务组名称" prop="name">
          <el-input v-model="groupForm.name" placeholder="请输入任务组名称" />
        </el-form-item>
        <el-form-item label="任务组描述" prop="description">
          <el-input
            v-model="groupForm.description"
            type="textarea"
            :rows="4"
            placeholder="请输入任务组描述"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="groupForm.status" placeholder="请选择状态" style="width: 100%">
            <el-option label="活跃" value="active" />
            <el-option label="暂停" value="paused" />
            <el-option label="已完成" value="completed" />
          </el-select>
        </el-form-item>
        <el-form-item label="优先级">
          <el-select v-model="groupForm.priority" placeholder="请选择优先级" style="width: 100%">
            <el-option label="高" value="high" />
            <el-option label="中" value="medium" />
            <el-option label="低" value="low" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 任务组详情对话框 -->
    <el-dialog
      title="任务组详情"
      v-model="detailDialogVisible"
      width="800px"
      @close="selectedGroup = null"
    >
      <div v-if="selectedGroup">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="任务组名称">{{ selectedGroup.name }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusTagType(selectedGroup.status)">
              {{ getStatusLabel(selectedGroup.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="优先级">{{ getPriorityLabel(selectedGroup.priority) }}</el-descriptions-item>
          <el-descriptions-item label="任务数量">{{ selectedGroup.monitoring_tasks_count || 0 }}</el-descriptions-item>
          <el-descriptions-item label="创建时间" :span="2">{{ formatDateTime(selectedGroup.created_at) }}</el-descriptions-item>
          <el-descriptions-item label="更新时间" :span="2">{{ formatDateTime(selectedGroup.updated_at) }}</el-descriptions-item>
          <el-descriptions-item label="描述" :span="2">
            {{ selectedGroup.description || '暂无描述' }}
          </el-descriptions-item>
        </el-descriptions>

        <!-- 关联任务列表 -->
        <div class="related-tasks" v-if="selectedGroup.monitoring_tasks && selectedGroup.monitoring_tasks.length > 0">
          <h4 style="margin: 20px 0 10px 0;">关联任务</h4>
          <el-table :data="selectedGroup.monitoring_tasks" stripe>
            <el-table-column prop="name" label="任务名称" />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getStatusTagType(row.status)" size="small">
                  {{ getStatusLabel(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="frequency_type" label="执行频率" width="120">
              <template #default="{ row }">
                {{ getFrequencyLabel(row.frequency_type) }}
              </template>
            </el-table-column>
            <el-table-column prop="updated_at" label="更新时间" width="160">
              <template #default="{ row }">
                {{ formatDateTime(row.updated_at) }}
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'
import {
  Plus,
  Refresh,
  FolderOpened,
  Monitor,
  Timer,
  Calendar,
  MoreFilled,
  Clock
} from '@element-plus/icons-vue'

// 路由
const router = useRouter()

// 响应式数据
const loading = ref(false)
const submitLoading = ref(false)
const dialogVisible = ref(false)
const detailDialogVisible = ref(false)
const isEdit = ref(false)
const searchKeyword = ref('')
const statusFilter = ref('')

// 数据
const taskGroups = ref([])
const selectedGroup = ref(null)

// 表单
const groupFormRef = ref()
const groupForm = ref({
  id: null,
  name: '',
  description: '',
  status: 'active',
  priority: 'medium'
})

const groupRules = {
  name: [
    { required: true, message: '请输入任务组名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ]
}

// 计算属性
const filteredGroups = computed(() => {
  let groups = taskGroups.value
  
  // 搜索过滤
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    groups = groups.filter(group => 
      group.name.toLowerCase().includes(keyword) ||
      (group.description && group.description.toLowerCase().includes(keyword))
    )
  }
  
  // 状态过滤
  if (statusFilter.value) {
    groups = groups.filter(group => group.status === statusFilter.value)
  }
  
  return groups
})

const totalTasks = computed(() => {
  return taskGroups.value.reduce((sum, group) => sum + (group.monitoring_tasks_count || 0), 0)
})

const activeGroups = computed(() => {
  return taskGroups.value.filter(group => group.status === 'active').length
})

const recentGroups = computed(() => {
  const oneWeekAgo = new Date()
  oneWeekAgo.setDate(oneWeekAgo.getDate() - 7)
  return taskGroups.value.filter(group => new Date(group.created_at) > oneWeekAgo).length
})

// 方法
const fetchTaskGroups = async () => {
  try {
    loading.value = true
    // 模拟API调用 - 实际项目中替换为真实API
    const response = await mockApi.getTaskGroups()
    taskGroups.value = response.data || []
  } catch (error) {
    console.error('获取任务组失败:', error)
    ElMessage.error('获取任务组失败')
  } finally {
    loading.value = false
  }
}

const showCreateDialog = () => {
  isEdit.value = false
  dialogVisible.value = true
  resetForm()
}

const showEditDialog = (group) => {
  isEdit.value = true
  dialogVisible.value = true
  groupForm.value = { ...group }
}

const resetForm = () => {
  groupForm.value = {
    id: null,
    name: '',
    description: '',
    status: 'active',
    priority: 'medium'
  }
  groupFormRef.value?.resetFields()
}

const handleSubmit = async () => {
  try {
    await groupFormRef.value.validate()
    submitLoading.value = true
    
    if (isEdit.value) {
      await mockApi.updateTaskGroup(groupForm.value.id, groupForm.value)
      ElMessage.success('任务组更新成功')
    } else {
      await mockApi.createTaskGroup(groupForm.value)
      ElMessage.success('任务组创建成功')
    }
    
    dialogVisible.value = false
    await fetchTaskGroups()
  } catch (error) {
    console.error('操作失败:', error)
    ElMessage.error(`${isEdit.value ? '更新' : '创建'}任务组失败`)
  } finally {
    submitLoading.value = false
  }
}

const viewGroupDetails = (group) => {
  selectedGroup.value = group
  detailDialogVisible.value = true
}

const handleGroupAction = async (command, group) => {
  switch (command) {
    case 'edit':
      showEditDialog(group)
      break
    case 'view':
      viewGroupDetails(group)
      break
    case 'tasks':
      router.push(`/task-management?group=${group.id}`)
      break
    case 'duplicate':
      const duplicateGroup = {
        ...group,
        name: `${group.name} (副本)`,
        id: null
      }
      groupForm.value = duplicateGroup
      isEdit.value = false
      dialogVisible.value = true
      break
    case 'delete':
      ElMessageBox.confirm('确定要删除这个任务组吗？删除后关联的任务将移到"未分组"中。', '确认删除', {
        type: 'warning'
      }).then(async () => {
        try {
          await mockApi.deleteTaskGroup(group.id)
          ElMessage.success('任务组已删除')
          await fetchTaskGroups()
        } catch (error) {
          ElMessage.error('删除任务组失败')
        }
      })
      break
  }
}

const refreshData = () => {
  fetchTaskGroups()
}

const handleSearch = () => {
  // 搜索逻辑通过计算属性实现
}

const handleFilter = () => {
  // 过滤逻辑通过计算属性实现
}

// 辅助方法
const getStatusLabel = (status) => {
  const labels = {
    active: '活跃',
    paused: '暂停',
    completed: '已完成'
  }
  return labels[status] || status
}

const getStatusTagType = (status) => {
  const types = {
    active: 'success',
    paused: 'warning',
    completed: 'info'
  }
  return types[status] || ''
}

const getPriorityLabel = (priority) => {
  const labels = {
    high: '高',
    medium: '中',
    low: '低'
  }
  return labels[priority] || priority
}

const getFrequencyLabel = (frequency) => {
  const labels = {
    hourly: '每小时',
    daily: '每日',
    weekly: '每周',
    monthly: '每月'
  }
  return labels[frequency] || frequency
}

const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString()
}

// 模拟API（实际项目中替换为真实API）
const mockApi = {
  async getTaskGroups() {
    // 模拟延迟
    await new Promise(resolve => setTimeout(resolve, 500))
    return {
      data: [
        {
          id: 1,
          name: '电子产品监控',
          description: '监控电子产品价格变动和库存情况',
          status: 'active',
          priority: 'high',
          monitoring_tasks_count: 15,
          created_at: '2024-01-01T10:00:00Z',
          updated_at: '2024-01-15T14:30:00Z',
          latest_tasks: [
            { id: 1, name: '手机价格监控' },
            { id: 2, name: '笔记本电脑监控' }
          ]
        },
        {
          id: 2,
          name: '服装类目监控',
          description: '服装品类的价格和促销活动监控',
          status: 'active',
          priority: 'medium',
          monitoring_tasks_count: 8,
          created_at: '2024-01-02T09:00:00Z',
          updated_at: '2024-01-16T11:20:00Z',
          latest_tasks: [
            { id: 3, name: '运动鞋价格监控' }
          ]
        }
      ]
    }
  },
  
  async createTaskGroup(data) {
    await new Promise(resolve => setTimeout(resolve, 300))
    return { success: true, data: { id: Date.now(), ...data } }
  },
  
  async updateTaskGroup(id, data) {
    await new Promise(resolve => setTimeout(resolve, 300))
    return { success: true, data: { id, ...data } }
  },
  
  async deleteTaskGroup(id) {
    await new Promise(resolve => setTimeout(resolve, 300))
    return { success: true }
  }
}

// 生命周期
onMounted(() => {
  fetchTaskGroups()
})
</script>

<style scoped>
.task-group-management {
  padding: 16px;
  background: #f8f9fa;
  min-height: 100%;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-content {
  flex: 1;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 4px 0;
}

.page-description {
  font-size: 14px;
  color: #7f8c8d;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stats-card {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
}

.stats-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.stats-icon.primary {
  background: linear-gradient(135deg, #409EFF, #66b3ff);
}

.stats-icon.success {
  background: linear-gradient(135deg, #67C23A, #85ce61);
}

.stats-icon.warning {
  background: linear-gradient(135deg, #E6A23C, #ebb563);
}

.stats-icon.info {
  background: linear-gradient(135deg, #909399, #b1b3b8);
}

.stats-content {
  flex: 1;
}

.stats-number {
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 4px;
}

.stats-label {
  font-size: 14px;
  color: #7f8c8d;
}

.filter-section {
  background: white;
  padding: 16px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  display: flex;
  align-items: center;
}

.task-groups-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.task-groups-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
}

.group-card {
  cursor: pointer;
  transition: all 0.3s ease;
}

.group-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.group-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.group-title {
  flex: 1;
}

.group-title h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.group-content {
  padding-top: 12px;
}

.group-description {
  color: #7f8c8d;
  font-size: 14px;
  margin-bottom: 16px;
  line-height: 1.5;
}

.group-stats {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 13px;
  color: #909399;
}

.tasks-preview {
  border-top: 1px solid #f0f0f0;
  padding-top: 12px;
}

.preview-title {
  font-size: 13px;
  color: #909399;
  margin-bottom: 8px;
}

.task-tags {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.task-tag {
  font-size: 12px;
}

.empty-state {
  grid-column: 1 / -1;
  text-align: center;
  padding: 60px 20px;
}

.Related-tasks {
  margin-top: 20px;
}

@media (max-width: 768px) {
  .task-groups-grid {
    grid-template-columns: 1fr;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .filter-section {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
}
</style> 