<template>
  <div class="competitor-task-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">竞品动态监测 - 任务管理</h1>
        <p class="page-description">创建和管理竞品监控任务，分析竞争对手价格策略和促销活动</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" :icon="Plus" @click="showCreateGroupDialog = true">
          创建监控组
        </el-button>
        <el-button :icon="Refresh" @click="loadCompetitorGroups">刷新</el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon">
                <el-icon color="#409EFF"><View /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ totalGroups }}</div>
                <div class="stats-label">监控组数量</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon">
                <el-icon color="#67C23A"><ShoppingBag /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ totalCompetitors }}</div>
                <div class="stats-label">竞品数量</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon">
                <el-icon color="#E6A23C"><TrendCharts /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ activeAnalysis }}</div>
                <div class="stats-label">活跃分析</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon">
                <el-icon color="#F56C6C"><Warning /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ priceAlerts }}</div>
                <div class="stats-label">价格异动</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 监控组列表 -->
    <el-card class="groups-card">
      <template #header>
        <div class="card-header">
          <span class="card-title">竞品监控组</span>
          <div class="header-tools">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索监控组名称..."
              :prefix-icon="Search"
              style="width: 250px; margin-right: 12px;"
              @input="handleSearch"
            />
            <el-select v-model="statusFilter" placeholder="状态筛选" style="width: 120px;">
              <el-option label="全部" value="" />
              <el-option label="运行中" value="running" />
              <el-option label="已暂停" value="paused" />
              <el-option label="已停止" value="stopped" />
            </el-select>
          </div>
        </div>
      </template>

      <el-table :data="filteredGroups" v-loading="loading" style="width: 100%">
        <el-table-column prop="name" label="监控组名称" min-width="200">
          <template #default="{ row }">
            <div class="group-name">
              <el-icon><View /></el-icon>
              <span>{{ row.name }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述" min-width="250" show-overflow-tooltip />
        <el-table-column prop="category" label="商品类目" width="120">
          <template #default="{ row }">
            <el-tag type="info" size="small">{{ row.category }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="competitor_count" label="竞品数量" width="100" align="center">
          <template #default="{ row }">
            <el-badge :value="row.competitor_count" class="competitor-count-badge">
              <el-icon><ShoppingBag /></el-icon>
            </el-badge>
          </template>
        </el-table-column>
        <el-table-column prop="data_source_name" label="数据源" width="180">
          <template #default="{ row }">
            <el-tag type="success" size="small">{{ row.data_source_name }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="分析指标" width="200">
          <template #default="{ row }">
            <div class="metrics-display">
              <el-tag v-for="metric in row.analysis_metrics" :key="metric" size="small" style="margin: 1px;">
                {{ getMetricText(metric) }}
              </el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="frequency" label="采集频率" width="120">
          <template #default="{ row }">
            <el-tag size="small">{{ getFrequencyText(row.frequency) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag 
              :type="getStatusTagType(row.status)" 
              size="small"
            >
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="last_analysis_at" label="最后分析" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.last_analysis_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="240" fixed="right">
          <template #default="{ row }">
            <el-button-group>
              <el-button size="small" @click="viewGroup(row)">查看</el-button>
              <el-button size="small" @click="editGroup(row)">编辑</el-button>
              <el-button size="small" @click="manageCompetitors(row)">管理竞品</el-button>
              <el-button 
                size="small" 
                :type="row.status === 'running' ? 'warning' : 'success'"
                @click="toggleGroupStatus(row)"
              >
                {{ row.status === 'running' ? '暂停' : '启动' }}
              </el-button>
              <el-button size="small" type="danger" @click="deleteGroup(row)">删除</el-button>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="totalItems"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 创建监控组对话框 -->
    <el-dialog
      v-model="showCreateGroupDialog"
      title="创建竞品监控组"
      width="800px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="createGroupFormRef"
        :model="createGroupForm"
        :rules="createGroupRules"
        label-width="120px"
      >
        <el-form-item label="监控组名称" prop="name">
          <el-input v-model="createGroupForm.name" placeholder="请输入监控组名称" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="createGroupForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入监控组描述"
          />
        </el-form-item>
        <el-form-item label="商品类目" prop="category">
          <el-select v-model="createGroupForm.category" placeholder="请选择商品类目" style="width: 100%">
            <el-option label="手机数码" value="mobile_digital" />
            <el-option label="家用电器" value="home_appliances" />
            <el-option label="服装鞋帽" value="clothing_shoes" />
            <el-option label="美妆护肤" value="beauty_skincare" />
            <el-option label="食品饮料" value="food_beverage" />
            <el-option label="运动户外" value="sports_outdoor" />
            <el-option label="其他" value="others" />
          </el-select>
        </el-form-item>
        <el-form-item label="数据源" prop="data_source_id">
          <el-select v-model="createGroupForm.data_source_id" placeholder="请选择数据源" style="width: 100%">
            <el-option
              v-for="source in dataSources"
              :key="source.id"
              :label="source.name"
              :value="source.id"
            >
              <div class="data-source-option">
                <span>{{ source.name }}</span>
                <el-tag size="small" type="info">{{ source.platform }}</el-tag>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="分析指标" prop="analysis_metrics">
          <el-checkbox-group v-model="createGroupForm.analysis_metrics">
            <el-checkbox value="price_trend">价格趋势</el-checkbox>
            <el-checkbox value="promotion_strategy">促销策略</el-checkbox>
            <el-checkbox value="discount_rate">折扣率分析</el-checkbox>
            <el-checkbox value="price_deviation">价格偏差</el-checkbox>
            <el-checkbox value="market_share">市场份额</el-checkbox>
            <el-checkbox value="sales_volume">销量分析</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="采集频率" prop="frequency">
          <el-select v-model="createGroupForm.frequency" placeholder="请选择采集频率">
            <el-option label="每小时" value="hourly" />
            <el-option label="每6小时" value="every_6_hours" />
            <el-option label="每12小时" value="every_12_hours" />
            <el-option label="每天" value="daily" />
            <el-option label="每周" value="weekly" />
          </el-select>
        </el-form-item>
        <el-form-item label="采集时间" prop="schedule_time" v-if="createGroupForm.frequency !== 'hourly'">
          <el-time-picker
            v-model="createGroupForm.schedule_time"
            placeholder="选择采集时间"
            format="HH:mm"
          />
        </el-form-item>
        <el-form-item label="我方产品" prop="own_products">
          <div class="own-products-section">
            <el-button @click="addOwnProduct" :icon="Plus" size="small">添加我方产品</el-button>
            <div class="product-rows" v-if="createGroupForm.own_products.length > 0">
              <div
                v-for="(product, index) in createGroupForm.own_products"
                :key="index"
                class="product-row"
              >
                <el-input
                  v-model="product.name"
                  placeholder="产品名称"
                  style="width: 200px"
                />
                <el-input
                  v-model="product.id"
                  placeholder="产品ID"
                  style="width: 150px"
                />
                <el-input
                  v-model="product.price"
                  placeholder="参考价格"
                  style="width: 120px"
                />
                <el-button
                  @click="removeOwnProduct(index)"
                  :icon="Delete"
                  size="small"
                  type="danger"
                />
              </div>
            </div>
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showCreateGroupDialog = false">取消</el-button>
          <el-button type="primary" @click="createGroup" :loading="submitting">创建</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 管理竞品对话框 -->
    <el-dialog
      v-model="showManageCompetitorsDialog"
      title="管理竞品"
      width="1000px"
      :close-on-click-modal="false"
    >
      <div class="competitors-management">
        <div class="management-header">
          <div class="header-info">
            <h3>{{ currentGroup?.name }} - 竞品管理</h3>
            <p>当前竞品数量: {{ competitors.length }}</p>
          </div>
          <div class="header-actions">
            <el-button type="primary" :icon="Plus" @click="showAddCompetitorDialog = true">
              添加竞品
            </el-button>
            <el-button :icon="Upload" @click="importCompetitors">批量导入</el-button>
          </div>
        </div>

        <el-table :data="competitors" style="width: 100%">
          <el-table-column prop="name" label="竞品名称" min-width="200">
            <template #default="{ row }">
              <div class="competitor-info">
                <el-image
                  :src="row.image"
                  :preview-src-list="[row.image]"
                  class="competitor-image"
                  fit="cover"
                />
                <div class="competitor-details">
                  <div class="competitor-name">{{ row.name }}</div>
                  <div class="competitor-brand">品牌: {{ row.brand }}</div>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="platform" label="平台" width="100">
            <template #default="{ row }">
              <el-tag size="small">{{ row.platform }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="current_price" label="当前价格" width="100" align="center">
            <template #default="{ row }">
              <span class="price">¥{{ row.current_price }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="original_price" label="原价" width="100" align="center">
            <template #default="{ row }">
              <span class="original-price">¥{{ row.original_price }}</span>
            </template>
          </el-table-column>
          <el-table-column label="折扣率" width="100" align="center">
            <template #default="{ row }">
              <span class="discount-rate">{{ getDiscountRate(row) }}%</span>
            </template>
          </el-table-column>
          <el-table-column prop="sales_volume" label="销量" width="100" align="center" />
          <el-table-column prop="status" label="状态" width="100" align="center">
            <template #default="{ row }">
              <el-tag :type="getCompetitorStatusTagType(row.status)" size="small">
                {{ getCompetitorStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="updated_at" label="更新时间" width="160">
            <template #default="{ row }">
              {{ formatDateTime(row.updated_at) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150" fixed="right">
            <template #default="{ row }">
              <el-button-group>
                <el-button size="small" @click="editCompetitor(row)">编辑</el-button>
                <el-button size="small" type="danger" @click="removeCompetitor(row)">移除</el-button>
              </el-button-group>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showManageCompetitorsDialog = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 添加竞品对话框 -->
    <el-dialog
      v-model="showAddCompetitorDialog"
      title="添加竞品"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="addCompetitorFormRef"
        :model="addCompetitorForm"
        :rules="addCompetitorRules"
        label-width="100px"
      >
        <el-form-item label="竞品名称" prop="name">
          <el-input v-model="addCompetitorForm.name" placeholder="请输入竞品名称" />
        </el-form-item>
        <el-form-item label="品牌" prop="brand">
          <el-input v-model="addCompetitorForm.brand" placeholder="请输入品牌名称" />
        </el-form-item>
        <el-form-item label="平台" prop="platform">
          <el-select v-model="addCompetitorForm.platform" placeholder="请选择平台">
            <el-option label="淘宝" value="taobao" />
            <el-option label="京东" value="jd" />
            <el-option label="拼多多" value="pdd" />
            <el-option label="天猫" value="tmall" />
          </el-select>
        </el-form-item>
        <el-form-item label="商品ID" prop="product_id">
          <el-input v-model="addCompetitorForm.product_id" placeholder="请输入商品ID" />
        </el-form-item>
        <el-form-item label="商品URL" prop="product_url">
          <el-input v-model="addCompetitorForm.product_url" placeholder="请输入商品URL（可选）" />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showAddCompetitorDialog = false">取消</el-button>
          <el-button type="primary" @click="addCompetitor" :loading="submitting">添加</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Refresh,
  Search,
  View,
  ShoppingBag,
  TrendCharts,
  Warning,
  Delete,
  Upload
} from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const searchKeyword = ref('')
const statusFilter = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const totalItems = ref(0)

// 统计数据
const totalGroups = ref(0)
const totalCompetitors = ref(0)
const activeAnalysis = ref(0)
const priceAlerts = ref(0)

// 数据列表
const groups = ref([])
const dataSources = ref([])
const competitors = ref([])

// 对话框状态
const showCreateGroupDialog = ref(false)
const showManageCompetitorsDialog = ref(false)
const showAddCompetitorDialog = ref(false)

// 当前操作的监控组
const currentGroup = ref(null)

// 表单数据
const createGroupForm = reactive({
  name: '',
  description: '',
  category: '',
  data_source_id: '',
  analysis_metrics: ['price_trend', 'promotion_strategy'],
  frequency: 'daily',
  schedule_time: null,
  own_products: []
})

const addCompetitorForm = reactive({
  name: '',
  brand: '',
  platform: '',
  product_id: '',
  product_url: ''
})

// 表单验证规则
const createGroupRules = {
  name: [
    { required: true, message: '请输入监控组名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择商品类目', trigger: 'change' }
  ],
  data_source_id: [
    { required: true, message: '请选择数据源', trigger: 'change' }
  ],
  analysis_metrics: [
    { required: true, message: '请选择至少一个分析指标', trigger: 'change' }
  ],
  frequency: [
    { required: true, message: '请选择采集频率', trigger: 'change' }
  ]
}

const addCompetitorRules = {
  name: [
    { required: true, message: '请输入竞品名称', trigger: 'blur' }
  ],
  brand: [
    { required: true, message: '请输入品牌名称', trigger: 'blur' }
  ],
  platform: [
    { required: true, message: '请选择平台', trigger: 'change' }
  ],
  product_id: [
    { required: true, message: '请输入商品ID', trigger: 'blur' }
  ]
}

// 表单引用
const createGroupFormRef = ref()
const addCompetitorFormRef = ref()

// 计算属性
const filteredGroups = computed(() => {
  let filtered = groups.value

  // 搜索过滤
  if (searchKeyword.value) {
    filtered = filtered.filter(group => 
      group.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
      group.description.toLowerCase().includes(searchKeyword.value.toLowerCase())
    )
  }

  // 状态过滤
  if (statusFilter.value) {
    filtered = filtered.filter(group => group.status === statusFilter.value)
  }

  return filtered
})

// 方法
const loadCompetitorGroups = async () => {
  loading.value = true
  try {
    // TODO: 调用API获取竞品监控组列表
    // const response = await api.getCompetitorGroups()
    
    // 模拟数据
    groups.value = [
      {
        id: 1,
        name: '手机市场竞品分析',
        description: '监控主流品牌手机价格策略和促销活动',
        category: '手机数码',
        data_source_name: '综合电商数据接口',
        data_source_id: 1,
        competitor_count: 45,
        analysis_metrics: ['price_trend', 'promotion_strategy', 'discount_rate'],
        frequency: 'every_6_hours',
        status: 'running',
        last_analysis_at: '2024-01-15 14:30:00',
        created_at: '2024-01-10 09:00:00'
      },
      {
        id: 2,
        name: '美妆护肤品牌监控',
        description: '分析美妆护肤行业主要品牌的定价策略',
        category: '美妆护肤',
        data_source_name: '美妆专业数据源',
        data_source_id: 2,
        competitor_count: 32,
        analysis_metrics: ['price_trend', 'market_share', 'sales_volume'],
        frequency: 'daily',
        status: 'paused',
        last_analysis_at: '2024-01-14 10:00:00',
        created_at: '2024-01-08 15:30:00'
      }
    ]
    
    totalItems.value = groups.value.length
    totalGroups.value = groups.value.length
    totalCompetitors.value = groups.value.reduce((sum, group) => sum + group.competitor_count, 0)
    activeAnalysis.value = groups.value.filter(group => group.status === 'running').length
    priceAlerts.value = 12
    
  } catch (error) {
    ElMessage.error('加载监控组列表失败')
  } finally {
    loading.value = false
  }
}

const loadDataSources = async () => {
  try {
    // TODO: 调用API获取数据源列表
    // const response = await api.getDataSources()
    
    // 模拟数据
    dataSources.value = [
      { id: 1, name: '综合电商数据接口', platform: '多平台' },
      { id: 2, name: '美妆专业数据源', platform: '垂直平台' },
      { id: 3, name: '3C数码数据接口', platform: '专业平台' }
    ]
  } catch (error) {
    ElMessage.error('加载数据源列表失败')
  }
}

const loadCompetitors = async (groupId: number) => {
  try {
    // TODO: 调用API获取竞品列表
    // const response = await api.getCompetitors(groupId)
    
    // 模拟数据
    competitors.value = [
      {
        id: 1,
        name: 'iPhone 15 Pro Max 256GB',
        brand: 'Apple',
        platform: '天猫',
        image: 'http://via.placeholder.com/60x60',
        current_price: 9999,
        original_price: 10999,
        sales_volume: 1500,
        status: 'active',
        updated_at: '2024-01-15 14:30:00'
      },
      {
        id: 2,
        name: '华为Mate60 Pro 512GB',
        brand: '华为',
        platform: '京东',
        image: 'http://via.placeholder.com/60x60',
        current_price: 6999,
        original_price: 7999,
        sales_volume: 890,
        status: 'active',
        updated_at: '2024-01-15 14:25:00'
      }
    ]
  } catch (error) {
    ElMessage.error('加载竞品列表失败')
  }
}

const handleSearch = () => {
  currentPage.value = 1
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  loadCompetitorGroups()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  loadCompetitorGroups()
}

const createGroup = async () => {
  try {
    await createGroupFormRef.value.validate()
    submitting.value = true
    
    // TODO: 调用API创建监控组
    // await api.createCompetitorGroup(createGroupForm)
    
    ElMessage.success('监控组创建成功')
    showCreateGroupDialog.value = false
    resetCreateForm()
    loadCompetitorGroups()
  } catch (error) {
    if (error !== false) { // 不是表单验证错误
      ElMessage.error('创建监控组失败')
    }
  } finally {
    submitting.value = false
  }
}

const editGroup = (row: any) => {
  // TODO: 打开编辑监控组对话框
  ElMessage.info(`编辑监控组: ${row.name}`)
}

const viewGroup = (row: any) => {
  // TODO: 跳转到监控组详情页面
  ElMessage.info(`查看监控组: ${row.name}`)
}

const manageCompetitors = async (row: any) => {
  currentGroup.value = row
  await loadCompetitors(row.id)
  showManageCompetitorsDialog.value = true
}

const toggleGroupStatus = async (row: any) => {
  try {
    const action = row.status === 'running' ? '暂停' : '启动'
    await ElMessageBox.confirm(`确定要${action}监控组"${row.name}"吗？`, '确认操作', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    // TODO: 调用API切换状态
    // await api.toggleCompetitorGroupStatus(row.id)
    
    row.status = row.status === 'running' ? 'paused' : 'running'
    ElMessage.success(`监控组已${action}`)
    loadCompetitorGroups()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败')
    }
  }
}

const deleteGroup = async (row: any) => {
  try {
    await ElMessageBox.confirm(`确定要删除监控组"${row.name}"吗？此操作不可恢复。`, '确认删除', {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'error'
    })
    
    // TODO: 调用API删除监控组
    // await api.deleteCompetitorGroup(row.id)
    
    ElMessage.success('监控组删除成功')
    loadCompetitorGroups()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const addOwnProduct = () => {
  createGroupForm.own_products.push({ name: '', id: '', price: '' })
}

const removeOwnProduct = (index: number) => {
  createGroupForm.own_products.splice(index, 1)
}

const addCompetitor = async () => {
  try {
    await addCompetitorFormRef.value.validate()
    submitting.value = true
    
    // TODO: 调用API添加竞品
    // await api.addCompetitor(currentGroup.value.id, addCompetitorForm)
    
    ElMessage.success('竞品添加成功')
    showAddCompetitorDialog.value = false
    resetAddCompetitorForm()
    loadCompetitors(currentGroup.value.id)
  } catch (error) {
    if (error !== false) { // 不是表单验证错误
      ElMessage.error('添加竞品失败')
    }
  } finally {
    submitting.value = false
  }
}

const editCompetitor = (row: any) => {
  // TODO: 打开编辑竞品对话框
  ElMessage.info(`编辑竞品: ${row.name}`)
}

const removeCompetitor = async (row: any) => {
  try {
    await ElMessageBox.confirm(`确定要移除竞品"${row.name}"吗？`, '确认移除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    // TODO: 调用API移除竞品
    // await api.removeCompetitor(row.id)
    
    ElMessage.success('竞品移除成功')
    loadCompetitors(currentGroup.value.id)
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('移除失败')
    }
  }
}

const importCompetitors = () => {
  // TODO: 实现批量导入竞品功能
  ElMessage.info('批量导入功能开发中...')
}

const resetCreateForm = () => {
  Object.assign(createGroupForm, {
    name: '',
    description: '',
    category: '',
    data_source_id: '',
    analysis_metrics: ['price_trend', 'promotion_strategy'],
    frequency: 'daily',
    schedule_time: null,
    own_products: []
  })
  createGroupFormRef.value?.resetFields()
}

const resetAddCompetitorForm = () => {
  Object.assign(addCompetitorForm, {
    name: '',
    brand: '',
    platform: '',
    product_id: '',
    product_url: ''
  })
  addCompetitorFormRef.value?.resetFields()
}

// 辅助方法
const getMetricText = (metric: string) => {
  const metricMap: Record<string, string> = {
    price_trend: '价格趋势',
    promotion_strategy: '促销策略',
    discount_rate: '折扣率',
    price_deviation: '价格偏差',
    market_share: '市场份额',
    sales_volume: '销量'
  }
  return metricMap[metric] || metric
}

const getFrequencyText = (frequency: string) => {
  const frequencyMap: Record<string, string> = {
    hourly: '每小时',
    every_6_hours: '每6小时',
    every_12_hours: '每12小时',
    daily: '每天',
    weekly: '每周'
  }
  return frequencyMap[frequency] || frequency
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    running: '运行中',
    paused: '已暂停',
    stopped: '已停止'
  }
  return statusMap[status] || status
}

const getStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    running: 'success',
    paused: 'warning',
    stopped: 'danger'
  }
  return typeMap[status] || 'info'
}

const getCompetitorStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    active: '正常',
    inactive: '失效',
    out_of_stock: '缺货'
  }
  return statusMap[status] || status
}

const getCompetitorStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    active: 'success',
    inactive: 'danger',
    out_of_stock: 'warning'
  }
  return typeMap[status] || 'info'
}

const getDiscountRate = (competitor: any) => {
  if (!competitor.original_price || !competitor.current_price) return 0
  return Math.round((1 - competitor.current_price / competitor.original_price) * 100)
}

const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  loadCompetitorGroups()
  loadDataSources()
})
</script>

<style scoped>
.competitor-task-management {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
}

.header-left {
  flex: 1;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 8px 0;
}

.page-description {
  font-size: 14px;
  color: #64748b;
  margin: 0;
  line-height: 1.5;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.stats-cards {
  margin-bottom: 24px;
}

.stats-card {
  height: 100px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.stats-content {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 16px;
}

.stats-icon {
  font-size: 32px;
  margin-right: 16px;
  opacity: 0.8;
}

.stats-info {
  flex: 1;
}

.stats-value {
  font-size: 28px;
  font-weight: 600;
  color: #2c3e50;
  line-height: 1;
  margin-bottom: 4px;
}

.stats-label {
  font-size: 14px;
  color: #64748b;
}

.groups-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.header-tools {
  display: flex;
  align-items: center;
}

.group-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.competitor-count-badge {
  cursor: pointer;
}

.metrics-display {
  display: flex;
  flex-wrap: wrap;
  gap: 2px;
}

.data-source-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.own-products-section {
  width: 100%;
}

.product-rows {
  margin-top: 12px;
}

.product-row {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.competitors-management {
  padding: 0;
}

.management-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e9ecef;
}

.header-info h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  color: #2c3e50;
}

.header-info p {
  margin: 0;
  color: #64748b;
  font-size: 14px;
}

.competitor-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.competitor-image {
  width: 50px;
  height: 50px;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

.competitor-details {
  flex: 1;
}

.competitor-name {
  font-size: 14px;
  font-weight: 500;
  color: #2c3e50;
  line-height: 1.4;
  margin-bottom: 4px;
}

.competitor-brand {
  font-size: 12px;
  color: #64748b;
}

.price {
  font-weight: 600;
  color: #e6a23c;
}

.original-price {
  color: #64748b;
  text-decoration: line-through;
}

.discount-rate {
  color: #67c23a;
  font-weight: 600;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 24px;
  padding: 16px 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
  }
  
  .header-actions {
    width: 100%;
    justify-content: flex-start;
  }
  
  .stats-cards :deep(.el-col) {
    margin-bottom: 16px;
  }
  
  .header-tools {
    flex-direction: column;
    gap: 12px;
    width: 100%;
  }
  
  .header-tools .el-input,
  .header-tools .el-select {
    width: 100% !important;
  }
  
  .management-header {
    flex-direction: column;
    gap: 16px;
  }
  
  .product-row {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
  
  .competitor-info {
    flex-direction: column;
    align-items: flex-start;
  }
}

/* 表格样式优化 */
:deep(.el-table) {
  font-size: 14px;
}

:deep(.el-table th) {
  background-color: #f8f9fa;
  color: #2c3e50;
  font-weight: 600;
}

:deep(.el-table td) {
  padding: 12px 0;
}

:deep(.el-table .el-button-group) {
  display: flex;
  gap: 4px;
}

:deep(.el-table .el-button-group .el-button) {
  margin: 0;
  padding: 4px 8px;
  font-size: 12px;
}

/* 卡片动画 */
.stats-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.stats-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
}

/* 标签样式 */
.el-tag {
  border-radius: 4px;
}

/* 对话框样式 */
:deep(.el-dialog) {
  border-radius: 8px;
}

:deep(.el-dialog__header) {
  background-color: #f8f9fa;
  padding: 20px 24px;
  border-bottom: 1px solid #e9ecef;
}

:deep(.el-dialog__body) {
  padding: 24px;
}

:deep(.el-dialog__footer) {
  padding: 16px 24px;
  background-color: #f8f9fa;
  border-top: 1px solid #e9ecef;
}
</style> 