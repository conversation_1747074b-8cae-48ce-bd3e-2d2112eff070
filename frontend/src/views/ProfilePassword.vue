<template>
  <div class="profile-password">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">
            <el-icon><Lock /></el-icon>
            修改密码
          </h1>
          <p class="page-description">保护您的账户安全，建议定期更换密码</p>
        </div>
      </div>
    </div>

    <el-row :gutter="20">
      <!-- 修改密码表单 -->
      <el-col :span="16">
        <el-card class="password-card">
          <template #header>
            <div class="card-header">
              <el-icon><Key /></el-icon>
              <span>修改密码</span>
            </div>
          </template>
          
          <el-form
            :model="passwordForm"
            :rules="passwordRules"
            ref="passwordFormRef"
            label-width="120px"
            class="password-form"
          >
            <el-form-item label="当前密码" prop="currentPassword">
              <el-input
                v-model="passwordForm.currentPassword"
                type="password"
                placeholder="请输入当前密码"
                show-password
                size="large"
                :prefix-icon="Lock"
              />
            </el-form-item>
            
            <el-form-item label="新密码" prop="newPassword">
              <el-input
                v-model="passwordForm.newPassword"
                type="password"
                placeholder="请输入新密码"
                show-password
                size="large"
                :prefix-icon="Key"
                @input="checkPasswordStrength"
              />
              <div class="password-strength" v-if="passwordForm.newPassword">
                <div class="strength-label">密码强度：</div>
                <div class="strength-bar">
                  <div
                    class="strength-fill"
                    :class="passwordStrength.level"
                    :style="{ width: passwordStrength.percentage + '%' }"
                  ></div>
                </div>
                <div class="strength-text" :class="passwordStrength.level">
                  {{ passwordStrength.text }}
                </div>
              </div>
              <div class="password-tips">
                <p>密码要求：</p>
                <ul>
                  <li :class="{ valid: passwordChecks.length }">
                    <el-icon><Check v-if="passwordChecks.length" /><Close v-else /></el-icon>
                    至少8个字符
                  </li>
                  <li :class="{ valid: passwordChecks.uppercase }">
                    <el-icon><Check v-if="passwordChecks.uppercase" /><Close v-else /></el-icon>
                    包含大写字母
                  </li>
                  <li :class="{ valid: passwordChecks.lowercase }">
                    <el-icon><Check v-if="passwordChecks.lowercase" /><Close v-else /></el-icon>
                    包含小写字母
                  </li>
                  <li :class="{ valid: passwordChecks.number }">
                    <el-icon><Check v-if="passwordChecks.number" /><Close v-else /></el-icon>
                    包含数字
                  </li>
                  <li :class="{ valid: passwordChecks.special }">
                    <el-icon><Check v-if="passwordChecks.special" /><Close v-else /></el-icon>
                    包含特殊字符
                  </li>
                </ul>
              </div>
            </el-form-item>
            
            <el-form-item label="确认新密码" prop="confirmPassword">
              <el-input
                v-model="passwordForm.confirmPassword"
                type="password"
                placeholder="请再次输入新密码"
                show-password
                size="large"
                :prefix-icon="Key"
              />
            </el-form-item>
            
            <el-form-item>
              <el-button
                type="primary"
                size="large"
                @click="submitPasswordChange"
                :loading="loading"
                :disabled="!isFormValid"
              >
                <el-icon><Check /></el-icon>
                修改密码
              </el-button>
              <el-button size="large" @click="resetForm">
                <el-icon><Refresh /></el-icon>
                重置
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>

      <!-- 安全设置 -->
      <el-col :span="8">
        <el-card class="security-card">
          <template #header>
            <div class="card-header">
              <el-icon><UserFilled /></el-icon>
              <span>安全设置</span>
            </div>
          </template>
          
          <div class="security-info">
            <div class="info-item">
              <div class="info-label">上次修改密码</div>
              <div class="info-value">{{ lastPasswordChange }}</div>
            </div>
            
            <div class="info-item">
              <div class="info-label">登录设备数</div>
              <div class="info-value">{{ loginDevices }} 个设备</div>
            </div>
            
            <div class="info-item">
              <div class="info-label">最后登录时间</div>
              <div class="info-value">{{ lastLoginTime }}</div>
            </div>
            
            <div class="info-item">
              <div class="info-label">登录IP地址</div>
              <div class="info-value">{{ lastLoginIP }}</div>
            </div>
          </div>
          
          <el-divider />
          
          <div class="security-actions">
            <h4>安全操作</h4>
            <el-button type="warning" plain @click="logoutAllDevices">
              <el-icon><SwitchButton /></el-icon>
              注销所有设备
            </el-button>
            <el-button type="info" plain @click="viewLoginHistory">
              <el-icon><View /></el-icon>
              查看登录历史
            </el-button>
          </div>
        </el-card>
        
        <!-- 安全提示 -->
        <el-card class="tips-card">
          <template #header>
            <div class="card-header">
              <el-icon><InfoFilled /></el-icon>
              <span>安全提示</span>
            </div>
          </template>
          
          <div class="security-tips">
            <el-alert
              title="密码安全建议"
              type="info"
              :closable="false"
              show-icon
            >
              <ul class="tips-list">
                <li>定期更换密码，建议每3个月更换一次</li>
                <li>使用复杂密码，包含大小写字母、数字和特殊字符</li>
                <li>不要在多个网站使用相同密码</li>
                <li>不要将密码告诉他人或写在易见的地方</li>
                <li>如发现异常登录，请立即修改密码</li>
              </ul>
            </el-alert>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 登录历史对话框 -->
    <el-dialog
      v-model="loginHistoryDialog.visible"
      title="登录历史"
      width="800px"
    >
      <el-table :data="loginHistory" v-loading="loginHistoryLoading">
        <el-table-column prop="time" label="登录时间" width="180" />
        <el-table-column prop="ip" label="IP地址" width="140" />
        <el-table-column prop="location" label="登录地点" width="150" />
        <el-table-column prop="device" label="设备信息" min-width="200" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status === 'success' ? 'success' : 'danger'">
              {{ scope.row.status === 'success' ? '成功' : '失败' }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="loginHistoryDialog.visible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Lock,
  Key,
  Check,
  Close,
  Refresh,
  UserFilled,
  SwitchButton,
  View,
  InfoFilled
} from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const passwordFormRef = ref()

// 密码表单
const passwordForm = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 表单验证规则
const passwordRules = {
  currentPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 8, message: '密码长度不能少于8位', trigger: 'blur' },
    { 
      validator: (rule: any, value: string, callback: any) => {
        if (value && !isPasswordValid(value)) {
          callback(new Error('密码必须包含大小写字母、数字和特殊字符'))
        } else {
          callback()
        }
      }, 
      trigger: 'blur' 
    }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (rule: any, value: string, callback: any) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 密码强度
const passwordStrength = reactive({
  level: 'weak',
  percentage: 0,
  text: '弱'
})

// 密码检查
const passwordChecks = reactive({
  length: false,
  uppercase: false,
  lowercase: false,
  number: false,
  special: false
})

// 安全信息
const lastPasswordChange = ref('2024-01-01 10:30:00')
const loginDevices = ref(3)
const lastLoginTime = ref('2024-01-15 14:25:30')
const lastLoginIP = ref('*************')

// 登录历史对话框
const loginHistoryDialog = reactive({
  visible: false
})

const loginHistoryLoading = ref(false)

// 登录历史数据
const loginHistory = ref([
  {
    time: '2024-01-15 14:25:30',
    ip: '*************',
    location: '北京市',
    device: 'Chrome 120.0 / Windows 10',
    status: 'success'
  },
  {
    time: '2024-01-15 09:15:20',
    ip: '*************',
    location: '北京市',
    device: 'Chrome 120.0 / Windows 10',
    status: 'success'
  },
  {
    time: '2024-01-14 18:30:45',
    ip: '*************',
    location: '上海市',
    device: 'Safari 17.0 / macOS',
    status: 'failed'
  }
])

// 计算属性
const isFormValid = computed(() => {
  return passwordForm.currentPassword &&
         passwordForm.newPassword &&
         passwordForm.confirmPassword &&
         passwordForm.newPassword === passwordForm.confirmPassword &&
         isPasswordValid(passwordForm.newPassword)
})

// 方法
const checkPasswordStrength = () => {
  const password = passwordForm.newPassword
  
  // 重置检查状态
  passwordChecks.length = password.length >= 8
  passwordChecks.uppercase = /[A-Z]/.test(password)
  passwordChecks.lowercase = /[a-z]/.test(password)
  passwordChecks.number = /\d/.test(password)
  passwordChecks.special = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)
  
  // 计算强度
  let score = 0
  if (passwordChecks.length) score += 20
  if (passwordChecks.uppercase) score += 20
  if (passwordChecks.lowercase) score += 20
  if (passwordChecks.number) score += 20
  if (passwordChecks.special) score += 20
  
  passwordStrength.percentage = score
  
  if (score <= 40) {
    passwordStrength.level = 'weak'
    passwordStrength.text = '弱'
  } else if (score <= 80) {
    passwordStrength.level = 'medium'
    passwordStrength.text = '中等'
  } else {
    passwordStrength.level = 'strong'
    passwordStrength.text = '强'
  }
}

const isPasswordValid = (password: string) => {
  return password.length >= 8 &&
         /[A-Z]/.test(password) &&
         /[a-z]/.test(password) &&
         /\d/.test(password) &&
         /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)
}

const submitPasswordChange = () => {
  passwordFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      loading.value = true
      
      try {
        // TODO: 调用API修改密码
        await new Promise(resolve => setTimeout(resolve, 2000))
        
        ElMessage.success('密码修改成功，请重新登录')
        resetForm()
        
        // 可以选择自动登出用户
        // await authStore.logout()
        // router.push('/login')
        
      } catch (error) {
        ElMessage.error('密码修改失败，请重试')
      } finally {
        loading.value = false
      }
    }
  })
}

const resetForm = () => {
  passwordFormRef.value?.resetFields()
  passwordForm.currentPassword = ''
  passwordForm.newPassword = ''
  passwordForm.confirmPassword = ''
  
  // 重置密码强度
  passwordStrength.level = 'weak'
  passwordStrength.percentage = 0
  passwordStrength.text = '弱'
  
  // 重置检查状态
  Object.keys(passwordChecks).forEach(key => {
    passwordChecks[key as keyof typeof passwordChecks] = false
  })
}

const logoutAllDevices = async () => {
  try {
    await ElMessageBox.confirm(
      '注销所有设备后，您需要在其他设备上重新登录。确定要继续吗？',
      '确认注销',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // TODO: 调用API注销所有设备
    ElMessage.success('已注销所有设备')
    loginDevices.value = 1
    
  } catch (error) {
    // 用户取消操作
  }
}

const viewLoginHistory = () => {
  loginHistoryDialog.visible = true
  loginHistoryLoading.value = true
  
  // TODO: 加载登录历史数据
  setTimeout(() => {
    loginHistoryLoading.value = false
  }, 1000)
}

// 监听密码变化
watch(() => passwordForm.confirmPassword, () => {
  if (passwordForm.confirmPassword && passwordFormRef.value) {
    passwordFormRef.value.validateField('confirmPassword')
  }
})
</script>

<style scoped>
.profile-password {
  padding: 0;
}

/* 页面头部 */
.page-header {
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-description {
  color: #7f8c8d;
  margin: 0;
  font-size: 14px;
}

/* 卡片头部 */
.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #2c3e50;
}

/* 密码表单 */
.password-card {
  margin-bottom: 20px;
}

.password-form {
  max-width: 500px;
}

.password-form .el-form-item {
  margin-bottom: 24px;
}

/* 密码强度 */
.password-strength {
  margin-top: 8px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.strength-label {
  font-size: 13px;
  color: #666;
  white-space: nowrap;
}

.strength-bar {
  flex: 1;
  height: 6px;
  background: #f0f0f0;
  border-radius: 3px;
  overflow: hidden;
}

.strength-fill {
  height: 100%;
  transition: all 0.3s ease;
  border-radius: 3px;
}

.strength-fill.weak {
  background: #f56c6c;
}

.strength-fill.medium {
  background: #e6a23c;
}

.strength-fill.strong {
  background: #67c23a;
}

.strength-text {
  font-size: 13px;
  font-weight: 500;
  white-space: nowrap;
}

.strength-text.weak {
  color: #f56c6c;
}

.strength-text.medium {
  color: #e6a23c;
}

.strength-text.strong {
  color: #67c23a;
}

/* 密码提示 */
.password-tips {
  margin-top: 12px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  font-size: 13px;
}

.password-tips p {
  margin: 0 0 8px 0;
  font-weight: 500;
  color: #666;
}

.password-tips ul {
  margin: 0;
  padding: 0;
  list-style: none;
}

.password-tips li {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 4px;
  color: #999;
  transition: color 0.3s ease;
}

.password-tips li.valid {
  color: #67c23a;
}

.password-tips li .el-icon {
  font-size: 12px;
}

/* 安全设置 */
.security-card {
  margin-bottom: 20px;
}

.security-info {
  margin-bottom: 20px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 14px;
  color: #666;
}

.info-value {
  font-size: 14px;
  color: #2c3e50;
  font-weight: 500;
}

.security-actions h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  color: #2c3e50;
}

.security-actions .el-button {
  display: block;
  width: 100%;
  margin-bottom: 12px;
}

.security-actions .el-button:last-child {
  margin-bottom: 0;
}

/* 安全提示 */
.tips-card {
  margin-bottom: 20px;
}

.tips-list {
  margin: 0;
  padding-left: 16px;
  color: #666;
  font-size: 13px;
  line-height: 1.6;
}

.tips-list li {
  margin-bottom: 6px;
}

.tips-list li:last-child {
  margin-bottom: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .el-row {
    flex-direction: column;
  }
  
  .el-col {
    width: 100% !important;
    margin-bottom: 20px;
  }
  
  .password-form {
    max-width: none;
  }
  
  .password-strength {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .strength-bar {
    width: 100%;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .password-tips {
    background: #2d2d2d;
  }
  
  .info-item {
    border-bottom-color: #404040;
  }
}
</style> 