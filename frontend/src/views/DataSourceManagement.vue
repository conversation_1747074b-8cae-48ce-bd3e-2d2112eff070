<template>
  <div class="p-4">
    <h1 class="text-2xl font-bold mb-4">数据源管理</h1>

    <!-- 操作区域 -->
    <div class="flex justify-between items-center mb-4">
      <div class="space-x-2">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索数据源名称"
          clearable
          @clear="fetchDataSources"
          @keyup.enter="fetchDataSources"
          style="width: 200px;"
        ></el-input>
        <el-button @click="fetchDataSources" :icon="Search">搜索</el-button>
      </div>
      <el-button type="primary" @click="handleCreate" :icon="Plus">新增数据源</el-button>
    </div>

    <!-- 数据源列表 -->
    <el-table :data="dataSourceList" v-loading="loading" border style="width: 100%">
      <el-table-column prop="id" label="ID" width="80"></el-table-column>
      <el-table-column prop="name" label="数据源名称" width="200"></el-table-column>
      <el-table-column prop="type" label="类型" width="120">
        <template #default="{ row }">
          <el-tag :type="getPlatformInfo(row.type).color" disable-transitions>
            {{ getPlatformInfo(row.type).text }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="config.url" label="API/数据库地址">
         <template #default="{ row }">
          <span>{{ row.type === 'api' ? row.config.url : `${row.config.host}:${row.config.port}` }}</span>
        </template>
      </el-table-column>
       <el-table-column prop="created_at" label="创建时间" width="180">
        <template #default="{ row }">
          {{ new Date(row.created_at).toLocaleString() }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="{ row }">
          <el-button size="small" @click="handleEdit(row)" :icon="Edit">编辑</el-button>
          <el-popconfirm
            title="确定要删除这个数据源吗？"
            @confirm="handleDelete(row.id)"
            confirm-button-text="确认"
            cancel-button-text="取消"
          >
            <template #reference>
              <el-button size="small" type="danger" :icon="Delete">删除</el-button>
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination
      v-if="total > 0"
      class="mt-4"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="currentPage"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
    ></el-pagination>

    <!-- 新增/编辑 对话框 -->
    <el-dialog :title="dialogTitle" v-model="dialogVisible" width="60%">
      <el-form :model="currentDataSource" ref="dataSourceForm" label-width="120px">
        <el-form-item label="数据源名称" prop="name">
          <el-input v-model="currentDataSource.name" placeholder="例如：我的淘宝API"></el-input>
        </el-form-item>
        <el-form-item label="数据源类型" prop="type">
          <el-select v-model="currentDataSource.type" placeholder="请选择类型" @change="onTypeChange">
            <el-option
              v-for="item in platformTypes"
              :key="item.value"
              :label="item.text"
              :value="item.value"
            >
              <span style="float: left">{{ item.text }}</span>
              <span style="float: right; color: var(--el-text-color-secondary); font-size: 13px;">
                <el-icon><component :is="item.icon" /></el-icon>
              </span>
            </el-option>
          </el-select>
        </el-form-item>

        <!-- API 配置 -->
        <template v-if="currentDataSource.type === 'api'">
          <el-form-item label="API URL" prop="config.url">
            <el-input v-model="currentDataSource.config.url" placeholder="http://api.example.com/data"></el-input>
          </el-form-item>
          <el-form-item label="请求方法" prop="config.method">
            <el-select v-model="currentDataSource.config.method">
              <el-option label="GET" value="GET"></el-option>
              <el-option label="POST" value="POST"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="认证方式" prop="config.auth_type">
             <el-select v-model="currentDataSource.config.auth_type" placeholder="选择认证方式">
              <el-option label="无" value="none"></el-option>
              <el-option label="Bearer Token" value="bearer"></el-option>
              <el-option label="API Key in Header" value="header_key"></el-option>
              <el-option label="API Key in Query" value="query_key"></el-option>
            </el-select>
          </el-form-item>

          <template v-if="currentDataSource.config.auth_type === 'bearer'">
            <el-form-item label="Bearer Token" prop="config.token">
              <el-input v-model="currentDataSource.config.token" type="password" show-password placeholder="输入Bearer Token"></el-input>
            </el-form-item>
          </template>

           <template v-if="['header_key', 'query_key'].includes(currentDataSource.config.auth_type)">
             <el-form-item label="Key名称" prop="config.api_key_name">
                <el-input v-model="currentDataSource.config.api_key_name" placeholder="例如：X-API-KEY 或 api_key"></el-input>
            </el-form-item>
             <el-form-item label="Key值" prop="config.api_key_value">
                <el-input v-model="currentDataSource.config.api_key_value" type="password" show-password placeholder="输入API Key"></el-input>
            </el-form-item>
           </template>

          <el-form-item label="默认参数 (JSON)" prop="default_params">
            <el-input v-model="currentDataSource.default_params" type="textarea" :rows="3" placeholder='{"key": "value"}'></el-input>
          </el-form-item>
        </template>
        
        <!-- 数据库配置 -->
        <template v-if="currentDataSource.type === 'db'">
           <el-form-item label="数据库类型" prop="config.db_type">
             <el-select v-model="currentDataSource.config.db_type">
              <el-option label="MySQL" value="mysql"></el-option>
              <el-option label="PostgreSQL" value="pgsql"></el-option>
              <el-option label="SQLite" value="sqlite"></el-option>
               <el-option label="SQL Server" value="sqlsrv"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="主机" prop="config.host">
            <el-input v-model="currentDataSource.config.host"></el-input>
          </el-form-item>
          <el-form-item label="端口" prop="config.port">
            <el-input v-model.number="currentDataSource.config.port" type="number"></el-input>
          </el-form-item>
          <el-form-item label="数据库名" prop="config.database">
            <el-input v-model="currentDataSource.config.database"></el-input>
          </el-form-item>
          <el-form-item label="用户名" prop="config.username">
            <el-input v-model="currentDataSource.config.username"></el-input>
          </el-form-item>
          <el-form-item label="密码" prop="config.password">
            <el-input v-model="currentDataSource.config.password" type="password" show-password></el-input>
          </el-form-item>
        </template>
        
        <!-- 字段映射 -->
        <template v-if="currentDataSource.type === 'api'">
          <el-divider>字段映射</el-divider>
          <el-form-item label="映射规则" prop="field_mapping">
             <div class="w-full">
                <el-button @click="loadTaobaoTemplate" size="small" class="mb-2">载入淘宝详情接口模板</el-button>
                <vue-json-pretty 
                  v-model:data="currentDataSource.field_mapping"
                  :editable="true"
                  :show-double-quotes="true"
                  :deep="3"
                />
             </div>
          </el-form-item>
        </template>
        
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Search, Plus, Edit, Delete, Platform, Coin, Tickets, Box, Link } from '@element-plus/icons-vue';
import { getDataSource, createDataSource, updateDataSource, deleteDataSource } from '@/api/dataSource';
import VueJsonPretty from 'vue-json-pretty';
import 'vue-json-pretty/lib/styles.css';

const dataSourceList = ref([]);
const loading = ref(true);
const searchKeyword = ref('');
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);

const dialogVisible = ref(false);
const dialogTitle = ref('');
const isEdit = ref(false);
const dataSourceForm = ref(null);

// 淘宝详情接口默认映射模板
const TAOBAO_DETAIL_TEMPLATE = {
  "data_path": "data",
  "fields": {
    // 基本信息字段
    "code": "code",
    "product_id": "id",
    "id": "id", // 兼容性字段
    "main_image_url": "pic_urls[0]",
    "title": "title",
    
    // 价格相关字段
    "min_hand_price": "min_price",
    "max_hand_price": "max_price", 
    "original_price": "price",
    "price": "price", // 兼容性字段
    "sub_price": "sub_price",
    "sub_price_title": "sub_price_title",
    
    // SKU相关字段
    "sku_id": "skus[0].id",
    "sku_name": "skus[0].name",
    "quantity": "skus[0].quantity",
    
    // 商品属性字段
    "promotions": {
      "path": "promotionDisplays"
    },
    "sale": "sale",
    "comment_count": "comment_count",
    "state": "state",
    "is_sku": "is_sku",
    "item_type": "item_type",
    
    // 分类和店铺字段
    "category_id": "category_id",
    "category_path": "category_path",
    "shop_id": "shop_id",
    "shop_name": "shop_name",
    
    // 其他字段
    "props": "props",
    "delivery": "delivery",
    
    // 兼容性字段 (原有字段保持不变)
    "product_name": "title",
    "all_image_urls": "pic_urls",
    "stock": "skus[0].quantity",
    "sales_count": "sale",
    "status": "state",
    "category_name": "category_path",
    "attributes": "props",
    
    // SKU详细信息
    "skus": {
      "path": "skus",
      "fields": {
        "sku_id": "id",
        "sku_name": "name",
        "price": "price",
        "promotion_price": "sub_price",
        "stock": "quantity",
        "image_url": "pic_url"
      }
    },
    "quantity": "skus[0].quantity",
  },
  "transformations": {
    "state": {
      "type": "boolean",
      "mapping": {
        "1": true,
        "0": false,
        "true": true,
        "false": false
      }
    },
    "is_sku": {
      "type": "boolean",
      "mapping": {
        "1": true,
        "0": false,
        "true": true,
        "false": false
      }
    },
    "item_type": {
      "type": "string",
      "default": "淘宝"
    },
    "promotion": {
      "type": "array",
      "ensure_array": true
    },
    "props": {
      "type": "array",
      "ensure_array": true
    }
  }
};

const getInitialDataSource = () => ({
  id: null,
  name: '',
  type: 'api', // 默认类型
  config: {
    url: '',
    method: 'GET',
    auth_type: 'none',
    token: '',
    api_key_name: '',
    api_key_value: '',
    db_type: 'mysql',
    host: '127.0.0.1',
    port: 3306,
    database: '',
    username: '',
    password: '',
  },
  default_params: {}, // 由字符串改为对象
  field_mapping: {}, // 初始化为空对象
});

let currentDataSource = ref(getInitialDataSource());

const platformTypes = ref([
  { value: 'api', text: '通用API', icon: Link, color: 'primary' },
  { value: 'taobao_api', text: '淘宝详情接口', icon: Platform, color: 'danger' },
  { value: 'jd_api', text: '京东详情接口', icon: Platform, color: 'warning' },
  { value: 'db', text: '数据库', icon: Coin, color: 'success' },
  { value: 'competitor', text: '竞品网站', icon: Tickets, color: 'info' },
  { value: 'other', text: '其他', icon: Box, color: 'default' },
]);

const getPlatformInfo = (type) => {
  return platformTypes.value.find(p => p.value === type) || { text: '未知', color: 'default' };
};

const onTypeChange = (newType) => {
  // 当类型改变时，可以重置部分配置
  const oldConfig = currentDataSource.value.config;
  const newConfig = getInitialDataSource().config;
  currentDataSource.value.config = { ...newConfig, ...oldConfig };

  if (newType === 'taobao_api') {
      currentDataSource.value.type = 'api'; // 底层类型还是api
      // 可以在这里预填一些淘宝API的默认值
      currentDataSource.value.name = '淘宝商品详情API';
      currentDataSource.value.config.url = 'http://60.247.148.208:5001/tb/new/item_detail_base';
      currentDataSource.value.config.auth_type = 'query_key';
      currentDataSource.value.config.api_key_name = 'token';
      loadTaobaoTemplate();
  }
};

const loadTaobaoTemplate = () => {
    currentDataSource.value.field_mapping = JSON.parse(JSON.stringify(TAOBAO_DETAIL_TEMPLATE));
    ElMessage.success('已加载淘宝详情接口模板');
}

const fetchDataSources = async () => {
  loading.value = true;
  try {
    const response = await getDataSource({
      page: currentPage.value,
      per_page: pageSize.value,
      keyword: searchKeyword.value,
    });
    dataSourceList.value = response.data.data;
    total.value = response.data.total;
  } catch (error) {
    console.error("获取数据源列表失败:", error);
    ElMessage.error("获取数据源列表失败");
  } finally {
    loading.value = false;
  }
};

const handleCreate = () => {
  isEdit.value = false;
  dialogTitle.value = '新增数据源';
  currentDataSource.value = getInitialDataSource();
  dialogVisible.value = true;
};

const handleEdit = (row) => {
  isEdit.value = true;
  dialogTitle.value = '编辑数据源';
  
  // 深拷贝，避免直接修改列表中的数据
  const rowData = JSON.parse(JSON.stringify(row));

  // 合并以确保所有字段存在
  // 后端casts会自动处理JSON字符串，前端不需要手动parse
  currentDataSource.value = {
      ...getInitialDataSource(),
      ...rowData,
      config: {
          ...getInitialDataSource().config,
          ...rowData.config,
      },
  };

  dialogVisible.value = true;
};

const handleDelete = async (id) => {
  try {
    await deleteDataSource(id);
    ElMessage.success('删除成功');
    fetchDataSources();
  } catch (error) {
    ElMessage.error('删除失败');
  }
};

const handleSubmit = async () => {
  try {
    const valid = await dataSourceForm.value.validate();
    if (!valid) return;

    // 深拷贝一份数据，防止修改原始响应式对象
    const payload = JSON.parse(JSON.stringify(currentDataSource.value));

    // default_params 来自文本框，需要从字符串解析为对象
    if (payload.default_params && typeof payload.default_params === 'string') {
      try {
        payload.default_params = JSON.parse(payload.default_params);
      } catch (e) {
        ElMessage.error('默认参数 (JSON) 格式不正确，请修正或留空');
        return;
      }
    } else if (!payload.default_params) {
      // 如果为空，确保发送一个空对象，以满足后端array类型验证
      payload.default_params = {};
    }
    
    // field_mapping 来自JSON编辑器，本身就是对象，无需处理

    let response;
    if (isEdit.value) {
      response = await updateDataSource(payload.id, payload);
    } else {
      response = await createDataSource(payload);
    }

    if (response && response.data && response.data.id) {
      ElMessage.success(isEdit.value ? '更新成功' : '创建成功');
      dialogVisible.value = false;
      fetchDataSources();
    } else {
      ElMessage.error(response.message || '操作失败');
    }
  } catch (error) {
    console.error('保存数据源失败:', error);
    ElMessage.error(error.response?.data?.message || '保存失败，请检查填写的内容');
  }
};

const handleSizeChange = (val) => {
  pageSize.value = val;
  fetchDataSources();
};

const handleCurrentChange = (val) => {
  currentPage.value = val;
  fetchDataSources();
};

onMounted(fetchDataSources);
</script>

<style scoped>
.el-table {
  -webkit-box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
  box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
}
.el-button--text {
  margin-right: 15px;
}
</style>