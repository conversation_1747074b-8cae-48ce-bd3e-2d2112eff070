<template>
  <div class="competitor-alerts">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">
            <el-icon><Bell /></el-icon>
            竞品预警中心
          </h1>
          <p class="page-description">设置竞品监测预警规则，查看预警历史</p>
        </div>
        <div class="header-actions">
          <el-button type="primary" @click="showCreateRuleDialog">
            <el-icon><Plus /></el-icon>
            新建预警规则
          </el-button>
          <el-button @click="refreshAlerts">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </div>
    </div>

    <!-- 预警统计 -->
    <div class="alert-stats">
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon total">
              <el-icon><DataBoard /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ alertStats.totalRules }}</div>
              <div class="stat-label">预警规则总数</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon active">
              <el-icon><Bell /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ alertStats.activeRules }}</div>
              <div class="stat-label">活跃规则</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon triggered">
              <el-icon><Warning /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ alertStats.triggeredToday }}</div>
              <div class="stat-label">今日触发</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon unread">
              <el-icon><Message /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ alertStats.unreadAlerts }}</div>
              <div class="stat-label">未读预警</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 标签页切换 -->
    <el-tabs v-model="activeTab" class="alert-tabs">
      <el-tab-pane label="预警规则" name="rules">
        <div class="rules-section">
          <!-- 规则筛选 -->
          <div class="filter-bar">
            <el-form :model="ruleFilters" inline>
              <el-form-item label="规则状态">
                <el-select v-model="ruleFilters.status" placeholder="全部状态" clearable>
                  <el-option label="启用" value="active" />
                  <el-option label="禁用" value="inactive" />
                </el-select>
              </el-form-item>
              <el-form-item label="预警类型">
                <el-select v-model="ruleFilters.type" placeholder="全部类型" clearable>
                  <el-option label="价格偏差" value="price_deviation" />
                  <el-option label="促销强度" value="promotion_intensity" />
                  <el-option label="库存变化" value="stock_change" />
                  <el-option label="新品上架" value="new_product" />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="applyRuleFilters">查询</el-button>
                <el-button @click="resetRuleFilters">重置</el-button>
              </el-form-item>
            </el-form>
          </div>

          <!-- 规则列表 -->
          <el-table :data="alertRules" v-loading="rulesLoading">
            <el-table-column prop="name" label="规则名称" width="200">
              <template #default="scope">
                <div class="rule-name">
                  <span>{{ scope.row.name }}</span>
                  <el-tag v-if="!scope.row.enabled" type="info" size="small">已禁用</el-tag>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="type" label="预警类型" width="120">
              <template #default="scope">
                <el-tag :type="getAlertTypeColor(scope.row.type)">
                  {{ getAlertTypeText(scope.row.type) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="condition" label="触发条件" width="250">
              <template #default="scope">
                <div class="condition-text">{{ formatCondition(scope.row) }}</div>
              </template>
            </el-table-column>
            <el-table-column prop="targetProducts" label="监控商品" width="100">
              <template #default="scope">
                <el-link @click="viewTargetProducts(scope.row)">
                  {{ scope.row.targetProducts }} 个商品
                </el-link>
              </template>
            </el-table-column>
            <el-table-column prop="notifyUsers" label="通知人员" width="150">
              <template #default="scope">
                <div class="avatar-group">
                  <el-avatar 
                    v-for="(user, index) in scope.row.notifyUsers.slice(0, 3)" 
                    :key="user.id"
                    :size="24"
                    :style="{ marginLeft: index > 0 ? '-8px' : '0' }"
                  >
                    {{ user.name.charAt(0) }}
                  </el-avatar>
                  <span v-if="scope.row.notifyUsers.length > 3" class="more-count">
                    +{{ scope.row.notifyUsers.length - 3 }}
                  </span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="lastTriggered" label="最后触发" width="150" />
            <el-table-column prop="triggerCount" label="触发次数" width="100" />
            <el-table-column label="操作" width="200">
              <template #default="scope">
                <el-button
                  :type="scope.row.enabled ? 'warning' : 'success'"
                  size="small"
                  @click="toggleRule(scope.row)"
                >
                  {{ scope.row.enabled ? '禁用' : '启用' }}
                </el-button>
                <el-button type="primary" size="small" @click="editRule(scope.row)">
                  编辑
                </el-button>
                <el-button type="danger" size="small" @click="deleteRule(scope.row)">
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-tab-pane>

      <el-tab-pane label="预警历史" name="history">
        <div class="history-section">
          <!-- 历史筛选 -->
          <div class="filter-bar">
            <el-form :model="historyFilters" inline>
              <el-form-item label="时间范围">
                <el-date-picker
                  v-model="historyFilters.dateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                />
              </el-form-item>
              <el-form-item label="预警级别">
                <el-select v-model="historyFilters.level" placeholder="全部级别" clearable>
                  <el-option label="高" value="high" />
                  <el-option label="中" value="medium" />
                  <el-option label="低" value="low" />
                </el-select>
              </el-form-item>
              <el-form-item label="处理状态">
                <el-select v-model="historyFilters.status" placeholder="全部状态" clearable>
                  <el-option label="未读" value="unread" />
                  <el-option label="已读" value="read" />
                  <el-option label="已处理" value="handled" />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="applyHistoryFilters">查询</el-button>
                <el-button @click="resetHistoryFilters">重置</el-button>
                <el-button @click="markAllAsRead">全部标为已读</el-button>
              </el-form-item>
            </el-form>
          </div>

          <!-- 历史列表 -->
          <el-table :data="alertHistory" v-loading="historyLoading">
            <el-table-column width="50">
              <template #default="scope">
                <div class="status-indicator" :class="scope.row.status"></div>
              </template>
            </el-table-column>
            <el-table-column prop="ruleName" label="预警规则" width="150" />
            <el-table-column prop="level" label="级别" width="80">
              <template #default="scope">
                <el-tag :type="getLevelType(scope.row.level)" size="small">
                  {{ scope.row.level === 'high' ? '高' : scope.row.level === 'medium' ? '中' : '低' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="message" label="预警内容" min-width="300">
              <template #default="scope">
                <div class="alert-message">
                  <div class="message-title">{{ scope.row.title }}</div>
                  <div class="message-content">{{ scope.row.message }}</div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="product" label="相关商品" width="200">
              <template #default="scope">
                <div class="product-info" v-if="scope.row.product">
                  <el-image
                    :src="scope.row.product.image"
                    class="product-thumb"
                    fit="cover"
                  />
                  <div class="product-name">{{ scope.row.product.name }}</div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="triggeredAt" label="触发时间" width="150" />
            <el-table-column label="操作" width="150">
              <template #default="scope">
                <el-button
                  v-if="scope.row.status === 'unread'"
                  type="primary"
                  size="small"
                  @click="markAsRead(scope.row)"
                >
                  标为已读
                </el-button>
                <el-button
                  type="success"
                  size="small"
                  @click="handleAlert(scope.row)"
                  :disabled="scope.row.status === 'handled'"
                >
                  {{ scope.row.status === 'handled' ? '已处理' : '处理' }}
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <div class="pagination-wrapper">
            <el-pagination
              v-model:current-page="historyPagination.currentPage"
              v-model:page-size="historyPagination.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              :total="historyPagination.total"
              layout="total, sizes, prev, pager, next, jumper"
            />
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 创建/编辑规则对话框 -->
    <el-dialog
      v-model="ruleDialog.visible"
      :title="ruleDialog.isEdit ? '编辑预警规则' : '新建预警规则'"
      width="600px"
    >
      <el-form :model="ruleForm" :rules="ruleRules" ref="ruleFormRef" label-width="100px">
        <el-form-item label="规则名称" prop="name">
          <el-input v-model="ruleForm.name" placeholder="请输入规则名称" />
        </el-form-item>
        <el-form-item label="预警类型" prop="type">
          <el-select v-model="ruleForm.type" placeholder="请选择预警类型">
            <el-option label="价格偏差预警" value="price_deviation" />
            <el-option label="促销强度预警" value="promotion_intensity" />
            <el-option label="库存变化预警" value="stock_change" />
            <el-option label="新品上架预警" value="new_product" />
          </el-select>
        </el-form-item>
        <el-form-item label="触发条件" prop="condition">
          <div class="condition-builder">
            <el-select v-model="ruleForm.condition.field" placeholder="选择字段">
              <el-option label="价格偏差率" value="price_deviation_rate" />
              <el-option label="促销强度指数" value="promotion_intensity" />
              <el-option label="库存数量" value="stock_quantity" />
              <el-option label="销量变化" value="sales_change" />
            </el-select>
            <el-select v-model="ruleForm.condition.operator" placeholder="选择操作符">
              <el-option label="大于" value="gt" />
              <el-option label="小于" value="lt" />
              <el-option label="等于" value="eq" />
              <el-option label="大于等于" value="gte" />
              <el-option label="小于等于" value="lte" />
            </el-select>
            <el-input-number
              v-model="ruleForm.condition.value"
              placeholder="阈值"
              :precision="2"
            />
          </div>
        </el-form-item>
        <el-form-item label="预警级别" prop="level">
          <el-radio-group v-model="ruleForm.level">
                          <el-radio value="high">高</el-radio>
              <el-radio value="medium">中</el-radio>
              <el-radio value="low">低</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="通知人员" prop="notifyUsers">
          <el-select v-model="ruleForm.notifyUsers" multiple placeholder="选择通知人员">
            <el-option
              v-for="user in allUsers"
              :key="user.id"
              :label="user.name"
              :value="user.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="描述">
          <el-input
            v-model="ruleForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入规则描述"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="ruleDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="saveRule">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Bell,
  Plus,
  Refresh,
  DataBoard,
  Warning,
  Message
} from '@element-plus/icons-vue'

// 响应式数据
const activeTab = ref('rules')
const rulesLoading = ref(false)
const historyLoading = ref(false)

// 预警统计
const alertStats = reactive({
  totalRules: 15,
  activeRules: 12,
  triggeredToday: 8,
  unreadAlerts: 3
})

// 规则筛选
const ruleFilters = reactive({
  status: '',
  type: ''
})

// 历史筛选
const historyFilters = reactive({
  dateRange: [],
  level: '',
  status: ''
})

// 历史分页
const historyPagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 156
})

// 预警规则数据
const alertRules = ref([
  {
    id: 1,
    name: '竞品价格大幅下降预警',
    type: 'price_deviation',
    condition: { field: 'price_deviation_rate', operator: 'lt', value: -20 },
    level: 'high',
    enabled: true,
    targetProducts: 25,
    notifyUsers: [
      { id: 1, name: '张三' },
      { id: 2, name: '李四' }
    ],
    lastTriggered: '2024-01-15 14:30',
    triggerCount: 12
  },
  {
    id: 2,
    name: '促销强度异常预警',
    type: 'promotion_intensity',
    condition: { field: 'promotion_intensity', operator: 'gt', value: 80 },
    level: 'medium',
    enabled: true,
    targetProducts: 18,
    notifyUsers: [
      { id: 1, name: '张三' }
    ],
    lastTriggered: '2024-01-15 10:15',
    triggerCount: 5
  }
])

// 预警历史数据
const alertHistory = ref([
  {
    id: 1,
    ruleName: '竞品价格大幅下降预警',
    level: 'high',
    title: '竞品价格异常下降',
    message: '商品"iPhone 15 Pro"在竞品店铺的价格下降了25%，当前价格¥7,999',
    product: {
      name: 'iPhone 15 Pro',
      image: 'https://picsum.photos/40/40?random=1'
    },
    status: 'unread',
    triggeredAt: '2024-01-15 14:30:25'
  },
  {
    id: 2,
    ruleName: '促销强度异常预警',
    level: 'medium',
    title: '竞品促销力度加大',
    message: '竞品店铺开始大力度促销活动，促销强度指数达到85%',
    product: {
      name: 'MacBook Air M2',
      image: 'https://picsum.photos/40/40?random=2'
    },
    status: 'read',
    triggeredAt: '2024-01-15 10:15:30'
  }
])

// 规则表单
const ruleDialog = reactive({
  visible: false,
  isEdit: false
})

const ruleForm = reactive({
  name: '',
  type: '',
  condition: {
    field: '',
    operator: '',
    value: 0
  },
  level: 'medium',
  notifyUsers: [],
  description: ''
})

const ruleRules = {
  name: [{ required: true, message: '请输入规则名称', trigger: 'blur' }],
  type: [{ required: true, message: '请选择预警类型', trigger: 'change' }],
  level: [{ required: true, message: '请选择预警级别', trigger: 'change' }]
}

const ruleFormRef = ref()

// 所有用户数据
const allUsers = ref([
  { id: 1, name: '张三' },
  { id: 2, name: '李四' },
  { id: 3, name: '王五' },
  { id: 4, name: '赵六' }
])

// 方法
const showCreateRuleDialog = () => {
  ruleDialog.isEdit = false
  ruleDialog.visible = true
  resetRuleForm()
}

const refreshAlerts = () => {
  ElMessage.success('数据已刷新')
}

const applyRuleFilters = () => {
  rulesLoading.value = true
  setTimeout(() => {
    rulesLoading.value = false
  }, 500)
}

const resetRuleFilters = () => {
  ruleFilters.status = ''
  ruleFilters.type = ''
  applyRuleFilters()
}

const applyHistoryFilters = () => {
  historyLoading.value = true
  setTimeout(() => {
    historyLoading.value = false
  }, 500)
}

const resetHistoryFilters = () => {
  historyFilters.dateRange = []
  historyFilters.level = ''
  historyFilters.status = ''
  applyHistoryFilters()
}

const markAllAsRead = async () => {
  try {
    await ElMessageBox.confirm('确定要将所有未读预警标记为已读吗？', '确认操作')
    // TODO: 调用API
    ElMessage.success('已全部标记为已读')
  } catch (error) {
    // 用户取消
  }
}

const toggleRule = async (rule: any) => {
  const action = rule.enabled ? '禁用' : '启用'
  try {
    await ElMessageBox.confirm(`确定要${action}规则"${rule.name}"吗？`, '确认操作')
    rule.enabled = !rule.enabled
    ElMessage.success(`规则已${action}`)
  } catch (error) {
    // 用户取消
  }
}

const editRule = (rule: any) => {
  ruleDialog.isEdit = true
  ruleDialog.visible = true
  // 填充表单数据
  Object.assign(ruleForm, rule)
}

const deleteRule = async (rule: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除规则"${rule.name}"吗？删除后无法恢复。`,
      '确认删除',
      { type: 'warning' }
    )
    // TODO: 调用API删除
    ElMessage.success('规则已删除')
  } catch (error) {
    // 用户取消
  }
}

const viewTargetProducts = (rule: any) => {
  ElMessage.info('查看监控商品功能开发中...')
}

const markAsRead = (alert: any) => {
  alert.status = 'read'
  ElMessage.success('已标记为已读')
}

const handleAlert = (alert: any) => {
  alert.status = 'handled'
  ElMessage.success('预警已处理')
}

const resetRuleForm = () => {
  Object.assign(ruleForm, {
    name: '',
    type: '',
    condition: {
      field: '',
      operator: '',
      value: 0
    },
    level: 'medium',
    notifyUsers: [],
    description: ''
  })
}

const saveRule = () => {
  ruleFormRef.value?.validate((valid: boolean) => {
    if (valid) {
      // TODO: 调用API保存规则
      ruleDialog.visible = false
      ElMessage.success(ruleDialog.isEdit ? '规则已更新' : '规则已创建')
    }
  })
}

// 工具方法
const getAlertTypeColor = (type: string) => {
  const colors: Record<string, string> = {
    'price_deviation': 'danger',
    'promotion_intensity': 'warning',
    'stock_change': 'info',
    'new_product': 'success'
  }
  return colors[type] || ''
}

const getAlertTypeText = (type: string) => {
  const texts: Record<string, string> = {
    'price_deviation': '价格偏差',
    'promotion_intensity': '促销强度',
    'stock_change': '库存变化',
    'new_product': '新品上架'
  }
  return texts[type] || type
}

const formatCondition = (rule: any) => {
  const fieldTexts: Record<string, string> = {
    'price_deviation_rate': '价格偏差率',
    'promotion_intensity': '促销强度指数',
    'stock_quantity': '库存数量',
    'sales_change': '销量变化'
  }
  
  const operatorTexts: Record<string, string> = {
    'gt': '大于',
    'lt': '小于',
    'eq': '等于',
    'gte': '大于等于',
    'lte': '小于等于'
  }
  
  const field = fieldTexts[rule.condition.field] || rule.condition.field
  const operator = operatorTexts[rule.condition.operator] || rule.condition.operator
  const value = rule.condition.value
  
  return `${field} ${operator} ${value}${rule.condition.field.includes('rate') ? '%' : ''}`
}

const getLevelType = (level: string) => {
  const types: Record<string, string> = {
    'high': 'danger',
    'medium': 'warning',
    'low': 'info'
  }
  return types[level] || ''
}

// 生命周期
onMounted(() => {
  // TODO: 加载初始数据
})
</script>

<style scoped>
.competitor-alerts {
  padding: 0;
}

/* 页面头部 */
.page-header {
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-description {
  color: #7f8c8d;
  margin: 0;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 统计卡片 */
.alert-stats {
  margin-bottom: 20px;
}

.stat-card {
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
}

.stat-icon.total { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
.stat-icon.active { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
.stat-icon.triggered { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
.stat-icon.unread { background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); }

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: #2c3e50;
  line-height: 1;
}

.stat-label {
  color: #7f8c8d;
  font-size: 14px;
  margin-top: 4px;
}

/* 标签页 */
.alert-tabs {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

/* 筛选栏 */
.filter-bar {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
}

/* 规则名称 */
.rule-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 条件文本 */
.condition-text {
  font-size: 13px;
  color: #666;
}

/* 状态指示器 */
.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin: 0 auto;
}

.status-indicator.unread {
  background: #f56c6c;
}

.status-indicator.read {
  background: #e6a23c;
}

.status-indicator.handled {
  background: #67c23a;
}

/* 预警消息 */
.alert-message {
  line-height: 1.4;
}

.message-title {
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 4px;
}

.message-content {
  font-size: 13px;
  color: #666;
}

/* 头像组 */
.avatar-group {
  display: flex;
  align-items: center;
}

.more-count {
  font-size: 12px;
  color: #666;
  margin-left: 8px;
}

/* 商品信息 */
.product-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.product-thumb {
  width: 32px;
  height: 32px;
  border-radius: 4px;
  flex-shrink: 0;
}

.product-name {
  font-size: 13px;
  color: #666;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 分页 */
.pagination-wrapper {
  display: flex;
  justify-content: center;
  padding: 20px 0;
}

/* 条件构建器 */
.condition-builder {
  display: flex;
  gap: 12px;
  align-items: center;
}

.condition-builder .el-select,
.condition-builder .el-input-number {
  flex: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .alert-stats .el-col {
    margin-bottom: 16px;
  }
  
  .filter-bar .el-form {
    flex-direction: column;
  }
  
  .filter-bar .el-form-item {
    margin-right: 0;
    margin-bottom: 16px;
  }
  
  .condition-builder {
    flex-direction: column;
    align-items: stretch;
  }
}
</style> 