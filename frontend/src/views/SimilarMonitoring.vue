<template>
  <div class="similar-monitoring">
    <div class="page-header">
      <h1>相似同款监控任务</h1>
      <p>管理长期同款商品监控任务，追踪侵权和非授权销售</p>
    </div>

    <!-- 操作工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-button type="primary" :icon="Plus" @click="showCreateDialog = true">
          创建监控任务
        </el-button>
        <el-button type="success" :disabled="selectedTasks.length === 0" @click="handleBatchStart">
          批量启动
        </el-button>
        <el-button type="warning" :disabled="selectedTasks.length === 0" @click="handleBatchStop">
          批量停止
        </el-button>
      </div>
      <div class="toolbar-right">
        <el-input
          v-model="searchQuery"
          placeholder="搜索任务名称或商品"
          :prefix-icon="Search"
          style="width: 300px"
          clearable
        />
      </div>
    </div>

    <!-- 任务列表 -->
    <el-card class="table-card">
      <el-table
        :data="filteredTasks"
        v-loading="loading"
        @selection-change="handleSelectionChange"
        stripe
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="name" label="任务名称" min-width="200" />
        <el-table-column prop="product_info" label="监控商品" width="300">
          <template #default="{ row }">
            <div class="product-info">
              <el-image
                :src="row.product_info.image"
                :alt="row.product_info.title"
                style="width: 50px; height: 50px"
                fit="cover"
              />
              <div class="product-details">
                <div class="product-title">{{ row.product_info.title }}</div>
                <div class="product-id">ID: {{ row.product_info.id }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="data_source" label="数据源" width="150">
          <template #default="{ row }">
            <el-tag type="info" size="small">{{ row.data_source.name }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="frequency" label="监控频率" width="120">
          <template #default="{ row }">
            {{ getFrequencyLabel(row.frequency) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)" size="small">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="found_count" label="发现同款" width="100">
          <template #default="{ row }">
            <el-badge :value="row.found_count" :max="999" type="danger">
              <span>{{ row.found_count }}</span>
            </el-badge>
          </template>
        </el-table-column>
        <el-table-column prop="last_scan" label="最后扫描" width="180">
          <template #default="{ row }">
            {{ row.last_scan ? new Date(row.last_scan).toLocaleString() : '未扫描' }}
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="{ row }">
            {{ new Date(row.created_at).toLocaleString() }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="viewResults(row)">查看结果</el-button>
            <el-button size="small" @click="editTask(row)">编辑</el-button>
            <el-button
              size="small"
              :type="row.status === 'running' ? 'warning' : 'success'"
              @click="toggleTaskStatus(row)"
            >
              {{ row.status === 'running' ? '停止' : '启动' }}
            </el-button>
            <el-button size="small" type="danger" @click="deleteTask(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="totalTasks"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 创建/编辑任务对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editingTask ? '编辑监控任务' : '创建监控任务'"
      width="700px"
      @close="resetForm"
    >
      <el-form
        ref="taskFormRef"
        :model="taskForm"
        :rules="taskFormRules"
        label-width="120px"
      >
        <el-form-item label="任务名称" prop="name">
          <el-input v-model="taskForm.name" placeholder="请输入任务名称" />
        </el-form-item>
        
        <el-form-item label="商品信息" prop="product_type">
          <el-radio-group v-model="taskForm.product_type">
            <el-radio value="id">商品ID</el-radio>
            <el-radio value="url">商品链接</el-radio>
            <el-radio value="image">商品图片</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item v-if="taskForm.product_type === 'id'" label="商品ID" prop="product_id">
          <el-input v-model="taskForm.product_id" placeholder="请输入商品ID" />
        </el-form-item>
        
        <el-form-item v-if="taskForm.product_type === 'url'" label="商品链接" prop="product_url">
          <el-input v-model="taskForm.product_url" placeholder="请输入商品链接" />
        </el-form-item>
        
        <el-form-item v-if="taskForm.product_type === 'image'" label="商品图片" prop="product_image">
          <el-upload
            class="image-uploader"
            :show-file-list="false"
            :before-upload="beforeImageUpload"
            action="#"
          >
            <img v-if="taskForm.product_image" :src="taskForm.product_image" class="uploaded-image" />
            <el-icon v-else class="image-uploader-icon"><Plus /></el-icon>
          </el-upload>
        </el-form-item>
        
        <el-form-item label="数据源" prop="data_source_id">
          <el-select v-model="taskForm.data_source_id" placeholder="请选择数据源" style="width: 100%">
            <el-option
              v-for="source in dataSources"
              :key="source.id"
              :label="source.name"
              :value="source.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="监控频率" prop="frequency">
          <el-select v-model="taskForm.frequency" placeholder="请选择监控频率" style="width: 100%">
            <el-option label="每小时" value="hourly" />
            <el-option label="每6小时" value="6hours" />
            <el-option label="每12小时" value="12hours" />
            <el-option label="每天" value="daily" />
            <el-option label="每周" value="weekly" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="相似度阈值" prop="similarity_threshold">
          <el-slider
            v-model="taskForm.similarity_threshold"
            :min="50"
            :max="100"
            :step="5"
            show-stops
            show-input
          />
          <div class="form-desc">设置商品相似度匹配的最低阈值（%）</div>
        </el-form-item>
        
        <el-form-item label="价格范围筛选">
          <el-checkbox v-model="taskForm.enable_price_filter">启用价格筛选</el-checkbox>
        </el-form-item>
        
        <el-form-item v-if="taskForm.enable_price_filter" label="价格范围">
          <div class="price-range">
            <el-input-number v-model="taskForm.min_price" :min="0" placeholder="最低价" />
            <span style="margin: 0 10px">至</span>
            <el-input-number v-model="taskForm.max_price" :min="0" placeholder="最高价" />
          </div>
        </el-form-item>
        
        <el-form-item label="通知设置">
          <el-checkbox-group v-model="taskForm.notifications">
            <el-checkbox value="email">邮件通知</el-checkbox>
            <el-checkbox value="internal">站内消息</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        
        <el-form-item label="备注">
          <el-input
            v-model="taskForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入任务备注"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="saveTask" :loading="saving">
          {{ editingTask ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 扫描结果对话框 -->
    <el-dialog v-model="showResultsDialog" title="同款扫描结果" width="1000px">
      <div class="scan-results" v-if="selectedTaskResults">
        <div class="results-header">
          <h3>{{ selectedTaskResults.name }} - 扫描结果</h3>
          <el-button type="primary" @click="runManualScan" :loading="scanning">
            手动扫描
          </el-button>
        </div>
        
        <el-table :data="selectedTaskResults.results" stripe>
          <el-table-column prop="image" label="商品图片" width="80">
            <template #default="{ row }">
              <el-image
                :src="row.image"
                style="width: 60px; height: 60px"
                fit="cover"
              />
            </template>
          </el-table-column>
          <el-table-column prop="title" label="商品标题" min-width="200" show-overflow-tooltip />
          <el-table-column prop="shop_name" label="店铺名称" width="150" />
          <el-table-column prop="price" label="价格" width="100">
            <template #default="{ row }">
              ¥{{ row.price }}
            </template>
          </el-table-column>
          <el-table-column prop="similarity" label="相似度" width="100">
            <template #default="{ row }">
              <el-progress
                :percentage="row.similarity"
                :color="getSimilarityColor(row.similarity)"
                :stroke-width="6"
                text-inside
              />
            </template>
          </el-table-column>
          <el-table-column prop="platform" label="平台" width="100">
            <template #default="{ row }">
              <el-tag size="small">{{ row.platform }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="found_at" label="发现时间" width="180">
            <template #default="{ row }">
              {{ new Date(row.found_at).toLocaleString() }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120">
            <template #default="{ row }">
              <el-button size="small" @click="viewProduct(row)">查看</el-button>
              <el-button size="small" type="danger" @click="reportProduct(row)">举报</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search } from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const scanning = ref(false)
const searchQuery = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const totalTasks = ref(0)
const selectedTasks = ref([])

// 对话框状态
const showCreateDialog = ref(false)
const showResultsDialog = ref(false)
const editingTask = ref(null)
const selectedTaskResults = ref(null)

// 表单数据
const taskForm = ref({
  name: '',
  product_type: 'id',
  product_id: '',
  product_url: '',
  product_image: '',
  data_source_id: null,
  frequency: 'daily',
  similarity_threshold: 80,
  enable_price_filter: false,
  min_price: null,
  max_price: null,
  notifications: ['internal'],
  description: ''
})

// 表单验证规则
const taskFormRules = {
  name: [
    { required: true, message: '请输入任务名称', trigger: 'blur' }
  ],
  product_id: [
    { required: true, message: '请输入商品ID', trigger: 'blur' }
  ],
  product_url: [
    { required: true, message: '请输入商品链接', trigger: 'blur' }
  ],
  product_image: [
    { required: true, message: '请上传商品图片', trigger: 'change' }
  ],
  data_source_id: [
    { required: true, message: '请选择数据源', trigger: 'change' }
  ],
  frequency: [
    { required: true, message: '请选择监控频率', trigger: 'change' }
  ]
}

// 模拟数据
const tasks = ref([
  {
    id: 1,
    name: '品牌A手机壳监控',
    product_info: {
      id: 'TB123456789',
      title: '苹果iPhone 14 Pro Max 透明手机壳',
      image: 'http://via.placeholder.com/100x100'
    },
    data_source: { id: 1, name: '淘宝同款搜索接口' },
    frequency: 'daily',
    status: 'running',
    found_count: 15,
    last_scan: '2024-01-15T10:30:00Z',
    created_at: '2024-01-10T00:00:00Z'
  },
  {
    id: 2,
    name: '品牌B数据线监控',
    product_info: {
      id: 'TB987654321',
      title: 'Type-C数据线 快充线 1米',
      image: 'http://via.placeholder.com/100x100'
    },
    data_source: { id: 2, name: '京东同款搜索接口' },
    frequency: '12hours',
    status: 'stopped',
    found_count: 8,
    last_scan: '2024-01-14T15:20:00Z',
    created_at: '2024-01-12T00:00:00Z'
  }
])

const dataSources = ref([
  { id: 1, name: '淘宝同款搜索接口' },
  { id: 2, name: '京东同款搜索接口' },
  { id: 3, name: '拼多多同款搜索接口' }
])

// 计算属性
const filteredTasks = computed(() => {
  if (!searchQuery.value) return tasks.value
  return tasks.value.filter(task => 
    task.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
    task.product_info.title.toLowerCase().includes(searchQuery.value.toLowerCase())
  )
})

// 方法
const getFrequencyLabel = (frequency) => {
  const labels = {
    hourly: '每小时',
    '6hours': '每6小时',
    '12hours': '每12小时',
    daily: '每天',
    weekly: '每周'
  }
  return labels[frequency] || frequency
}

const getStatusTagType = (status) => {
  const types = {
    running: 'success',
    stopped: 'info',
    error: 'danger'
  }
  return types[status] || 'info'
}

const getStatusLabel = (status) => {
  const labels = {
    running: '运行中',
    stopped: '已停止',
    error: '错误'
  }
  return labels[status] || status
}

const getSimilarityColor = (similarity) => {
  if (similarity >= 90) return '#67c23a'
  if (similarity >= 80) return '#e6a23c'
  if (similarity >= 70) return '#f56c6c'
  return '#909399'
}

const handleSelectionChange = (selection) => {
  selectedTasks.value = selection
}

const handleSizeChange = (size) => {
  pageSize.value = size
  loadTasks()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  loadTasks()
}

const editTask = (task) => {
  editingTask.value = task
  taskForm.value = {
    name: task.name,
    product_type: 'id',
    product_id: task.product_info.id,
    product_url: '',
    product_image: '',
    data_source_id: task.data_source.id,
    frequency: task.frequency,
    similarity_threshold: 80,
    enable_price_filter: false,
    min_price: null,
    max_price: null,
    notifications: ['internal'],
    description: ''
  }
  showCreateDialog.value = true
}

const viewResults = (task) => {
  selectedTaskResults.value = {
    ...task,
    results: [
      {
        id: 1,
        title: '苹果iPhone 14 Pro Max 透明手机壳 - 高品质',
        image: 'http://via.placeholder.com/100x100',
        shop_name: '某某数码专营店',
        price: 29.9,
        similarity: 95,
        platform: '淘宝',
        found_at: '2024-01-15T10:30:00Z'
      },
      {
        id: 2,
        title: 'iPhone14ProMax手机壳透明防摔',
        image: 'http://via.placeholder.com/100x100',
        shop_name: '手机配件店',
        price: 19.9,
        similarity: 88,
        platform: '拼多多',
        found_at: '2024-01-15T09:15:00Z'
      }
    ]
  }
  showResultsDialog.value = true
}

const toggleTaskStatus = async (task) => {
  try {
    // TODO: 调用API切换任务状态
    task.status = task.status === 'running' ? 'stopped' : 'running'
    ElMessage.success(`任务已${task.status === 'running' ? '启动' : '停止'}`)
  } catch (error) {
    ElMessage.error('操作失败')
  }
}

const deleteTask = async (task) => {
  try {
    await ElMessageBox.confirm(`确定要删除监控任务 "${task.name}" 吗？`, '确认删除', {
      type: 'warning'
    })
    
    // TODO: 调用API删除任务
    const index = tasks.value.findIndex(t => t.id === task.id)
    if (index > -1) {
      tasks.value.splice(index, 1)
    }
    
    ElMessage.success('删除成功')
  } catch (error) {
    // 用户取消
  }
}

const handleBatchStart = async () => {
  try {
    // TODO: 调用API批量启动任务
    selectedTasks.value.forEach(task => {
      task.status = 'running'
    })
    ElMessage.success(`已启动 ${selectedTasks.value.length} 个任务`)
  } catch (error) {
    ElMessage.error('操作失败')
  }
}

const handleBatchStop = async () => {
  try {
    // TODO: 调用API批量停止任务
    selectedTasks.value.forEach(task => {
      task.status = 'stopped'
    })
    ElMessage.success(`已停止 ${selectedTasks.value.length} 个任务`)
  } catch (error) {
    ElMessage.error('操作失败')
  }
}

const saveTask = async () => {
  saving.value = true
  try {
    if (editingTask.value) {
      // 更新任务
      const index = tasks.value.findIndex(t => t.id === editingTask.value.id)
      if (index > -1) {
        tasks.value[index] = {
          ...tasks.value[index],
          name: taskForm.value.name,
          frequency: taskForm.value.frequency
        }
      }
      ElMessage.success('任务更新成功')
    } else {
      // 创建任务
      const newTask = {
        id: Date.now(),
        name: taskForm.value.name,
        product_info: {
          id: taskForm.value.product_id,
          title: '新商品',
          image: 'http://via.placeholder.com/100x100'
        },
        data_source: dataSources.value.find(ds => ds.id === taskForm.value.data_source_id),
        frequency: taskForm.value.frequency,
        status: 'stopped',
        found_count: 0,
        last_scan: null,
        created_at: new Date().toISOString()
      }
      tasks.value.push(newTask)
      ElMessage.success('任务创建成功')
    }
    showCreateDialog.value = false
    resetForm()
  } catch (error) {
    ElMessage.error('操作失败')
  } finally {
    saving.value = false
  }
}

const runManualScan = async () => {
  scanning.value = true
  try {
    // TODO: 调用API执行手动扫描
    await new Promise(resolve => setTimeout(resolve, 3000))
    ElMessage.success('扫描完成')
  } catch (error) {
    ElMessage.error('扫描失败')
  } finally {
    scanning.value = false
  }
}

const viewProduct = (product) => {
  // TODO: 打开商品详情页面
  ElMessage.info('查看商品详情功能开发中...')
}

const reportProduct = async (product) => {
  try {
    await ElMessageBox.confirm('确定要举报这个商品吗？', '确认举报', {
      type: 'warning'
    })
    
    // TODO: 调用API举报商品
    ElMessage.success('举报成功')
  } catch (error) {
    // 用户取消
  }
}

const beforeImageUpload = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }

  // 模拟上传
  const reader = new FileReader()
  reader.onload = (e) => {
    taskForm.value.product_image = e.target.result
  }
  reader.readAsDataURL(file)
  
  return false // 阻止自动上传
}

const resetForm = () => {
  taskForm.value = {
    name: '',
    product_type: 'id',
    product_id: '',
    product_url: '',
    product_image: '',
    data_source_id: null,
    frequency: 'daily',
    similarity_threshold: 80,
    enable_price_filter: false,
    min_price: null,
    max_price: null,
    notifications: ['internal'],
    description: ''
  }
  editingTask.value = null
}

const loadTasks = async () => {
  loading.value = true
  try {
    // TODO: 调用API加载任务列表
    totalTasks.value = tasks.value.length
  } catch (error) {
    ElMessage.error('加载任务列表失败')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadTasks()
})
</script>

<style scoped lang="scss">
.similar-monitoring {
  .page-header {
    margin-bottom: 24px;
    
    h1 {
      font-size: 24px;
      font-weight: 600;
      margin-bottom: 8px;
      color: #303133;
    }
    
    p {
      color: #606266;
      margin: 0;
    }
  }
  
  .toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    .toolbar-left {
      display: flex;
      gap: 12px;
    }
  }
  
  .table-card {
    .product-info {
      display: flex;
      align-items: center;
      gap: 12px;
      
      .product-details {
        flex: 1;
        
        .product-title {
          font-weight: 500;
          color: #303133;
          margin-bottom: 4px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        
        .product-id {
          font-size: 12px;
          color: #909399;
        }
      }
    }
    
    .pagination-wrapper {
      display: flex;
      justify-content: flex-end;
      margin-top: 16px;
    }
  }
  
  .form-desc {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
  }
  
  .price-range {
    display: flex;
    align-items: center;
  }
  
  .image-uploader {
    :deep(.el-upload) {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      transition: all 0.3s;
      
      &:hover {
        border-color: #409eff;
      }
    }
    
    .image-uploader-icon {
      font-size: 28px;
      color: #8c939d;
      width: 100px;
      height: 100px;
      text-align: center;
      line-height: 100px;
    }
    
    .uploaded-image {
      width: 100px;
      height: 100px;
      display: block;
    }
  }
  
  .scan-results {
    .results-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      
      h3 {
        margin: 0;
        color: #303133;
      }
    }
  }
}
</style> 