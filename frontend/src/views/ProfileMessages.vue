<template>
  <div class="profile-messages">
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">我的消息</h1>
        <p class="page-description">查看系统通知、预警消息和站内信</p>
      </div>
      <div class="header-actions">
        <el-button :icon="Check" @click="markAllRead" :disabled="unreadCount === 0">
          全部标记已读
        </el-button>
        <el-button :icon="Delete" @click="clearAll">
          清空消息
        </el-button>
      </div>
    </div>

    <div class="messages-container">
      <!-- 消息筛选 -->
      <el-card class="filter-card">
        <el-form :model="filter" :inline="true">
          <el-form-item label="消息类型">
            <el-select v-model="filter.type" placeholder="全部类型" clearable @change="loadMessages">
              <el-option label="系统通知" value="system" />
              <el-option label="预警消息" value="alert" />
              <el-option label="任务通知" value="task" />
              <el-option label="站内信" value="message" />
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="filter.status" placeholder="全部状态" clearable @change="loadMessages">
              <el-option label="未读" value="unread" />
              <el-option label="已读" value="read" />
            </el-select>
          </el-form-item>
          <el-form-item label="时间范围">
            <el-date-picker
              v-model="filter.dateRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              @change="loadMessages"
            />
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 消息统计 -->
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-icon unread">
            <el-icon><Bell /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ unreadCount }}</div>
            <div class="stat-label">未读消息</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon total">
            <el-icon><Message /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ totalCount }}</div>
            <div class="stat-label">总消息数</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon alert">
            <el-icon><Warning /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ alertCount }}</div>
            <div class="stat-label">预警消息</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon system">
            <el-icon><Setting /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ systemCount }}</div>
            <div class="stat-label">系统通知</div>
          </div>
        </div>
      </div>

      <!-- 消息列表 -->
      <el-card class="messages-card">
        <div v-loading="loading" class="messages-list">
          <div 
            v-for="message in messages" 
            :key="message.id" 
            class="message-item"
            :class="{ 'unread': !message.isRead }"
            @click="handleMessageClick(message)"
          >
            <div class="message-icon">
              <el-icon v-if="message.type === 'system'" class="system-icon"><Setting /></el-icon>
              <el-icon v-else-if="message.type === 'alert'" class="alert-icon"><Warning /></el-icon>
              <el-icon v-else-if="message.type === 'task'" class="task-icon"><Document /></el-icon>
              <el-icon v-else class="message-icon-default"><Message /></el-icon>
            </div>
            
            <div class="message-content">
              <div class="message-header">
                <h4 class="message-title">{{ message.title }}</h4>
                <div class="message-meta">
                  <el-tag :type="getTypeColor(message.type)" size="small">
                    {{ getTypeName(message.type) }}
                  </el-tag>
                  <span class="message-time">{{ formatTime(message.createdAt) }}</span>
                </div>
              </div>
              <p class="message-summary">{{ message.content }}</p>
              <div class="message-actions" v-if="!message.isRead">
                <el-button size="small" link @click.stop="markAsRead(message)">
                  标记已读
                </el-button>
              </div>
            </div>

            <div class="message-status">
              <el-badge v-if="!message.isRead" is-dot />
            </div>
          </div>

          <!-- 空状态 -->
          <el-empty v-if="messages.length === 0 && !loading" description="暂无消息" />
        </div>

        <!-- 分页 -->
        <div class="pagination-wrapper" v-if="pagination.total > 0">
          <el-pagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.size"
            :page-sizes="[10, 20, 50]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>

    <!-- 消息详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      :title="selectedMessage?.title"
      width="600px"
    >
      <div v-if="selectedMessage" class="message-detail">
        <div class="detail-meta">
          <el-tag :type="getTypeColor(selectedMessage.type)">
            {{ getTypeName(selectedMessage.type) }}
          </el-tag>
          <span class="detail-time">{{ formatTime(selectedMessage.createdAt) }}</span>
        </div>
        <div class="detail-content">
          {{ selectedMessage.content }}
        </div>
        <div v-if="selectedMessage.data" class="detail-data">
          <h4>相关数据</h4>
          <pre>{{ JSON.stringify(selectedMessage.data, null, 2) }}</pre>
        </div>
      </div>
      <template #footer>
        <el-button @click="showDetailDialog = false">关闭</el-button>
        <el-button v-if="!selectedMessage?.isRead" type="primary" @click="markAsRead(selectedMessage)">
          标记已读
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Check, Delete, Bell, Message, Warning, Setting, Document } from '@element-plus/icons-vue'
import notificationApi from '@/api/notifications'

// 响应式数据
const messages = ref([])
const loading = ref(false)
const showDetailDialog = ref(false)
const selectedMessage = ref(null)

// 筛选条件
const filter = ref({
  type: '',
  status: '',
  dateRange: []
})

// 分页
const pagination = ref({
  page: 1,
  size: 20,
  total: 0
})

// 计算属性
const unreadCount = computed(() => 
  messages.value.filter(msg => !msg.isRead).length
)

const totalCount = computed(() => messages.value.length)

const alertCount = computed(() => 
  messages.value.filter(msg => msg.type === 'alert').length
)

const systemCount = computed(() => 
  messages.value.filter(msg => msg.type === 'system').length
)

// 生命周期
onMounted(() => {
  loadMessages()
})

// 方法
const loadMessages = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.value.page,
      per_page: pagination.value.size,
      type: filter.value.type || undefined,
      read_status: filter.value.status || undefined
    }

    const response = await notificationApi.getNotifications(params)

    if (response.data.success) {
      messages.value = response.data.data.data || []
      pagination.value.total = response.data.data.total || 0
    } else {
      ElMessage.error('获取消息列表失败')
    }
  } catch (error) {
    console.error('获取消息列表失败:', error)
    ElMessage.error('获取消息列表失败')
  } finally {
    loading.value = false
  }
}

const handleMessageClick = (message) => {
  selectedMessage.value = message
  showDetailDialog.value = true
  
  // 如果是未读消息，自动标记为已读
  if (!message.isRead) {
    markAsRead(message)
  }
}

const markAsRead = async (message) => {
  try {
    const response = await notificationApi.markAsRead(message.id)
    if (response.data.success) {
      message.isRead = true
      ElMessage.success('已标记为已读')
    } else {
      ElMessage.error('操作失败')
    }
  } catch (error) {
    console.error('标记已读失败:', error)
    ElMessage.error('操作失败')
  }
}

const markAllRead = async () => {
  try {
    await ElMessageBox.confirm('确定要将所有未读消息标记为已读吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const response = await notificationApi.markAllAsRead()
    if (response.data.success) {
      messages.value.forEach(msg => {
        msg.isRead = true
      })
      ElMessage.success('所有消息已标记为已读')
    } else {
      ElMessage.error('操作失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('标记所有已读失败:', error)
      ElMessage.error('操作失败')
    }
  }
}

const clearAll = async () => {
  try {
    await ElMessageBox.confirm('确定要清空所有消息吗？此操作不可恢复。', '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const response = await notificationApi.clearAll()
    if (response.data.success) {
      messages.value = []
      pagination.value.total = 0
      ElMessage.success('所有消息已清空')
    } else {
      ElMessage.error('操作失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('清空消息失败:', error)
      ElMessage.error('操作失败')
    }
  }
}

const handleSizeChange = (size) => {
  pagination.value.size = size
  loadMessages()
}

const handleCurrentChange = (page) => {
  pagination.value.page = page
  loadMessages()
}

// 辅助方法
const getTypeColor = (type) => {
  const colors = {
    'system': 'info',
    'alert': 'danger',
    'task': 'warning',
    'message': 'success'
  }
  return colors[type] || 'info'
}

const getTypeName = (type) => {
  const names = {
    'system': '系统通知',
    'alert': '预警消息',
    'task': '任务通知',
    'message': '站内信'
  }
  return names[type] || type
}

const formatTime = (time) => {
  const now = new Date()
  const msgTime = new Date(time)
  const diff = now.getTime() - msgTime.getTime()
  
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (minutes < 60) {
    return `${minutes}分钟前`
  } else if (hours < 24) {
    return `${hours}小时前`
  } else if (days < 7) {
    return `${days}天前`
  } else {
    return msgTime.toLocaleDateString()
  }
}
</script>

<style scoped>
.profile-messages {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-content h1 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-description {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
}

.filter-card {
  margin-bottom: 20px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 20px;
}

.stat-icon.unread {
  background: #fef0f0;
  color: #f56c6c;
}

.stat-icon.total {
  background: #f0f9ff;
  color: #409eff;
}

.stat-icon.alert {
  background: #fdf6ec;
  color: #e6a23c;
}

.stat-icon.system {
  background: #f4f4f5;
  color: #909399;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

.messages-card {
  margin-bottom: 20px;
}

.messages-list {
  min-height: 400px;
}

.message-item {
  display: flex;
  align-items: flex-start;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.message-item:hover {
  background-color: #f8f9fa;
}

.message-item.unread {
  background-color: #f0f9ff;
}

.message-item:last-child {
  border-bottom: none;
}

.message-icon {
  margin-right: 12px;
  margin-top: 4px;
}

.system-icon {
  color: #909399;
}

.alert-icon {
  color: #f56c6c;
}

.task-icon {
  color: #e6a23c;
}

.message-icon-default {
  color: #409eff;
}

.message-content {
  flex: 1;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.message-title {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  line-height: 1.4;
}

.message-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.message-time {
  font-size: 12px;
  color: #909399;
}

.message-summary {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #606266;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.message-actions {
  margin-top: 8px;
}

.message-status {
  margin-left: 12px;
  margin-top: 8px;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: center;
}

.message-detail {
  line-height: 1.6;
}

.detail-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.detail-time {
  font-size: 14px;
  color: #909399;
}

.detail-content {
  font-size: 14px;
  color: #303133;
  margin-bottom: 16px;
}

.detail-data {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.detail-data h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #303133;
}

.detail-data pre {
  background: #f8f9fa;
  padding: 12px;
  border-radius: 4px;
  font-size: 12px;
  color: #606266;
  overflow-x: auto;
}
</style> 