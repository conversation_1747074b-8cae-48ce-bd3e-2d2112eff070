<template>
  <div class="login-container">
    <div class="login-form">
      <div class="login-header">
        <h1>电商市场动态监测系统</h1>
        <p>用户登录</p>
      </div>
      
      <el-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginRules"
        class="login-form-content"
        @submit.prevent="handleLogin"
      >
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            placeholder="请输入用户名或邮箱"
            size="large"
            prefix-icon="User"
            clearable
          />
        </el-form-item>
        
        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="请输入密码"
            size="large"
            prefix-icon="Lock"
            show-password
          />
        </el-form-item>
        
        <el-form-item prop="captcha">
          <div class="captcha-row">
            <el-input
              v-model="loginForm.captcha"
              placeholder="请输入验证码"
              size="large"
              prefix-icon="Key"
              clearable
              style="flex: 1;"
              @keyup.enter="handleLogin"
            />
            <Captcha ref="captchaRef" @update:value="handleCaptchaUpdate" />
          </div>
        </el-form-item>
        
        <el-form-item>
          <div class="login-options">
            <el-checkbox v-model="loginForm.remember" size="large">
              记住我
            </el-checkbox>
            <el-link type="primary" @click="handleForgotPassword">
              忘记密码？
            </el-link>
          </div>
        </el-form-item>
        
        <el-form-item>
          <el-button
            type="primary"
            size="large"
            :loading="authStore.isLoading"
            @click="handleLogin"
            class="login-button"
          >
            {{ authStore.isLoading ? '登录中...' : '立即登录' }}
          </el-button>
        </el-form-item>
      </el-form>
      
      <div class="login-footer">
        <div class="default-account">
          <p>默认管理员账号：admin / admin123</p>
        </div>
        <div class="register-link">
          <p>
            还没有账号？
            <el-link type="primary" @click="goToRegister">立即注册</el-link>
          </p>
        </div>
      </div>
    </div>
    
    <!-- 忘记密码对话框 -->
    <el-dialog
      v-model="forgotPasswordVisible"
      title="忘记密码"
      width="400px"
      :before-close="handleForgotPasswordClose"
    >
      <el-form
        ref="forgotPasswordFormRef"
        :model="forgotPasswordForm"
        :rules="forgotPasswordRules"
        label-width="80px"
      >
        <el-form-item label="邮箱" prop="email">
          <el-input
            v-model="forgotPasswordForm.email"
            placeholder="请输入注册邮箱"
            type="email"
            clearable
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="forgotPasswordVisible = false">取消</el-button>
          <el-button 
            type="primary" 
            :loading="forgotPasswordLoading"
            @click="handleSendResetEmail"
          >
            发送重置邮件
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElForm, ElMessage } from 'element-plus'
import { useAuthStore } from '../stores/auth'
import Captcha from '../components/Captcha.vue'

const router = useRouter()
const authStore = useAuthStore()

// 表单引用
const loginFormRef = ref<InstanceType<typeof ElForm>>()
const forgotPasswordFormRef = ref<InstanceType<typeof ElForm>>()
const captchaRef = ref<InstanceType<typeof Captcha>>()

// 状态管理
const forgotPasswordVisible = ref(false)
const forgotPasswordLoading = ref(false)
const captchaValue = ref('')

// 表单数据
const loginForm = reactive({
  username: '',
  password: '',
  captcha: '',
  remember: false,
})

const forgotPasswordForm = reactive({
  email: '',
})

// 自定义验证器
const validateCaptcha = (rule: any, value: string, callback: any) => {
  if (!value) {
    callback(new Error('请输入验证码'))
  } else if (!captchaRef.value?.verify(value)) {
    callback(new Error('验证码不正确'))
  } else {
    callback()
  }
}

// 表单验证规则
const loginRules = {
  username: [
    { required: true, message: '请输入用户名或邮箱', trigger: 'blur' },
    { min: 3, max: 50, message: '用户名长度在 3 到 50 个字符', trigger: 'blur' },
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 100, message: '密码长度在 6 到 100 个字符', trigger: 'blur' },
  ],
  captcha: [
    { validator: validateCaptcha, trigger: 'blur' },
  ],
}

const forgotPasswordRules = {
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' },
  ],
}

// 处理验证码更新
const handleCaptchaUpdate = (value: string) => {
  captchaValue.value = value
}

// 处理登录
const handleLogin = async () => {
  if (!loginFormRef.value) return
  
  try {
    // 验证表单
    await loginFormRef.value.validate()
    
    // 执行登录
    const success = await authStore.login(loginForm.username, loginForm.password)
    
    if (success) {
      // 处理记住我功能
      if (loginForm.remember) {
        localStorage.setItem('remember_username', loginForm.username)
      } else {
        localStorage.removeItem('remember_username')
      }
      
      ElMessage.success('登录成功！')
      
      // 登录成功，跳转到首页
      router.push('/')
    } else {
      // 登录失败，刷新验证码
      captchaRef.value?.refresh()
      loginForm.captcha = ''
    }
  } catch (error) {
    console.error('登录表单验证失败:', error)
    // 验证码错误时刷新验证码
    if (loginForm.captcha && !captchaRef.value?.verify(loginForm.captcha)) {
      captchaRef.value?.refresh()
      loginForm.captcha = ''
    }
  }
}

// 处理忘记密码
const handleForgotPassword = () => {
  forgotPasswordVisible.value = true
}

// 发送重置密码邮件
const handleSendResetEmail = async () => {
  if (!forgotPasswordFormRef.value) return
  
  try {
    await forgotPasswordFormRef.value.validate()
    
    forgotPasswordLoading.value = true
    
    // 模拟发送重置邮件
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    ElMessage.success('重置密码邮件已发送，请查收邮箱')
    forgotPasswordVisible.value = false
    
  } catch (error) {
    console.error('发送重置邮件失败:', error)
  } finally {
    forgotPasswordLoading.value = false
  }
}

// 处理忘记密码对话框关闭
const handleForgotPasswordClose = (done: () => void) => {
  forgotPasswordForm.email = ''
  done()
}

// 跳转到注册页面
const goToRegister = () => {
  router.push('/register')
}

// 组件挂载时检查是否已登录和记住的用户名
onMounted(() => {
  if (authStore.isAuthenticated) {
    router.push('/')
  }
  
  // 恢复记住的用户名
  const rememberedUsername = localStorage.getItem('remember_username')
  if (rememberedUsername) {
    loginForm.username = rememberedUsername
    loginForm.remember = true
  }
})
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #667eea 100%);
  background-size: 200% 200%;
  animation: gradientShift 6s ease infinite;
  padding: 20px;
  position: relative;
  overflow: hidden;
}

.login-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
              radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
              radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
  pointer-events: none;
}

@keyframes gradientShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

.login-form {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 40px;
  border-radius: 16px;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
  width: 100%;
  max-width: 420px;
  animation: slideUp 0.8s ease-out;
  position: relative;
  z-index: 1;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(40px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.login-header {
  text-align: center;
  margin-bottom: 32px;
}

.login-header h1 {
  color: #2c3e50;
  font-size: 26px;
  font-weight: 700;
  margin: 0 0 12px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.login-header p {
  color: #64748b;
  font-size: 16px;
  margin: 0;
  font-weight: 500;
}

.login-form-content {
  margin-bottom: 24px;
}

.captcha-row {
  display: flex;
  gap: 12px;
  align-items: center;
}

.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.login-button {
  width: 100%;
  height: 48px;
  font-size: 16px;
  font-weight: 600;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 8px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.login-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.login-button:hover::before {
  left: 100%;
}

.login-button:hover:not(:disabled) {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  transform: translateY(-2px);
  box-shadow: 0 12px 25px rgba(102, 126, 234, 0.4);
}

.login-button:active {
  transform: translateY(0);
}

.login-footer {
  text-align: center;
  color: #64748b;
  font-size: 14px;
}

.default-account {
  margin-bottom: 16px;
  padding: 12px;
  background: #f8fafc;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.default-account p {
  margin: 0;
  color: #475569;
  font-weight: 500;
}

.register-link p {
  margin: 0;
}

/* Element Plus组件样式覆盖 */
:deep(.el-input__wrapper) {
  border-radius: 8px;
  padding: 0 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

:deep(.el-input__wrapper:hover) {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.3);
}

:deep(.el-checkbox) {
  font-weight: 500;
}

:deep(.el-link) {
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .login-form {
    padding: 32px 24px;
    margin: 16px;
    max-width: none;
  }
  
  .login-header h1 {
    font-size: 22px;
  }
  
  .login-header p {
    font-size: 14px;
  }
  
  .captcha-row {
    flex-direction: column;
    align-items: stretch;
  }
  
  .login-options {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
  .login-form {
    background: rgba(30, 41, 59, 0.95);
    color: #e2e8f0;
  }
  
  .login-header h1 {
    color: #f1f5f9;
  }
  
  .login-header p {
    color: #94a3b8;
  }
  
  .default-account {
    background: rgba(51, 65, 85, 0.6);
    border-left-color: #667eea;
  }
  
  .default-account p {
    color: #cbd5e1;
  }
}
</style> 