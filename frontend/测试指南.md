# 队列采集数据测试指南

## 基础环境启动
```bash
# 1. 启动后端服务
cd /home/<USER>/php/website/backend
php artisan serve

# 2. 启动前端开发服务
cd /home/<USER>/php/website/frontend
npm run dev

# 3. 启动队列处理（重要！）
cd /home/<USER>/php/website/backend
php artisan queue:work --tries=3 --timeout=300
```

## 队列采集数据问题排查

### 问题诊断命令
```bash
# 查看队列相关日志
cd /home/<USER>/php/website/backend
tail -100 storage/logs/laravel.log | grep -i "queue\|job\|error\|exception"

# 查看数据库表结构
php artisan migrate:status
php artisan db:show

# 检查队列任务状态
php artisan queue:failed

# 重试失败的队列任务
php artisan queue:retry all

# 清空失败的队列任务
php artisan queue:flush
```

### 数据库检查
```bash
# 进入数据库查看数据
mysql -u your_username -p your_database

# 检查product_data表
SELECT id, monitoring_task_id, item_id, code, title, state, created_at 
FROM product_data 
ORDER BY created_at DESC 
LIMIT 10;

# 检查product_skus表
SELECT pd.item_id, ps.sku_id, ps.name, ps.price, ps.quantity 
FROM product_data pd 
LEFT JOIN product_skus ps ON pd.id = ps.product_data_id 
WHERE pd.created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR);

# 检查监控任务
SELECT id, name, status, last_run_at, success_count, failed_count 
FROM monitoring_tasks;
```

### 手动触发测试
```bash
# 手动分发监控任务（如果任务ID=2）
cd /home/<USER>/php/website/backend
php artisan tinker

# 在tinker中执行
$task = \App\Models\MonitoringTask::find(2);
\App\Jobs\ProcessDataSourceTask::dispatch(
    $task->data_source_id,
    $task->target_products,
    [],
    $task->id
);
```

### 常见问题解决

#### 1. "Array to string conversion" 错误
- **原因**：promotion字段数据类型转换问题
- **解决方案**：已修复DataCollectionService中的safeJson方法

#### 2. "Argument #3 ($itemId) must be of type ?string, array given" 错误  
- **原因**：handleException方法类型错误
- **解决方案**：已修复类型转换逻辑

#### 3. 数据采集成功但无法保存到数据库
- **检查**：数据库连接、字段映射、唯一键约束
- **解决**：检查monitoring_task_id和item_id的唯一约束

#### 4. 队列任务一直失败
```bash
# 检查队列配置
cat .env | grep QUEUE

# 重启队列处理
pkill -f "queue:work"
php artisan queue:work --tries=3 --timeout=300

# 检查数据源配置
php artisan tinker
\App\Models\DataSource::find(3)->toArray();
```

### 成功验证步骤

1. **检查队列日志无ERROR**：
   ```bash
   tail -50 storage/logs/laravel.log | grep -i error
   ```

2. **验证数据已保存**：
   ```bash
   # 检查product_data表有新记录
   # 检查product_skus表有对应SKU记录
   # 检查数据完整性
   ```

3. **前端界面确认**：
   - 监控任务状态正常
   - 商品数据显示完整
   - SKU信息正确展示

### 修复总结

**已修复的问题**：
1. ✅ DataCollectionService中promotion字段的JSON转换问题
2. ✅ handleException方法的类型安全问题  
3. ✅ updateOrCreate的唯一键组合问题（monitoring_task_id + item_id）
4. ✅ props字段的JSON转换问题

**关键修改**：
- 添加了`safeJson`函数处理JSON字段转换
- 修复了`handleException`方法的参数类型转换
- 更正了`updateOrCreate`使用`monitoring_task_id`而非`data_source_id`

现在队列采集数据应该可以正常工作并保存到数据库。如果仍有问题，请使用上述排查命令进行诊断。