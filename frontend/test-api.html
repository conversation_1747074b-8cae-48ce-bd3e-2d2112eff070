<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试</title>
</head>
<body>
    <h1>API测试页面</h1>
    
    <div>
        <h2>1. 登录测试</h2>
        <button onclick="testLogin()">测试登录</button>
        <div id="loginResult"></div>
    </div>
    
    <div>
        <h2>2. 通知API测试</h2>
        <button onclick="testNotifications()">测试获取通知</button>
        <div id="notificationResult"></div>
    </div>

    <script>
        let authToken = null;
        
        async function testLogin() {
            try {
                const response = await fetch('http://*************:8000/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        login: 'admin',
                        password: 'admin123'
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    authToken = data.data.token;
                    document.getElementById('loginResult').innerHTML = 
                        `<p style="color: green;">登录成功！Token: ${authToken.substring(0, 20)}...</p>`;
                } else {
                    document.getElementById('loginResult').innerHTML = 
                        `<p style="color: red;">登录失败: ${data.message}</p>`;
                }
            } catch (error) {
                document.getElementById('loginResult').innerHTML = 
                    `<p style="color: red;">登录错误: ${error.message}</p>`;
            }
        }
        
        async function testNotifications() {
            if (!authToken) {
                document.getElementById('notificationResult').innerHTML = 
                    '<p style="color: red;">请先登录获取Token</p>';
                return;
            }
            
            try {
                const response = await fetch('http://*************:8000/api/notifications', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                
                const data = await response.json();
                
                if (data.success) {
                    document.getElementById('notificationResult').innerHTML = 
                        `<p style="color: green;">获取通知成功！共 ${data.data.total} 条通知</p>
                         <pre>${JSON.stringify(data.data.data[0], null, 2)}</pre>`;
                } else {
                    document.getElementById('notificationResult').innerHTML = 
                        `<p style="color: red;">获取通知失败: ${data.message}</p>`;
                }
            } catch (error) {
                document.getElementById('notificationResult').innerHTML = 
                    `<p style="color: red;">请求错误: ${error.message}</p>`;
            }
        }
    </script>
</body>
</html>
