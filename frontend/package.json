{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.10.0", "echarts": "^5.6.0", "element-plus": "^2.10.2", "pinia": "^3.0.3", "vue": "^3.5.13", "vue-echarts": "^7.0.3", "vue-json-pretty": "^2.5.0", "vue-router": "^4.5.1", "xlsx": "^0.18.5"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.3", "@vue/tsconfig": "^0.7.0", "sass-embedded": "^1.89.2", "typescript": "~5.8.3", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.7.0", "vite": "^6.3.5", "vue-tsc": "^2.2.8"}}