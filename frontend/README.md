# 智能电商市场动态监测系统 - 前端

这是一个基于Vue 3 + TypeScript + Element Plus的现代化电商监测系统前端应用。

## 🎯 项目概述

智能电商市场动态监测与分析系统的核心理念是建立可配置的数据源层，将第三方API的"不确定性"（不同的URL、参数、返回字段）与系统内部的"确定性"（标准化的数据表和分析逻辑）解耦。

## 🏗️ 技术栈

- **前端框架**: Vue 3 (Composition API)
- **类型系统**: TypeScript
- **UI组件库**: Element Plus
- **状态管理**: Pinia
- **路由管理**: Vue Router 4
- **构建工具**: Vite
- **HTTP客户端**: Fetch API

## 📁 项目结构

```
frontend/
├── src/
│   ├── api/                    # API接口定义
│   │   ├── alerts.js          # 预警相关API
│   │   ├── analytics.js       # 分析相关API
│   │   ├── apiClient.js       # API客户端配置
│   │   ├── dataSource.js      # 数据源API
│   │   ├── monitoringTask.js  # 监控任务API
│   │   └── productData.js     # 商品数据API
│   ├── components/            # 公共组件
│   │   ├── AlertHistory.vue   # 预警历史组件
│   │   ├── AlertRulesManagement.vue # 预警规则管理
│   │   ├── AppLayout.vue      # 主布局组件
│   │   └── HelloWorld.vue     # 示例组件
│   ├── router/                # 路由配置
│   │   └── index.ts          # 路由定义
│   ├── stores/               # 状态管理
│   │   ├── auth.ts           # 认证状态
│   │   └── counter.ts        # 计数器示例
│   ├── views/                # 页面组件
│   │   ├── 渠道价格监测/
│   │   │   ├── ChannelPriceTaskManagement.vue
│   │   │   ├── ChannelPriceDashboard.vue
│   │   │   └── ChannelPriceAlerts.vue
│   │   ├── 竞品动态监测/
│   │   │   ├── CompetitorTaskManagement.vue
│   │   │   ├── CompetitorDashboard.vue
│   │   │   └── CompetitorAlerts.vue
│   │   ├── 相似同款查询/
│   │   │   ├── SimilarLiveSearch.vue
│   │   │   ├── SimilarMonitoring.vue
│   │   │   └── SimilarDashboard.vue
│   │   ├── 系统管理/
│   │   │   ├── DataSourceManagement.vue
│   │   │   ├── UserManagement.vue
│   │   │   ├── RoleManagement.vue
│   │   │   ├── SystemSettings.vue
│   │   │   └── AuditLogs.vue
│   │   ├── 个人中心/
│   │   │   ├── ProfileMessages.vue
│   │   │   └── ProfilePassword.vue
│   │   ├── Home.vue           # 首页
│   │   ├── Login.vue          # 登录页
│   │   └── About.vue          # 关于页
│   ├── App.vue               # 根组件
│   ├── main.ts              # 应用入口
│   └── style.css            # 全局样式
├── public/                  # 静态资源
├── package.json            # 项目配置
├── tsconfig.json          # TypeScript配置
├── vite.config.ts         # Vite配置
└── README.md              # 项目说明
```

## 🗺️ 页面架构

### 管理员视图
```
├── 业务监控 (Business Monitoring)
│   ├── 渠道价格监测 (Channel Price Monitoring)
│   │   ├── 任务管理 (Task Management)
│   │   ├── 数据看板 (Data Dashboard)
│   │   └── 预警中心 (Alert Center)
│   ├── 竞品动态监测 (Competitor Dynamics Monitoring)
│   │   ├── 任务管理 (Task Management)
│   │   ├── 数据看板 (Data Dashboard)
│   │   └── 预警中心 (Alert Center)
│   └── 相似同款查询 (Similar Product Search)
│       ├── 实时搜索 (Live Search)
│       ├── 监控任务 (Monitoring Tasks)
│       └── 数据看板 (Data Dashboard)
├── 系统管理 (System Management)
│   ├── 数据源管理 (Data Source Management)
│   ├── 用户管理 (User Management)
│   ├── 角色与权限 (Roles & Permissions)
│   ├── 系统设置 (System Settings)
│   └── 操作日志 (Audit Log)
└── 个人中心 (Profile)
    ├── 我的消息 (My Messages)
    └── 修改密码 (Change Password)
```

### 普通用户视图
```
├── 业务监控 (Business Monitoring)
│   ├── 渠道价格监测 (Channel Price Monitoring)
│   ├── 竞品动态监测 (Competitor Dynamics Monitoring)
│   └── 相似同款查询 (Similar Product Search)
└── 个人中心 (Profile)
    ├── 我的消息 (My Messages)
    └── 修改密码 (Change Password)
```

## 🔧 核心功能

### 1. 渠道价格监测
- **任务管理**: 创建监控任务组，添加商品，选择数据源，设置采集计划
- **数据看板**: 可视化展示监控数据，支持商品对比和数据导出
- **预警中心**: 设置预警规则，实时监控价格异动

### 2. 竞品动态监测
- **任务管理**: 管理竞品监控任务和分组
- **数据看板**: 竞品数据分析，价格趋势图，促销类型分析
- **预警中心**: 竞品价格偏差预警，促销强度监控

### 3. 相似同款查询
- **实时搜索**: 即时同款商品搜索工具
- **监控任务**: 长期同款追踪任务管理
- **数据看板**: 历史搜索结果展示和分析

### 4. 系统管理（仅管理员）
- **数据源管理**: 配置第三方API数据源，字段映射管理
- **用户管理**: 用户账户管理和权限分配
- **角色权限**: 角色定义和权限控制
- **系统设置**: 全局系统参数配置
- **操作日志**: 系统操作审计和日志查看

## 🎨 UI特性

### 现代化设计
- 渐变色头部导航
- 卡片式布局设计
- 响应式界面适配
- 深色模式支持

### 交互体验
- 流畅的动画过渡
- 直观的操作反馈
- 智能的表单验证
- 丰富的数据可视化

### 组件化架构
- 可复用的UI组件
- 统一的设计规范
- 模块化的页面结构
- 灵活的布局系统

## 🚀 快速开始

### 环境要求
- Node.js >= 16.0.0
- npm >= 8.0.0

### 安装依赖
```bash
cd frontend
npm install
```

### 开发环境运行
```bash
npm run dev
```

### 生产环境构建
```bash
npm run build
```

### 预览构建结果
```bash
npm run preview
```

## 🔐 权限控制

系统实现了基于角色的权限控制：

- **管理员**: 拥有所有功能权限，包括系统管理
- **普通用户**: 只能访问业务监控功能和个人中心

权限控制在路由层面实现，通过`meta.requiresRole`字段配置。

## 📱 响应式设计

系统采用响应式设计，支持多种设备：

- **桌面端**: 完整功能体验
- **平板端**: 优化的触控交互
- **移动端**: 简化的操作界面

## 🔄 状态管理

使用Pinia进行状态管理：

- **auth**: 用户认证状态
- **counter**: 示例计数器状态

状态管理遵循单向数据流原则，确保状态的可预测性。

## 🌐 API集成

前端与后端API的集成：

- 统一的API客户端配置
- 自动的认证token管理
- 错误处理和重试机制
- 请求/响应拦截器

## 🎯 开发规范

### 代码风格
- 使用TypeScript进行类型检查
- 遵循Vue 3 Composition API规范
- 统一的命名约定
- 完整的注释文档

### 组件开发
- 单文件组件(.vue)
- Props类型定义
- 事件命名规范
- 样式作用域隔离

### 路由管理
- 路由懒加载
- 路由守卫权限控制
- 动态面包屑导航
- 页面缓存策略

## 🔧 配置说明

### 环境变量
```bash
VITE_API_BASE_URL=http://127.0.0.1:8000/api  # 后端API地址
VITE_APP_TITLE=智能电商监测系统              # 应用标题
```

### 构建配置
- Vite配置文件: `vite.config.ts`
- TypeScript配置: `tsconfig.json`
- 包管理配置: `package.json`

## 📈 性能优化

- 组件懒加载
- 图片懒加载
- 虚拟滚动
- 代码分割
- 缓存策略

## 🐛 调试指南

### 开发工具
- Vue DevTools浏览器扩展
- TypeScript类型检查
- ESLint代码检查
- 浏览器开发者工具

### 常见问题
1. **API请求失败**: 检查后端服务是否启动，API地址是否正确
2. **权限访问错误**: 确认用户角色和路由权限配置
3. **样式显示异常**: 检查Element Plus主题配置

## 📚 参考资料

- [Vue 3 官方文档](https://vuejs.org/)
- [TypeScript 官方文档](https://www.typescriptlang.org/)
- [Element Plus 组件库](https://element-plus.org/)
- [Pinia 状态管理](https://pinia.vuejs.org/)
- [Vue Router 路由管理](https://router.vuejs.org/)

## 🤝 贡献指南

1. Fork项目仓库
2. 创建功能分支
3. 提交代码更改
4. 创建Pull Request
5. 等待代码审查

## 📄 许可证

本项目采用MIT许可证，详见LICENSE文件。

---

**项目维护**: 智能电商监测系统开发团队  
**最后更新**: 2024年1月15日
