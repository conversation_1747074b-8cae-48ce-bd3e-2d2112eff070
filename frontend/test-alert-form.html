<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Alert Form Test</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
</head>
<body>
    <div id="app">
        <el-form :model="currentRule" label-width="120px" style="max-width: 600px; margin: 20px;">
            <h2>预警规则测试</h2>
            
            <el-form-item label="规则类型">
                <el-select v-model="currentRule.rule_type" multiple placeholder="请选择规则类型" style="width: 100%">
                    <el-option
                        v-for="(label, value) in ruleOptions.rule_types"
                        :key="value"
                        :label="label"
                        :value="value"
                    />
                </el-select>
            </el-form-item>

            <!-- Debug info -->
            <div style="background: #f0f0f0; padding: 10px; margin: 10px 0; font-size: 12px;">
                <strong>调试信息:</strong><br>
                当前选中的规则类型: {{ JSON.stringify(currentRule.rule_type) }}<br>
                条件对象: {{ JSON.stringify(currentRule.conditions) }}<br>
                规则类型数组长度: {{ Array.isArray(currentRule.rule_type) ? currentRule.rule_type.length : 'Not array' }}
            </div>

            <!-- 为每个选中的规则类型显示对应的操作符和阈值 -->
            <div v-for="ruleType in currentRule.rule_type" :key="ruleType">
                <div style="background: #e0e0e0; padding: 5px; margin: 5px 0; font-size: 11px;">
                    处理规则类型: {{ ruleType }}<br>
                    需要操作符和阈值: {{ needsOperatorAndThreshold(ruleType) }}<br>
                    条件存在: {{ currentRule.conditions && currentRule.conditions[ruleType] ? 'Yes' : 'No' }}
                </div>
                <template v-if="needsOperatorAndThreshold(ruleType) && currentRule.conditions && currentRule.conditions[ruleType]">
                    <el-divider>{{ ruleOptions.rule_types[ruleType] }} 配置</el-divider>

                    <el-form-item :label="`${ruleOptions.rule_types[ruleType]} 操作符`">
                        <el-select v-model="currentRule.conditions[ruleType].operator" placeholder="请选择操作符" style="width: 100%">
                            <el-option
                                v-for="(label, value) in ruleOptions.operators"
                                :key="value"
                                :label="label"
                                :value="value"
                            />
                        </el-select>
                    </el-form-item>

                    <el-form-item :label="`${ruleOptions.rule_types[ruleType]} 阈值`">
                        <el-input-number
                            v-model="currentRule.conditions[ruleType].threshold"
                            placeholder="请输入阈值"
                            :precision="2"
                            :min="0"
                            :max="100"
                            style="width: 100%"
                        />
                    </el-form-item>
                </template>
            </div>
        </el-form>
    </div>

    <script>
        const { createApp, ref, watch } = Vue;
        const { ElMessage } = ElementPlus;

        createApp({
            setup() {
                const currentRule = ref({
                    rule_type: [],
                    conditions: {
                        promotion_price_deviation: { operator: '>', threshold: 10 },
                        channel_price_deviation: { operator: '>', threshold: 10 }
                    }
                });

                const ruleOptions = ref({
                    rule_types: {
                        promotion_price_deviation: '促销价偏离率',
                        channel_price_deviation: '渠道价格偏离率',
                        listing_status_change: '上下架状态变化'
                    },
                    operators: {
                        '>': '大于',
                        '>=': '大于等于',
                        '<': '小于',
                        '<=': '小于等于',
                        '=': '等于'
                    }
                });

                const needsOperatorAndThreshold = (ruleType) => {
                    return ruleType && ruleType !== 'listing_status_change';
                };

                // 监听rule_type变化
                watch(() => currentRule.value.rule_type, (newRuleTypes) => {
                    console.log('Rule types changed:', newRuleTypes);
                    
                    if (!currentRule.value.conditions) {
                        currentRule.value.conditions = {};
                    }
                    
                    if (Array.isArray(newRuleTypes)) {
                        newRuleTypes.forEach(ruleType => {
                            if (needsOperatorAndThreshold(ruleType) && !currentRule.value.conditions[ruleType]) {
                                console.log('Adding condition for rule type:', ruleType);
                                currentRule.value.conditions[ruleType] = {
                                    operator: '>',
                                    threshold: 10
                                };
                            }
                        });
                    }
                    
                    console.log('Current conditions:', currentRule.value.conditions);
                }, { deep: true });

                return {
                    currentRule,
                    ruleOptions,
                    needsOperatorAndThreshold
                };
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>
