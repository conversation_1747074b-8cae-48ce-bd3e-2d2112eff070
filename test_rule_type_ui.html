<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>优化后的规则类型选择器测试</title>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <style>
        body {
            margin: 20px;
            font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', SimSun, sans-serif;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .demo-section {
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid #EBEEF5;
            border-radius: 6px;
        }
        .section-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #303133;
        }
        
        /* 优化后的规则类型选择器样式 */
        .rule-type-selector {
            border: 1px solid #EBEEF5;
            border-radius: 6px;
            overflow: hidden;
        }
        
        .rule-type-group {
            border-bottom: 1px solid #EBEEF5;
        }
        
        .rule-type-group:last-child {
            border-bottom: none;
        }
        
        .group-title {
            background: #F5F7FA;
            padding: 12px 16px;
            font-size: 14px;
            font-weight: 500;
            color: #606266;
            border-bottom: 1px solid #EBEEF5;
        }
        
        .checkbox-group {
            padding: 16px;
            display: flex;
            flex-direction: column;
            gap: 12px;
        }
        
        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 8px;
            margin: 0;
            cursor: pointer;
        }
        
        .checkbox-item input[type="checkbox"] {
            width: 14px;
            height: 14px;
        }
        
        .checkbox-item label {
            cursor: pointer;
            user-select: none;
        }
        
        /* 原始多选下拉框样式 */
        .original-select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #DCDFE6;
            border-radius: 4px;
            background: white;
            font-size: 14px;
        }
        
        .condition-section {
            margin-top: 20px;
            padding: 16px;
            background: #F9FAFC;
            border-radius: 6px;
        }
        
        .operator-threshold {
            display: flex;
            gap: 12px;
            align-items: center;
            margin-top: 12px;
        }
        
        .operator-threshold select, .operator-threshold input {
            padding: 6px 10px;
            border: 1px solid #DCDFE6;
            border-radius: 4px;
        }
        
        .help-text {
            font-size: 12px;
            color: #909399;
            margin-top: 8px;
        }
        
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }
        
        .comparison h3 {
            color: #409EFF;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>渠道价格监测 - 预警规则类型选择器优化对比</h1>
        
        <div class="comparison">
            <!-- 原始设计 -->
            <div class="demo-section">
                <div class="section-title">🔴 原始设计（不直观）</div>
                <h3>规则类型（多选下拉框）</h3>
                <select class="original-select" multiple>
                    <option value="promotion_price_deviation">促销价偏离率</option>
                    <option value="channel_price_deviation">渠道价格偏离率</option>
                    <option value="listing_status_change">上下架状态</option>
                </select>
                <div class="help-text">问题：不直观，用户无法清楚看到不同类型之间的关系</div>
                
                <div class="condition-section">
                    <h4>操作符和阈值</h4>
                    <div class="operator-threshold">
                        <label>操作符：</label>
                        <select>
                            <option value=">">大于</option>
                            <option value="<">小于</option>
                            <option value=">=">大于等于</option>
                            <option value="<=">小于等于</option>
                        </select>
                        <label>阈值：</label>
                        <input type="number" value="10" min="0" max="100" step="0.01">
                        <span>%</span>
                    </div>
                    <div class="help-text">问题：与规则类型的对应关系不明确</div>
                </div>
            </div>
            
            <!-- 优化后设计 -->
            <div class="demo-section">
                <div class="section-title">✅ 优化后设计（直观明确）</div>
                <h3>规则类型（分组复选框）</h3>
                <div class="rule-type-selector">
                    <div class="rule-type-group">
                        <div class="group-title">价格偏离类预警（需要设置阈值）</div>
                        <div class="checkbox-group">
                            <div class="checkbox-item">
                                <input type="checkbox" id="promo1" checked>
                                <label for="promo1">促销价偏离率</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="channel1">
                                <label for="channel1">渠道价格偏离率</label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="rule-type-group">
                        <div class="group-title">状态变化类预警（自动触发）</div>
                        <div class="checkbox-group">
                            <div class="checkbox-item">
                                <input type="checkbox" id="status1">
                                <label for="status1">上下架状态</label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="help-text">✅ 优势：分组清晰，直观显示哪些需要阈值，哪些自动触发</div>
                
                <div class="condition-section">
                    <h4>操作符和阈值（当选择价格偏离类时显示）</h4>
                    <div class="operator-threshold">
                        <label>操作符：</label>
                        <select>
                            <option value=">">大于</option>
                            <option value="<">小于</option>
                            <option value=">=">大于等于</option>
                            <option value="<=">小于等于</option>
                        </select>
                        <label>阈值：</label>
                        <input type="number" value="10" min="0" max="100" step="0.01">
                        <span>%</span>
                    </div>
                    <div class="help-text">✅ 优势：与上方规则类型的对应关系清晰明确</div>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <div class="section-title">📋 优化说明</div>
            <h3>主要改进点：</h3>
            <ul>
                <li><strong>分组展示</strong>：将规则类型按功能分为"价格偏离类"和"状态变化类"，用户一眼就能看出分类</li>
                <li><strong>描述性标题</strong>：每个分组都有说明性文字，如"需要设置阈值"、"自动触发"</li>
                <li><strong>复选框界面</strong>：相比下拉框，复选框能同时显示所有选项，更直观</li>
                <li><strong>逻辑关联</strong>：操作符和阈值的显示条件与规则类型的分组完全对应</li>
                <li><strong>互斥提示</strong>：通过分组暗示用户某些类型不能同时选择</li>
            </ul>
            
            <h3>用户体验提升：</h3>
            <ul>
                <li>减少了用户的认知负担</li>
                <li>避免了选择错误组合的可能</li>
                <li>界面逻辑更加清晰</li>
                <li>降低了使用门槛</li>
            </ul>
        </div>
    </div>
    
    <script>
        // 简单的交互演示
        document.addEventListener('DOMContentLoaded', function() {
            const priceCheckboxes = document.querySelectorAll('#promo1, #channel1');
            const statusCheckbox = document.querySelector('#status1');
            const conditionSection = document.querySelector('.condition-section');
            
            function updateConditionDisplay() {
                const priceSelected = Array.from(priceCheckboxes).some(cb => cb.checked);
                if (priceSelected) {
                    conditionSection.style.display = 'block';
                } else {
                    conditionSection.style.display = 'none';
                }
            }
            
            priceCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', updateConditionDisplay);
            });
            
            statusCheckbox.addEventListener('change', updateConditionDisplay);
            
            updateConditionDisplay();
        });
    </script>
</body>
</html> 