<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Services\DataCollectionService;
use Illuminate\Support\Facades\Log;

// 启动Laravel应用
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== 测试有促销价格的商品 ===\n\n";

// 测试一些可能有促销价格的商品ID
$testProducts = [
    '804460152151', // 原商品，已知没有促销价
    '123456789',    // 测试商品
    '987654321',    // 测试商品
];

$dataCollectionService = new DataCollectionService();

foreach ($testProducts as $productId) {
    echo "测试商品ID: {$productId}\n";
    
    try {
        // 直接调用API获取数据
        $dataSourceId = 3;
        $monitoringTaskId = 2;
        
        $result = $dataCollectionService->collectData($dataSourceId, $productId, $monitoringTaskId);
        
        if ($result['success']) {
            $standardizedData = $result['data']['standardized_data'] ?? [];
            $skuFields = $standardizedData['sku_fields'] ?? [];
            
            echo "  - 成功获取数据\n";
            echo "  - SKU数量: " . count($skuFields) . "\n";
            
            $hasPromotion = false;
            foreach ($skuFields as $sku) {
                $promotionPrice = $sku['promotion_price'] ?? null;
                if ($promotionPrice !== null && is_numeric($promotionPrice) && $promotionPrice > 0) {
                    $hasPromotion = true;
                    echo "  - 找到促销价格: {$promotionPrice}\n";
                    break;
                }
            }
            
            if (!$hasPromotion) {
                echo "  - 没有找到促销价格\n";
            }
            
        } else {
            echo "  - 获取数据失败: " . ($result['message'] ?? '未知错误') . "\n";
        }
        
    } catch (Exception $e) {
        echo "  - 异常: " . $e->getMessage() . "\n";
    }
    
    echo "\n";
}

echo "=== 测试完成 ===\n";
