<?php

/**
 * 测试渠道价格监测看板API
 * 这个脚本用于验证所有相关API是否正常工作
 */

require_once 'vendor/autoload.php';

use Illuminate\Http\Request;
use App\Http\Controllers\Api\AnalyticsController;
use App\Http\Controllers\Api\MonitoringTaskController;
use App\Http\Controllers\Api\DataSourceController;
use App\Http\Controllers\Api\ProductDataController;

// 测试仪表盘统计API
function testDashboardStats() {
    echo "=== 测试仪表盘统计API ===\n";
    
    try {
        $controller = new AnalyticsController(app(App\Services\AnalyticsService::class));
        $response = $controller->getDashboardStatistics();
        $data = json_decode($response->getContent(), true);
        
        echo "响应状态: " . $response->getStatusCode() . "\n";
        echo "响应数据: " . json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
        
        if ($data['success']) {
            echo "✅ 仪表盘统计API正常\n";
        } else {
            echo "❌ 仪表盘统计API失败: " . $data['message'] . "\n";
        }
    } catch (Exception $e) {
        echo "❌ 仪表盘统计API异常: " . $e->getMessage() . "\n";
    }
    
    echo "\n";
}

// 测试获取所有监控任务API
function testGetAllMonitoringTasks() {
    echo "=== 测试获取所有监控任务API ===\n";
    
    try {
        $controller = new MonitoringTaskController();
        $response = $controller->all();
        $data = json_decode($response->getContent(), true);
        
        echo "响应状态: " . $response->getStatusCode() . "\n";
        echo "响应数据: " . json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
        
        if ($data['success']) {
            echo "✅ 监控任务API正常，任务数量: " . count($data['data']) . "\n";
        } else {
            echo "❌ 监控任务API失败: " . $data['message'] . "\n";
        }
    } catch (Exception $e) {
        echo "❌ 监控任务API异常: " . $e->getMessage() . "\n";
    }
    
    echo "\n";
}

// 测试获取所有数据源API
function testGetAllDataSources() {
    echo "=== 测试获取所有数据源API ===\n";
    
    try {
        $controller = new DataSourceController();
        $response = $controller->all();
        $data = json_decode($response->getContent(), true);
        
        echo "响应状态: " . $response->getStatusCode() . "\n";
        echo "响应数据: " . json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
        
        if ($data['success']) {
            echo "✅ 数据源API正常，数据源数量: " . count($data['data']) . "\n";
        } else {
            echo "❌ 数据源API失败: " . $data['message'] . "\n";
        }
    } catch (Exception $e) {
        echo "❌ 数据源API异常: " . $e->getMessage() . "\n";
    }
    
    echo "\n";
}

// 测试获取产品数据API
function testGetProductData() {
    echo "=== 测试获取产品数据API ===\n";
    
    try {
        $controller = new ProductDataController();
        $request = new Request([
            'page' => 1,
            'per_page' => 10
        ]);
        
        $response = $controller->index($request);
        $data = json_decode($response->getContent(), true);
        
        echo "响应状态: " . $response->getStatusCode() . "\n";
        echo "响应数据结构: \n";
        echo "- success: " . ($data['success'] ? 'true' : 'false') . "\n";
        echo "- message: " . $data['message'] . "\n";
        
        if (isset($data['data']['data'])) {
            echo "- 产品数量: " . count($data['data']['data']) . "\n";
            echo "- 总数: " . ($data['data']['total'] ?? 0) . "\n";
            
            // 显示第一个产品的字段
            if (!empty($data['data']['data'])) {
                $firstProduct = $data['data']['data'][0];
                echo "- 第一个产品的字段: " . implode(', ', array_keys($firstProduct)) . "\n";
                
                // 检查价格字段
                $priceFields = ['price', 'min_hand_price', 'max_hand_price', 'lowest_price', 'highest_price'];
                foreach ($priceFields as $field) {
                    if (isset($firstProduct[$field])) {
                        echo "  - {$field}: " . $firstProduct[$field] . "\n";
                    }
                }
            }
        }
        
        if ($data['success']) {
            echo "✅ 产品数据API正常\n";
        } else {
            echo "❌ 产品数据API失败: " . $data['message'] . "\n";
        }
    } catch (Exception $e) {
        echo "❌ 产品数据API异常: " . $e->getMessage() . "\n";
    }
    
    echo "\n";
}

// 检查数据库中的数据
function checkDatabaseData() {
    echo "=== 检查数据库数据 ===\n";
    
    try {
        // 检查数据源
        $dataSourceCount = App\Models\DataSource::count();
        echo "数据源数量: " . $dataSourceCount . "\n";
        
        // 检查监控任务
        $taskCount = App\Models\MonitoringTask::count();
        echo "监控任务数量: " . $taskCount . "\n";
        
        // 检查产品数据
        $productCount = App\Models\ProductData::count();
        echo "产品数据数量: " . $productCount . "\n";
        
        // 检查字段
        if ($productCount > 0) {
            $sampleProduct = App\Models\ProductData::first();
            echo "示例产品字段: \n";
            echo "- ID: " . $sampleProduct->id . "\n";
            echo "- item_id: " . $sampleProduct->item_id . "\n";
            echo "- title: " . $sampleProduct->title . "\n";
            echo "- price: " . $sampleProduct->price . "\n";
            echo "- min_hand_price: " . $sampleProduct->min_hand_price . "\n";
            echo "- max_hand_price: " . $sampleProduct->max_hand_price . "\n";
            echo "- stock: " . $sampleProduct->stock . "\n";
        }
        
    } catch (Exception $e) {
        echo "❌ 数据库检查异常: " . $e->getMessage() . "\n";
    }
    
    echo "\n";
}

// 主函数
function main() {
    echo "开始测试渠道价格监测看板API...\n\n";
    
    // 初始化Laravel应用
    $app = require_once 'bootstrap/app.php';
    $app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();
    
    // 运行测试
    checkDatabaseData();
    testDashboardStats();
    testGetAllDataSources();
    testGetAllMonitoringTasks();
    testGetProductData();
    
    echo "测试完成。\n";
}

// 如果直接运行此脚本
if (basename(__FILE__) == basename($_SERVER["SCRIPT_NAME"])) {
    main();
} 