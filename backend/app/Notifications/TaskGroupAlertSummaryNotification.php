<?php

namespace App\Notifications;

use App\Models\TaskGroupAlertSummary;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class TaskGroupAlertSummaryNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * 任务分组预警汇总实例
     */
    private TaskGroupAlertSummary $summary;

    /**
     * 创建新的通知实例
     */
    public function __construct(TaskGroupAlertSummary $summary)
    {
        $this->summary = $summary;
    }

    /**
     * 获取通知的投递渠道
     */
    public function via($notifiable): array
    {
        // 任务分组预警汇总主要发送站内信
        return ['database'];
    }

    /**
     * 获取数据库通知的表示形式
     */
    public function toDatabase($notifiable): array
    {
        $taskGroupName = $this->summary->taskGroup->name ?? '未知分组';
        $report = $this->summary->generateSummaryReport();
        
        return [
            'type' => 'task_group_alert_summary',
            'summary_id' => $this->summary->id,
            'task_group_id' => $this->summary->task_group_id,
            'monitoring_task_id' => $this->summary->monitoring_task_id,
            'collection_batch_id' => $this->summary->collection_batch_id,
            'title' => "【任务分组预警汇总】{$taskGroupName}",
            'message' => $this->summary->getFormattedNotificationMessage(),
            'summary_data' => [
                'task_group_name' => $taskGroupName,
                'alert_count' => $this->summary->alert_count,
                'product_count' => $this->summary->product_count,
                'rule_count' => $this->summary->rule_count,
                'rules_summary' => $this->formatRulesSummary($report),
            ],
            'created_at' => $this->summary->created_at->toISOString(),
        ];
    }

    /**
     * 获取邮件通知的表示形式（如果需要）
     */
    public function toMail($notifiable): MailMessage
    {
        $taskGroupName = $this->summary->taskGroup->name ?? '未知分组';
        
        return (new MailMessage)
            ->subject("【任务分组预警汇总】{$taskGroupName}")
            ->greeting("您好！")
            ->line("您的任务分组触发了预警汇总通知。")
            ->line("**汇总详情：**")
            ->line("- 任务分组：{$taskGroupName}")
            ->line("- 预警总数：{$this->summary->alert_count} 个")
            ->line("- 涉及商品：{$this->summary->product_count} 个")
            ->line("- 涉及规则：{$this->summary->rule_count} 个")
            ->line("- 生成时间：{$this->summary->created_at->format('Y-m-d H:i:s')}")
            ->action('查看详情', url("/task-group-alerts/{$this->summary->id}"))
            ->line('请及时查看和处理相关预警。');
    }

    /**
     * 格式化规则汇总信息
     */
    private function formatRulesSummary(array $report): array
    {
        $rulesSummary = [];
        
        foreach ($report as $ruleReport) {
            $rulesSummary[] = [
                'rule_id' => $ruleReport['rule_id'],
                'rule_name' => $ruleReport['rule_name'],
                'rule_type' => $ruleReport['rule_type'],
                'alert_count' => $ruleReport['alert_count'],
                'product_count' => $ruleReport['product_count'],
                'products' => array_map(function ($product) {
                    return [
                        'item_id' => $product['item_id'],
                        'title' => $product['title'],
                        'data_source' => $product['data_source'],
                        'alert_summary' => $product['alert_summary'],
                    ];
                }, $ruleReport['products']),
            ];
        }
        
        return $rulesSummary;
    }

    /**
     * 获取通知的数组表示形式
     */
    public function toArray($notifiable): array
    {
        return $this->toDatabase($notifiable);
    }
}
