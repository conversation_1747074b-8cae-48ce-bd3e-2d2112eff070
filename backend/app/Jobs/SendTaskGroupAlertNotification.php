<?php

namespace App\Jobs;

use App\Models\TaskGroupAlertSummary;
use App\Models\User;
use App\Notifications\TaskGroupAlertSummaryNotification;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;

class SendTaskGroupAlertNotification implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * 任务分组预警汇总ID
     */
    private int $summaryId;

    /**
     * 任务尝试次数
     */
    public int $tries = 3;

    /**
     * 任务超时时间（秒）
     */
    public int $timeout = 120;

    /**
     * 创建新的任务实例
     */
    public function __construct(int $summaryId)
    {
        $this->summaryId = $summaryId;
    }

    /**
     * 执行任务
     */
    public function handle(): void
    {
        Log::info('开始处理任务分组预警汇总通知发送任务', [
            'summary_id' => $this->summaryId,
        ]);

        try {
            // 获取预警汇总记录
            $summary = TaskGroupAlertSummary::with(['taskGroup', 'monitoringTask'])->find($this->summaryId);

            if (!$summary) {
                Log::warning('预警汇总记录不存在，跳过通知发送', [
                    'summary_id' => $this->summaryId,
                ]);
                return;
            }

            // 检查状态
            if ($summary->status !== TaskGroupAlertSummary::STATUS_READY) {
                Log::warning('预警汇总状态不正确，跳过通知发送', [
                    'summary_id' => $this->summaryId,
                    'status' => $summary->status,
                ]);
                return;
            }

            // 检查是否有预警
            if (!$summary->hasAlerts()) {
                Log::info('预警汇总没有预警，跳过通知发送', [
                    'summary_id' => $this->summaryId,
                ]);
                $summary->markSent();
                return;
            }

            // 获取通知接收者
            $recipients = $this->getNotificationRecipients($summary);
            
            if (empty($recipients)) {
                Log::warning('没有找到通知接收者', [
                    'summary_id' => $this->summaryId,
                ]);
                $summary->markSent(); // 标记为已发送，避免重复处理
                return;
            }

            // 创建通知实例
            $notification = new TaskGroupAlertSummaryNotification($summary);

            // 发送通知
            $sentCount = 0;
            $errors = [];

            foreach ($recipients as $recipient) {
                try {
                    $recipient->notify($notification);
                    $sentCount++;
                    
                    Log::debug('任务分组预警汇总通知发送成功', [
                        'summary_id' => $this->summaryId,
                        'recipient_id' => $recipient->id,
                        'recipient_email' => $recipient->email,
                    ]);
                } catch (\Exception $e) {
                    $errors[] = [
                        'recipient_id' => $recipient->id,
                        'error' => $e->getMessage(),
                    ];
                    
                    Log::error('发送任务分组预警汇总通知失败', [
                        'summary_id' => $this->summaryId,
                        'recipient_id' => $recipient->id,
                        'error' => $e->getMessage(),
                    ]);
                }
            }

            // 标记为已发送
            $summary->markSent();

            Log::info('任务分组预警汇总通知发送任务完成', [
                'summary_id' => $this->summaryId,
                'recipients_count' => count($recipients),
                'sent_count' => $sentCount,
                'failed_count' => count($errors),
                'alert_count' => $summary->alert_count,
                'product_count' => $summary->product_count,
            ]);

        } catch (\Exception $e) {
            Log::error('任务分组预警汇总通知发送任务执行失败', [
                'summary_id' => $this->summaryId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            // 标记为失败
            if (isset($summary)) {
                $summary->markFailed();
            }

            // 重新抛出异常以触发重试机制
            throw $e;
        }
    }

    /**
     * 获取通知接收者列表
     *
     * @param TaskGroupAlertSummary $summary
     * @return array
     */
    private function getNotificationRecipients(TaskGroupAlertSummary $summary): array
    {
        $recipients = [];

        // 1. 从任务分组获取接收者（如果有配置的话）
        $taskGroup = $summary->taskGroup;
        if ($taskGroup && $taskGroup->user) {
            $recipients[] = $taskGroup->user;
        }

        // 2. 从监控任务获取接收者
        $monitoringTask = $summary->monitoringTask;
        if ($monitoringTask && $monitoringTask->user) {
            $recipients[] = $monitoringTask->user;
        }

        // 3. 从相关的预警规则获取接收者
        $alerts = $summary->alerts()->with('alertRule')->get();
        foreach ($alerts as $alert) {
            if ($alert->alertRule && !empty($alert->alertRule->recipients)) {
                $ruleRecipients = $alert->alertRule->recipients ?? [];
                
                foreach ($ruleRecipients as $recipient) {
                    if (is_numeric($recipient)) {
                        // 用户ID
                        $user = User::find($recipient);
                        if ($user) {
                            $recipients[] = $user;
                        }
                    } elseif (filter_var($recipient, FILTER_VALIDATE_EMAIL)) {
                        // 邮箱地址，查找对应用户
                        $user = User::where('email', $recipient)->first();
                        if ($user) {
                            $recipients[] = $user;
                        }
                    }
                }
            }
        }

        // 4. 去重（基于用户ID）
        $uniqueRecipients = [];
        $userIds = [];
        
        foreach ($recipients as $recipient) {
            if (!in_array($recipient->id, $userIds)) {
                $uniqueRecipients[] = $recipient;
                $userIds[] = $recipient->id;
            }
        }

        return $uniqueRecipients;
    }

    /**
     * 任务失败时的处理
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('任务分组预警汇总通知发送任务最终失败', [
            'summary_id' => $this->summaryId,
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts(),
        ]);

        // 标记汇总为失败状态
        $summary = TaskGroupAlertSummary::find($this->summaryId);
        if ($summary) {
            $summary->markFailed();
        }
    }

    /**
     * 获取任务的唯一标识符
     */
    public function uniqueId(): string
    {
        return "task_group_alert_summary_notification_{$this->summaryId}";
    }

    /**
     * 获取任务的标签
     */
    public function tags(): array
    {
        return ['task_group_alerts', 'notifications', "summary:{$this->summaryId}"];
    }
}
