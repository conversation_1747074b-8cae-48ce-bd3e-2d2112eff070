<?php

namespace App\Jobs;

use App\Models\DataSource;
use App\Models\MonitoringTask;
use App\Services\DataCollectionService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ProcessDataSourceTask implements ShouldQueue
{
    use Queueable, InteractsWithQueue, SerializesModels;

    /**
     * 任务最大尝试次数
     */
    public $tries = 3;

    /**
     * 任务超时时间（秒）
     */
    public $timeout = 300;

    /**
     * 数据源ID
     */
    public int $dataSourceId;

    /**
     * 监控任务ID
     */
    public ?int $monitoringTaskId;

    /**
     * 目标产品信息
     */
    public array $targetProducts;

    /**
     * 任务参数
     */
    public array $taskParams;

    /**
     * Create a new job instance.
     */
    public function __construct(
        int $dataSourceId,
        array $targetProducts = [],
        array $taskParams = [],
        ?int $monitoringTaskId = null
    ) {
        $this->dataSourceId = $dataSourceId;
        $this->targetProducts = $targetProducts;
        $this->taskParams = $taskParams;
        $this->monitoringTaskId = $monitoringTaskId;
    }

    /**
     * Execute the job.
     */
    public function handle(DataCollectionService $dataCollectionService): void
    {
        Log::info('开始处理数据源任务', [
            'data_source_id' => $this->dataSourceId,
            'monitoring_task_id' => $this->monitoringTaskId,
            'target_products' => $this->targetProducts,
        ]);

        // 获取数据源 - 保留此逻辑以进行早期检查和统计
        $dataSource = DataSource::find($this->dataSourceId);
        if (!$dataSource) {
            Log::warning('数据源任务中止：数据源不存在', ['data_source_id' => $this->dataSourceId]);
            return; // 中止任务
        }

        if (!$dataSource->isAvailable()) {
            Log::warning('数据源任务中止：数据源不可用', ['data_source_id' => $this->dataSourceId, 'name' => $dataSource->name]);
            return; // 中止任务
        }
        
        $monitoringTask = $this->monitoringTaskId ? MonitoringTask::find($this->monitoringTaskId) : null;

        try {
            // 处理每个目标产品
            $results = [];
            foreach ($this->targetProducts as $itemId) {
                // 直接调用 DataCollectionService，传递监控任务ID
                $result = $dataCollectionService->collectAndStandardize(
                    $this->dataSourceId,
                    $itemId,
                    $this->taskParams,
                    $this->monitoringTaskId // 传递监控任务ID
                );
                $results[] = $result;
            }

            // 更新数据源使用统计
            $dataSource->updateUsageStats(true);

            // 更新监控任务状态（如果存在）
            if ($monitoringTask) {
                $monitoringTask->increment('run_count');
                $monitoringTask->increment('success_count');
                $monitoringTask->update([
                    'last_run_at' => now(),
                    'next_run_at' => $monitoringTask->calculateNextRunTime(),
                ]);
            }

            Log::info('数据源任务处理完成', [
                'data_source_id' => $this->dataSourceId,
                'monitoring_task_id' => $this->monitoringTaskId,
                'results_count' => count($results),
            ]);

        } catch (\Exception $e) {
            // 更新数据源使用统计（失败）
            $dataSource->updateUsageStats(false);

            // 更新监控任务状态（如果存在）
            if ($monitoringTask) {
                $monitoringTask->increment('run_count');
                $monitoringTask->increment('failed_count');
                $monitoringTask->update([
                    'last_run_at' => now(),
                    'last_error' => $e->getMessage(),
                ]);
            }

            Log::error('数据源任务处理失败', [
                'data_source_id' => $this->dataSourceId,
                'error' => $e->getMessage(),
            ]);

            // 重新抛出异常以让队列系统处理失败任务
            throw $e; 
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        // 当任务最终失败时（例如，在所有重试后）记录日志
        Log::critical('数据源任务最终失败', [
            'data_source_id' => $this->dataSourceId,
            'monitoring_task_id' => $this->monitoringTaskId,
            'exception' => $exception->getMessage(),
        ]);
    }
}
