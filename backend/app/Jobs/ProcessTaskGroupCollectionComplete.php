<?php

namespace App\Jobs;

use App\Services\AlertService;
use App\Services\TaskGroupAlertSummaryService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ProcessTaskGroupCollectionComplete implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * 监控任务ID
     */
    private int $monitoringTaskId;

    /**
     * 采集批次ID
     */
    private string $collectionBatchId;

    /**
     * 任务执行超时时间（秒）
     */
    public int $timeout = 300;

    /**
     * 最大重试次数
     */
    public int $tries = 2;

    /**
     * 创建新的任务实例
     */
    public function __construct(int $monitoringTaskId, string $collectionBatchId)
    {
        $this->monitoringTaskId = $monitoringTaskId;
        $this->collectionBatchId = $collectionBatchId;
    }

    /**
     * 执行任务
     */
    public function handle(AlertService $alertService, TaskGroupAlertSummaryService $summaryService): void
    {
        Log::info('开始处理任务分组采集完成任务', [
            'monitoring_task_id' => $this->monitoringTaskId,
            'collection_batch_id' => $this->collectionBatchId,
        ]);

        try {
            // 检查监控任务是否完成采集
            if (!$summaryService->isMonitoringTaskCollectionComplete($this->monitoringTaskId, $this->collectionBatchId)) {
                Log::info('监控任务尚未完成采集，跳过汇总处理', [
                    'monitoring_task_id' => $this->monitoringTaskId,
                    'collection_batch_id' => $this->collectionBatchId,
                ]);
                return;
            }

            // 检查是否所有相关的预警检查都已完成
            $this->waitForAlertCheckingComplete();

            // 完成任务分组采集并触发汇总通知
            $alertService->completeTaskGroupCollection($this->monitoringTaskId, $this->collectionBatchId);

            Log::info('任务分组采集完成任务处理成功', [
                'monitoring_task_id' => $this->monitoringTaskId,
                'collection_batch_id' => $this->collectionBatchId,
            ]);

        } catch (\Exception $e) {
            Log::error('任务分组采集完成任务处理失败', [
                'monitoring_task_id' => $this->monitoringTaskId,
                'collection_batch_id' => $this->collectionBatchId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            // 重新抛出异常以触发重试机制
            throw $e;
        }
    }

    /**
     * 等待预警检查完成
     * 
     * 由于预警检查是异步进行的，我们需要等待一段时间确保所有预警检查都已完成
     */
    private function waitForAlertCheckingComplete(): void
    {
        // 等待30秒，确保所有预警检查任务都已完成
        // 在实际生产环境中，可以通过检查队列状态或数据库记录来确定是否完成
        Log::info('等待预警检查任务完成', [
            'monitoring_task_id' => $this->monitoringTaskId,
            'collection_batch_id' => $this->collectionBatchId,
            'wait_seconds' => 30,
        ]);

        sleep(30);
    }

    /**
     * 任务失败时的处理
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('任务分组采集完成任务最终失败', [
            'monitoring_task_id' => $this->monitoringTaskId,
            'collection_batch_id' => $this->collectionBatchId,
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts(),
        ]);
    }

    /**
     * 获取任务的唯一标识符
     */
    public function uniqueId(): string
    {
        return "task_group_collection_complete_{$this->monitoringTaskId}_{$this->collectionBatchId}";
    }

    /**
     * 获取任务的标签
     */
    public function tags(): array
    {
        return [
            'task_group_collection',
            'monitoring_task:' . $this->monitoringTaskId,
            'batch:' . $this->collectionBatchId,
        ];
    }
}
