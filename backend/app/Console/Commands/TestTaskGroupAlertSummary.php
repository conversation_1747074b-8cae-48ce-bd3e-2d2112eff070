<?php

namespace App\Console\Commands;

use App\Models\Alert;
use App\Models\MonitoringTask;
use App\Models\TaskGroup;
use App\Models\ProductData;
use App\Models\ProductSku;
use App\Models\AlertRule;
use App\Services\TaskGroupAlertSummaryService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class TestTaskGroupAlertSummary extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:task-group-alert-summary {--task-group-id= : 指定任务分组ID} {--create-test-data : 创建测试数据}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '测试任务分组预警汇总功能';

    /**
     * Execute the console command.
     */
    public function handle(TaskGroupAlertSummaryService $summaryService): int
    {
        $this->info('开始测试任务分组预警汇总功能...');

        try {
            if ($this->option('create-test-data')) {
                $this->createTestData();
            }

            $taskGroupId = $this->option('task-group-id');
            if (!$taskGroupId) {
                // 获取第一个任务分组进行测试
                $taskGroup = TaskGroup::with('monitoringTasks')->first();
                if (!$taskGroup) {
                    $this->error('没有找到任务分组，请先创建任务分组或使用 --create-test-data 选项');
                    return 1;
                }
                $taskGroupId = $taskGroup->id;
            }

            $this->info("使用任务分组ID: {$taskGroupId}");

            // 测试预警汇总功能
            $this->testAlertSummary($summaryService, $taskGroupId);

            $this->info('测试完成！');
            return 0;

        } catch (\Exception $e) {
            $this->error('测试失败: ' . $e->getMessage());
            Log::error('任务分组预警汇总测试失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            return 1;
        }
    }

    /**
     * 创建测试数据
     */
    private function createTestData(): void
    {
        $this->info('创建测试数据...');

        // 创建测试任务分组
        $taskGroup = TaskGroup::firstOrCreate([
            'name' => '测试任务分组',
        ], [
            'description' => '用于测试预警汇总功能的任务分组',
            'user_id' => 1,
            'status' => 1,
        ]);

        $this->info("创建/找到任务分组: {$taskGroup->name} (ID: {$taskGroup->id})");

        // 创建测试监控任务
        $monitoringTask = MonitoringTask::firstOrCreate([
            'name' => '测试监控任务',
            'task_group_id' => $taskGroup->id,
        ], [
            'description' => '用于测试预警汇总功能的监控任务',
            'user_id' => 1,
            'data_source_id' => 1,
            'target_products' => [
                ['product_id' => 'TEST001', 'official_guide_price' => 100.00],
                ['product_id' => 'TEST002', 'official_guide_price' => 200.00],
            ],
            'status' => 'running',
        ]);

        $this->info("创建/找到监控任务: {$monitoringTask->name} (ID: {$monitoringTask->id})");

        // 创建测试预警规则
        $alertRule1 = AlertRule::firstOrCreate([
            'name' => '促销价格偏差预警',
            'rule_type' => 'promotion_price_deviation',
        ], [
            'description' => '促销价格偏差超过阈值时触发预警',
            'conditions' => ['deviation_percentage' => 10],
            'status' => 1,
            'user_id' => 1,
        ]);

        $alertRule2 = AlertRule::firstOrCreate([
            'name' => '渠道价格偏差预警',
            'rule_type' => 'channel_price_deviation',
        ], [
            'description' => '渠道价格偏差超过阈值时触发预警',
            'conditions' => ['deviation_percentage' => 15],
            'status' => 1,
            'user_id' => 1,
        ]);

        $this->info("创建/找到预警规则: {$alertRule1->name}, {$alertRule2->name}");

        // 创建测试商品数据
        $productData1 = ProductData::firstOrCreate([
            'item_id' => 'TEST001',
            'monitoring_task_id' => $monitoringTask->id,
        ], [
            'title' => '测试商品1',
            'price' => 95.00,
            'original_price' => 100.00,
            'data_source_id' => 1,
            'raw_data' => ['test' => true],
            'last_collected_at' => now(),
        ]);

        $productData2 = ProductData::firstOrCreate([
            'item_id' => 'TEST002',
            'monitoring_task_id' => $monitoringTask->id,
        ], [
            'title' => '测试商品2',
            'price' => 180.00,
            'original_price' => 200.00,
            'data_source_id' => 1,
            'raw_data' => ['test' => true],
            'last_collected_at' => now(),
        ]);

        $this->info("创建/找到商品数据: {$productData1->title}, {$productData2->title}");

        // 创建测试SKU数据
        ProductSku::firstOrCreate([
            'product_data_id' => $productData1->id,
            'sku_id' => 'SKU001',
        ], [
            'sku_name' => '测试SKU1',
            'price' => 95.00,
            'original_price' => 100.00,
            'stock' => 100,
        ]);

        ProductSku::firstOrCreate([
            'product_data_id' => $productData2->id,
            'sku_id' => 'SKU002',
        ], [
            'sku_name' => '测试SKU2',
            'price' => 180.00,
            'original_price' => 200.00,
            'stock' => 50,
        ]);

        $this->info('测试数据创建完成！');
    }

    /**
     * 测试预警汇总功能
     */
    private function testAlertSummary(TaskGroupAlertSummaryService $summaryService, int $taskGroupId): void
    {
        $this->info('开始测试预警汇总功能...');

        $taskGroup = TaskGroup::with('monitoringTasks')->find($taskGroupId);
        if (!$taskGroup) {
            throw new \Exception("任务分组不存在: {$taskGroupId}");
        }

        $monitoringTask = $taskGroup->monitoringTasks->first();
        if (!$monitoringTask) {
            throw new \Exception("任务分组下没有监控任务: {$taskGroupId}");
        }

        // 生成测试采集批次ID
        $collectionBatchId = $summaryService->generateCollectionBatchId();
        $this->info("生成采集批次ID: {$collectionBatchId}");

        // 创建测试预警记录
        $this->createTestAlerts($monitoringTask, $collectionBatchId);

        // 测试汇总功能
        $this->info('完成汇总并发送通知...');
        $summaryService->finalizeSummary($monitoringTask->id, $collectionBatchId);

        $this->info('预警汇总功能测试完成！');
    }

    /**
     * 创建测试预警记录
     */
    private function createTestAlerts(MonitoringTask $monitoringTask, string $collectionBatchId): void
    {
        $this->info('创建测试预警记录...');

        $productData = ProductData::where('monitoring_task_id', $monitoringTask->id)->get();

        foreach ($productData as $product) {
            // 创建促销价格偏差预警
            $alert1 = Alert::create([
                'monitoring_task_id' => $monitoringTask->id,
                'product_data_id' => $product->id,
                'alert_rule_id' => 1,
                'rule_type' => 'promotion_price_deviation',
                'title' => '促销价格偏差预警',
                'message' => "商品 {$product->title} 促销价格偏差超过阈值",
                'level' => 'warning',
                'status' => 'active',
                'alert_data' => [
                    'product_id' => $product->item_id,
                    'current_price' => $product->price,
                    'expected_price' => $product->original_price,
                    'deviation_percentage' => 12.5,
                ],
            ]);

            // 创建渠道价格偏差预警
            $alert2 = Alert::create([
                'monitoring_task_id' => $monitoringTask->id,
                'product_data_id' => $product->id,
                'alert_rule_id' => 2,
                'rule_type' => 'channel_price_deviation',
                'title' => '渠道价格偏差预警',
                'message' => "商品 {$product->title} 渠道价格偏差超过阈值",
                'level' => 'error',
                'status' => 'active',
                'alert_data' => [
                    'product_id' => $product->item_id,
                    'current_price' => $product->price,
                    'channel_price' => $product->price * 0.9,
                    'deviation_percentage' => 18.0,
                ],
            ]);

            $this->info("为商品 {$product->title} 创建了 2 个测试预警");
        }
    }
}
