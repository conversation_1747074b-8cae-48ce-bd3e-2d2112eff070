<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\DataCollectionService;
use App\Models\ProductDataHistory;
use App\Models\ProductSkuHistory;

class TestHistorySave extends Command
{
    protected $signature = 'test:history-save';
    protected $description = '测试历史数据保存功能';

    public function handle()
    {
        $this->info('开始测试历史数据保存功能...');

        // 检查当前历史记录数
        $historyCountBefore = ProductDataHistory::count();
        $skuHistoryCountBefore = ProductSkuHistory::count();

        $this->info("测试前：");
        $this->info("ProductDataHistory 记录数: {$historyCountBefore}");
        $this->info("ProductSkuHistory 记录数: {$skuHistoryCountBefore}");
        $this->newLine();

        try {
            $service = new DataCollectionService();
            $this->info('开始采集数据...');
            
            // 采集数据 - 使用已存在的数据源ID=3和商品ID
            $result = $service->collectAndStandardize(3, '533344308218', [], 2);
            
            $this->info('数据采集完成');
            $this->newLine();
            
            // 检查采集后的历史记录数
            $historyCountAfter = ProductDataHistory::count();
            $skuHistoryCountAfter = ProductSkuHistory::count();
            
            $this->info("测试后：");
            $this->info("ProductDataHistory 记录数: {$historyCountAfter}");
            $this->info("ProductSkuHistory 记录数: {$skuHistoryCountAfter}");
            $this->newLine();
            
            if ($historyCountAfter > $historyCountBefore) {
                $this->info("✅ 历史数据保存成功！新增了 " . ($historyCountAfter - $historyCountBefore) . " 条产品历史记录");
            } else {
                $this->error("❌ 历史数据保存失败，没有新增记录");
            }
            
            if ($skuHistoryCountAfter > $skuHistoryCountBefore) {
                $this->info("✅ SKU历史数据保存成功！新增了 " . ($skuHistoryCountAfter - $skuHistoryCountBefore) . " 条SKU历史记录");
            } else {
                $this->error("❌ SKU历史数据保存失败，没有新增记录");
            }
            
            // 显示最新的历史记录
            $latestHistory = ProductDataHistory::latest('collected_at')->first();
            if ($latestHistory) {
                $this->newLine();
                $this->info("最新历史记录：");
                $this->info("ID: {$latestHistory->id}");
                $this->info("商品ID: {$latestHistory->item_id}");
                $this->info("采集时间: {$latestHistory->collected_at}");
                $this->info("标题: " . substr($latestHistory->title, 0, 50) . "...");
            }
            
        } catch (\Exception $e) {
            $this->error("错误: " . $e->getMessage());
            $this->error("详细信息: " . $e->getTraceAsString());
        }

        $this->newLine();
        $this->info('测试完成。');
    }
} 