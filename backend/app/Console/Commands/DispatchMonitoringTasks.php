<?php

namespace App\Console\Commands;

use App\Jobs\ProcessDataSourceTask;
use App\Models\MonitoringTask;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use Cron\CronExpression;

class DispatchMonitoringTasks extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'schedule:dispatch-tasks
                            {--dry-run : 只显示将要执行的任务，不实际执行}
                            {--force : 强制执行所有任务，忽略状态检查}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '检查并调度到期的监控任务到消息队列';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $dryRun = $this->option('dry-run');
        $force = $this->option('force');
        
        $this->info('开始检查到期的监控任务...');
        
        try {
            // 获取到期的监控任务
            $dueTasks = $this->getDueTasks($force);
            
            if ($dueTasks->isEmpty()) {
                $this->info('没有找到到期的监控任务。');
                return self::SUCCESS;
            }
            
            $this->info("找到 {$dueTasks->count()} 个到期的监控任务。");
            
            $dispatched = 0;
            $failed = 0;
            
            foreach ($dueTasks as $task) {
                try {
                    if ($dryRun) {
                        $this->line("  [DRY RUN] 任务: {$task->name} (ID: {$task->id})");
                        $this->line("    - 下次执行时间: {$task->next_run_at}");
                        $this->line("    - 数据源: {$task->dataSource->name}");
                        $this->line("    - 目标产品数: " . count($task->target_products));
                        continue;
                    }
                    
                    $this->dispatchTask($task);
                    $dispatched++;
                    
                    $this->line("  ✓ 已调度任务: {$task->name} (ID: {$task->id})");
                    
                } catch (\Exception $e) {
                    $failed++;
                    $this->error("  ✗ 调度任务失败: {$task->name} (ID: {$task->id}) - {$e->getMessage()}");
                    
                    Log::error('调度监控任务失败', [
                        'task_id' => $task->id,
                        'task_name' => $task->name,
                        'error' => $e->getMessage(),
                    ]);
                }
            }
            
            if (!$dryRun) {
                $this->info("调度完成！成功: {$dispatched}, 失败: {$failed}");
            }
            
            return self::SUCCESS;
            
        } catch (\Exception $e) {
            $this->error("调度过程中发生错误: {$e->getMessage()}");
            Log::error('监控任务调度器错误', ['error' => $e->getMessage()]);
            return self::FAILURE;
        }
    }
    
    /**
     * 获取到期的监控任务
     */
    private function getDueTasks(bool $force = false): \Illuminate\Database\Eloquent\Collection
    {
        $query = MonitoringTask::with(['dataSource', 'user'])
            ->where('auto_start', true);
        
        if (!$force) {
            $query->where('status', 'running')
                  ->where(function ($q) {
                      $q->whereNull('next_run_at')
                        ->orWhere('next_run_at', '<=', now());
                  });
        }
        
        return $query->get();
    }
    
    /**
     * 调度单个监控任务
     */
    private function dispatchTask(MonitoringTask $task): void
    {
        // 检查数据源是否可用
        if (!$task->dataSource->isAvailable()) {
            throw new \Exception("数据源不可用: {$task->dataSource->name}");
        }
        
        // 检查目标产品
        if (empty($task->target_products)) {
            throw new \Exception("任务没有配置目标产品");
        }
        
        // 更新任务状态
        $task->update([
            'status' => 'running',
            'last_run_at' => now(),
            'next_run_at' => $this->calculateNextRunTime($task),
        ]);
        
        // 调度任务到队列
        ProcessDataSourceTask::dispatch(
            $task->data_source_id,
            $task->target_products,
            $this->getTaskParams($task),
            $task->id
        )->onQueue('monitoring');
        
        Log::info('监控任务已调度到队列', [
            'task_id' => $task->id,
            'task_name' => $task->name,
            'data_source_id' => $task->data_source_id,
            'next_run_at' => $task->next_run_at,
        ]);
    }
    
    /**
     * 计算下次执行时间
     */
    private function calculateNextRunTime(MonitoringTask $task): ?Carbon
    {
        if ($task->frequency_type === 'cron' && $task->cron_expression) {
            try {
                $cron = new CronExpression($task->cron_expression);
                return Carbon::instance($cron->getNextRunDate());
            } catch (\Exception $e) {
                Log::warning('Cron表达式解析失败', [
                    'task_id' => $task->id,
                    'cron_expression' => $task->cron_expression,
                    'error' => $e->getMessage(),
                ]);
                // 如果Cron表达式解析失败，回退到间隔模式
            }
        }
        
        // 间隔模式或Cron解析失败时的回退
        if ($task->frequency_type === 'interval' && $task->frequency_value > 0) {
            return now()->addMinutes($task->frequency_value);
        }
        
        // 默认30分钟后再次执行
        return now()->addMinutes(30);
    }
    
    /**
     * 获取任务参数
     */
    private function getTaskParams(MonitoringTask $task): array
    {
        return [
            'monitor_fields' => $task->monitor_fields ?? [],
            'task_id' => $task->id,
            'user_id' => $task->user_id,
            'task_group_id' => $task->task_group_id,
        ];
    }
} 