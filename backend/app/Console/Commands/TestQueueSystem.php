<?php

namespace App\Console\Commands;

use App\Models\DataSource;
use App\Services\QueueProducerService;
use Illuminate\Console\Command;

class TestQueueSystem extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'queue:test-system
                            {--count=1 : 要发布的测试任务数量}
                            {--queue=default : 测试队列名称}
                            {--delay=0 : 任务延迟秒数}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '测试队列系统功能';

    /**
     * 队列生产者服务
     */
    private QueueProducerService $queueProducer;

    public function __construct(QueueProducerService $queueProducer)
    {
        parent::__construct();
        $this->queueProducer = $queueProducer;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🧪 开始测试队列系统...');
        $this->newLine();

        // 1. 检查队列连接
        $this->testQueueConnection();

        // 2. 创建测试数据源（如果不存在）
        $dataSource = $this->ensureTestDataSource();

        // 3. 发布测试任务
        $this->publishTestTasks($dataSource);

        // 4. 显示队列状态
        $this->showQueueStatus();

        // 5. 提供后续操作建议
        $this->showNextSteps();

        return 0;
    }

    /**
     * 测试队列连接
     */
    private function testQueueConnection(): void
    {
        $this->info('📡 测试队列连接...');

        try {
            $status = $this->queueProducer->getQueueStatus($this->option('queue'));
            
            if ($status['status'] === 'error') {
                $this->error('❌ 队列连接失败: ' . $status['error']);
                return;
            }

            $this->info('✅ 队列连接正常');
            $this->line('  连接类型: ' . $status['connection']);
            $this->line('  队列名称: ' . $status['queue']);
            $this->line('  待处理任务: ' . $status['pending_jobs']);
            $this->line('  失败任务: ' . $status['failed_jobs']);

        } catch (\Exception $e) {
            $this->error('❌ 队列连接测试失败: ' . $e->getMessage());
        }

        $this->newLine();
    }

    /**
     * 确保测试数据源存在
     */
    private function ensureTestDataSource(): DataSource
    {
        $this->info('🔧 准备测试数据源...');

        // 查找现有的测试数据源
        $dataSource = DataSource::where('name', '测试数据源')->first();

        if (!$dataSource) {
            // 创建测试数据源
            $dataSource = new DataSource();
            $dataSource->name = '测试数据源';
            $dataSource->type = 'api';
            $dataSource->description = '用于队列系统测试的数据源';
            $dataSource->api_base_url = 'https://jsonplaceholder.typicode.com';
            $dataSource->owner_id = 1; // 假设管理员用户存在
            $dataSource->api_config = json_encode([
                'timeout' => 30,
                'retry_count' => 3,
            ]);
            $dataSource->status = 1;
            $dataSource->save();

            $this->info('✅ 创建测试数据源: ' . $dataSource->name . ' (ID: ' . $dataSource->id . ')');
        } else {
            $this->info('✅ 使用现有测试数据源: ' . $dataSource->name . ' (ID: ' . $dataSource->id . ')');
        }

        $this->newLine();
        return $dataSource;
    }

    /**
     * 发布测试任务
     */
    private function publishTestTasks(DataSource $dataSource): void
    {
        $count = (int) $this->option('count');
        $queue = $this->option('queue');
        $delay = (int) $this->option('delay');

        $this->info("📤 发布 {$count} 个测试任务到队列 '{$queue}'...");

        $jobIds = [];
        $progressBar = $this->output->createProgressBar($count);
        $progressBar->start();

        for ($i = 1; $i <= $count; $i++) {
            try {
                $targetProducts = [
                    [
                        'id' => $i,
                        'type' => 'post',
                        'name' => "测试产品 {$i}",
                    ]
                ];

                $taskParams = [
                    'test_mode' => true,
                    'task_number' => $i,
                    'created_at' => now()->toISOString(),
                ];

                $jobId = $this->queueProducer->publishDataSourceTask(
                    $dataSource->id,
                    $targetProducts,
                    $taskParams,
                    null,
                    $queue,
                    $delay
                );

                $jobIds[] = $jobId;
                $progressBar->advance();

            } catch (\Exception $e) {
                $this->newLine();
                $this->error("❌ 发布任务 {$i} 失败: " . $e->getMessage());
                $progressBar->advance();
            }
        }

        $progressBar->finish();
        $this->newLine();

        $successCount = count(array_filter($jobIds));
        $this->info("✅ 成功发布 {$successCount}/{$count} 个任务");

        if ($successCount > 0) {
            $this->line('任务ID: ' . implode(', ', array_filter($jobIds)));
        }

        $this->newLine();
    }

    /**
     * 显示队列状态
     */
    private function showQueueStatus(): void
    {
        $this->info('📊 当前队列状态:');

        try {
            $status = $this->queueProducer->getQueueStatus($this->option('queue'));
            
            $this->table(
                ['属性', '值'],
                [
                    ['连接类型', $status['connection']],
                    ['队列名称', $status['queue']],
                    ['状态', $status['status']],
                    ['待处理任务', $status['pending_jobs']],
                    ['失败任务', $status['failed_jobs']],
                ]
            );

        } catch (\Exception $e) {
            $this->error('❌ 获取队列状态失败: ' . $e->getMessage());
        }

        $this->newLine();
    }

    /**
     * 显示后续操作建议
     */
    private function showNextSteps(): void
    {
        $this->info('🚀 后续操作建议:');
        $this->line('');
        $this->line('1. 启动队列消费者处理任务:');
        $this->line('   php artisan queue:consume-tasks');
        $this->line('');
        $this->line('2. 在另一个终端窗口监控队列状态:');
        $this->line('   php artisan queue:monitor');
        $this->line('');
        $this->line('3. 查看任务处理日志:');
        $this->line('   tail -f storage/logs/laravel.log');
        $this->line('');
        $this->line('4. 清理测试数据:');
        $this->line('   php artisan queue:flush');
        $this->line('');
    }
}
