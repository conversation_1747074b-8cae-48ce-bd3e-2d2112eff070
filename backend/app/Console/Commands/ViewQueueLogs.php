<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use Carbon\Carbon;

class ViewQueueLogs extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'queue:logs 
                            {--lines=50 : Number of lines to show}
                            {--follow : Follow log file in real time}
                            {--filter= : Filter logs by keyword}
                            {--level= : Filter by log level (error, info, debug)}
                            {--since= : Show logs since date (Y-m-d H:i:s)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '查看队列执行日志';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $logFile = storage_path('logs/laravel.log');
        
        if (!File::exists($logFile)) {
            $this->error('日志文件不存在: ' . $logFile);
            return 1;
        }

        $lines = $this->option('lines');
        $follow = $this->option('follow');
        $filter = $this->option('filter');
        $level = $this->option('level');
        $since = $this->option('since');

        if ($follow) {
            $this->followLogs($logFile, $filter, $level);
        } else {
            $this->showLogs($logFile, $lines, $filter, $level, $since);
        }

        return 0;
    }

    /**
     * 显示日志内容
     */
    private function showLogs($logFile, $lines, $filter = null, $level = null, $since = null)
    {
        $this->info("📋 查看队列日志 (最近 {$lines} 行)");
        $this->line('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');

        // 读取日志内容
        $content = File::get($logFile);
        $logLines = explode("\n", $content);
        $logLines = array_filter($logLines); // 移除空行
        
        // 获取最后N行
        $recentLines = array_slice($logLines, -$lines);
        
        $filteredLogs = [];
        $currentEntry = '';
        
        foreach ($recentLines as $line) {
            // 检查是否是新的日志条目开始
            if (preg_match('/^\[\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\]/', $line)) {
                // 处理上一个条目
                if ($currentEntry && $this->matchesFilters($currentEntry, $filter, $level, $since)) {
                    $filteredLogs[] = $currentEntry;
                }
                $currentEntry = $line;
            } else {
                // 续行
                $currentEntry .= "\n" . $line;
            }
        }
        
        // 处理最后一个条目
        if ($currentEntry && $this->matchesFilters($currentEntry, $filter, $level, $since)) {
            $filteredLogs[] = $currentEntry;
        }

        if (empty($filteredLogs)) {
            $this->warn('未找到匹配的日志条目');
            return;
        }

        $this->info("找到 " . count($filteredLogs) . " 条匹配的日志:");
        $this->line('');

        foreach ($filteredLogs as $entry) {
            $this->displayLogEntry($entry);
        }
    }

    /**
     * 实时跟踪日志
     */
    private function followLogs($logFile, $filter = null, $level = null)
    {
        $this->info('📡 实时监控队列日志 (按 Ctrl+C 退出)');
        $this->line('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');

        $lastSize = filesize($logFile);
        
        while (true) {
            clearstatcache();
            $currentSize = filesize($logFile);
            
            if ($currentSize > $lastSize) {
                $handle = fopen($logFile, 'r');
                fseek($handle, $lastSize);
                
                while (($line = fgets($handle)) !== false) {
                    $line = rtrim($line);
                    if ($this->matchesFilters($line, $filter, $level)) {
                        $this->displayLogEntry($line);
                    }
                }
                
                fclose($handle);
                $lastSize = $currentSize;
            }
            
            usleep(500000); // 0.5秒间隔
        }
    }

    /**
     * 检查日志条目是否匹配过滤条件
     */
    private function matchesFilters($entry, $filter = null, $level = null, $since = null)
    {
        // 过滤关键词
        if ($filter && stripos($entry, $filter) === false) {
            return false;
        }

        // 过滤日志级别
        if ($level) {
            $levelPattern = '/\.' . strtoupper($level) . ':/';
            if (!preg_match($levelPattern, $entry)) {
                return false;
            }
        }

        // 过滤时间
        if ($since) {
            $sinceTime = Carbon::parse($since);
            if (preg_match('/^\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})\]/', $entry, $matches)) {
                $entryTime = Carbon::parse($matches[1]);
                if ($entryTime->lt($sinceTime)) {
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * 显示日志条目
     */
    private function displayLogEntry($entry)
    {
        // 解析日志级别并着色
        if (strpos($entry, '.ERROR:') !== false) {
            $this->line('<fg=red>' . $entry . '</fg=red>');
        } elseif (strpos($entry, '.WARNING:') !== false) {
            $this->line('<fg=yellow>' . $entry . '</fg=yellow>');
        } elseif (strpos($entry, '.INFO:') !== false) {
            $this->line('<fg=green>' . $entry . '</fg=green>');
        } elseif (strpos($entry, '.DEBUG:') !== false) {
            $this->line('<fg=cyan>' . $entry . '</fg=cyan>');
        } else {
            $this->line($entry);
        }
        
        $this->line(''); // 空行分隔
    }
}
