<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;

class ConsumeQueueTasks extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'queue:consume-tasks
                            {--queue=default : 要消费的队列名称}
                            {--timeout=60 : 工作进程超时时间（秒）}
                            {--memory=128 : 内存限制（MB）}
                            {--sleep=3 : 空闲时休眠时间（秒）}
                            {--tries=3 : 任务最大尝试次数}
                            {--daemon : 以守护进程模式运行}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '启动队列消费者，处理数据源任务';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🚀 启动队列消费者...');
        $this->info('按 Ctrl+C 停止消费者');
        $this->newLine();

        // 显示配置信息
        $this->displayConfiguration();

        // 构建队列工作命令参数
        $arguments = [
            'connection' => config('queue.default'),
            '--queue' => $this->option('queue'),
            '--timeout' => $this->option('timeout'),
            '--memory' => $this->option('memory'),
            '--sleep' => $this->option('sleep'),
            '--tries' => $this->option('tries'),
        ];

        // 如果是守护进程模式，添加对应参数
        if ($this->option('daemon')) {
            $arguments['--daemon'] = true;
            $this->warn('⚠️  守护进程模式已启用，请确保在生产环境中使用进程监控工具');
        }

        try {
            // 启动队列工作进程
            $exitCode = Artisan::call('queue:work', $arguments);
            
            if ($exitCode === 0) {
                $this->info('✅ 队列消费者正常退出');
            } else {
                $this->error('❌ 队列消费者异常退出，退出码: ' . $exitCode);
            }

        } catch (\Exception $e) {
            $this->error('❌ 启动队列消费者时发生错误: ' . $e->getMessage());
            return 1;
        }

        return 0;
    }

    /**
     * 显示配置信息
     */
    private function displayConfiguration(): void
    {
        $this->info('📋 配置信息:');
        $this->line('  队列连接: ' . config('queue.default'));
        $this->line('  队列名称: ' . $this->option('queue'));
        $this->line('  超时时间: ' . $this->option('timeout') . ' 秒');
        $this->line('  内存限制: ' . $this->option('memory') . ' MB');
        $this->line('  休眠时间: ' . $this->option('sleep') . ' 秒');
        $this->line('  最大尝试: ' . $this->option('tries') . ' 次');
        $this->line('  守护进程: ' . ($this->option('daemon') ? '是' : '否'));
        $this->newLine();

        // 检查队列连接状态
        $this->checkQueueConnection();
    }

    /**
     * 检查队列连接状态
     */
    private function checkQueueConnection(): void
    {
        try {
            $connection = config('queue.default');
            
            switch ($connection) {
                case 'database':
                    // 检查数据库连接和jobs表
                    \DB::connection()->getPdo();
                    if (\Schema::hasTable('jobs')) {
                        $this->info('✅ 数据库队列连接正常');
                    } else {
                        $this->error('❌ jobs表不存在，请运行: php artisan queue:table && php artisan migrate');
                    }
                    break;

                case 'redis':
                    // 检查Redis连接
                    $redis = \Redis::connection();
                    $redis->ping();
                    $this->info('✅ Redis队列连接正常');
                    break;

                default:
                    $this->warn('⚠️  未知的队列驱动: ' . $connection);
            }

        } catch (\Exception $e) {
            $this->error('❌ 队列连接检查失败: ' . $e->getMessage());
            $this->warn('请检查队列配置和服务状态');
        }
    }
}
