<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\DataSource;

class ListDataSources extends Command
{
    protected $signature = 'list:data-sources';
    protected $description = '列出所有数据源';

    public function handle()
    {
        $dataSources = DataSource::all();
        
        if ($dataSources->count() === 0) {
            $this->info('没有找到数据源');
            return;
        }

        $this->info('数据源列表:');
        foreach ($dataSources as $ds) {
            $this->info("ID: {$ds->id}, 名称: {$ds->name}, 类型: {$ds->source_type}, URL: {$ds->api_url}");
        }
    }
} 