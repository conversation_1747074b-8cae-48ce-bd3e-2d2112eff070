<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\ProductData;
use App\Models\MonitoringTask;
use App\Models\ProductSku;

class UpdateOfficialGuidePrices extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update:official-guide-prices {--dry-run : Show what would be updated without making changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '更新现有ProductSku记录的官方指导价，从监控任务配置中获取';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $dryRun = $this->option('dry-run');
        
        if ($dryRun) {
            $this->info('运行在预览模式，不会实际更新数据');
        }

        $this->info('开始更新官方指导价...');

        // 获取所有有监控任务ID的产品数据
        $productDataRecords = ProductData::whereNotNull('monitoring_task_id')->get();
        
        $totalUpdated = 0;
        $totalSkipped = 0;

        foreach ($productDataRecords as $productData) {
            $monitoringTaskId = $productData->monitoring_task_id;
            $itemId = $productData->item_id;

            // 获取监控任务
            $monitoringTask = MonitoringTask::find($monitoringTaskId);
            if (!$monitoringTask) {
                $this->warn("监控任务 {$monitoringTaskId} 不存在，跳过产品 {$itemId}");
                $totalSkipped++;
                continue;
            }

            // 获取官方指导价
            $officialGuidePrice = $this->getOfficialGuidePriceForProduct($monitoringTask, $itemId);
            
            if ($officialGuidePrice === null) {
                $this->warn("产品 {$itemId} 在监控任务 {$monitoringTaskId} 中没有配置官方指导价，跳过");
                $totalSkipped++;
                continue;
            }

            // 更新该产品的所有SKU
            $skus = $productData->skus;
            $skuCount = $skus->count();
            
            if ($skuCount === 0) {
                $this->warn("产品 {$itemId} 没有SKU记录，跳过");
                $totalSkipped++;
                continue;
            }

            $this->info("产品 {$itemId}: 找到 {$skuCount} 个SKU，官方指导价: {$officialGuidePrice}");

            if (!$dryRun) {
                // 批量更新SKU的官方指导价
                $skus->each(function ($sku) use ($officialGuidePrice) {
                    $sku->official_guide_price = $officialGuidePrice;
                    $sku->save();
                });
            }

            $totalUpdated += $skuCount;
        }

        if ($dryRun) {
            $this->info("预览完成：将更新 {$totalUpdated} 个SKU记录，跳过 {$totalSkipped} 个产品");
        } else {
            $this->info("更新完成：已更新 {$totalUpdated} 个SKU记录，跳过 {$totalSkipped} 个产品");
        }

        return 0;
    }

    /**
     * 获取商品的官方指导价
     *
     * @param MonitoringTask $monitoringTask
     * @param string $itemId
     * @return float|null
     */
    private function getOfficialGuidePriceForProduct(MonitoringTask $monitoringTask, string $itemId): ?float
    {
        $targetProducts = $monitoringTask->target_products ?? [];
        
        // 如果target_products是新格式（包含official_guide_price的对象数组）
        if (!empty($targetProducts) && is_array($targetProducts)) {
            foreach ($targetProducts as $product) {
                if (is_array($product) && 
                    isset($product['product_id']) && 
                    $product['product_id'] == $itemId &&
                    isset($product['official_guide_price'])) {
                    return (float)$product['official_guide_price'];
                }
            }
        }

        return null;
    }
}
