<?php

namespace App\Listeners;

use App\Models\AuditLog;
use App\Services\AuditLogService;
use Illuminate\Auth\Events\Attempting;
use Illuminate\Auth\Events\Authenticated;
use Illuminate\Auth\Events\Failed;
use Illuminate\Auth\Events\Login;
use Illuminate\Auth\Events\Logout;
use Illuminate\Auth\Events\PasswordReset;
use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Events\Verified;

class AuthEventListener
{
    protected AuditLogService $auditLogService;

    public function __construct(AuditLogService $auditLogService)
    {
        $this->auditLogService = $auditLogService;
    }

    /**
     * 处理用户登录尝试事件
     */
    public function handleAttempting(Attempting $event)
    {
        $this->auditLogService->logAuthAction(
            AuditLog::ACTION_LOGIN_ATTEMPT,
            null,
            [
                'guard' => $event->guard,
                'credentials' => ['email' => $event->credentials['email'] ?? 'unknown'],
            ],
            AuditLog::LEVEL_INFO
        );
    }

    /**
     * 处理用户登录成功事件
     */
    public function handleLogin(Login $event)
    {
        $this->auditLogService->logAuthAction(
            AuditLog::ACTION_LOGIN,
            $event->user->id,
            [
                'guard' => $event->guard,
                'user_email' => $event->user->email,
                'username' => $event->user->name,
            ],
            AuditLog::LEVEL_INFO
        );
    }

    /**
     * 处理用户登录失败事件
     */
    public function handleFailed(Failed $event)
    {
        $this->auditLogService->logAuthAction(
            AuditLog::ACTION_LOGIN_FAILED,
            null,
            [
                'guard' => $event->guard,
                'credentials' => ['email' => $event->credentials['email'] ?? 'unknown'],
            ],
            AuditLog::LEVEL_WARNING
        );
    }

    /**
     * 处理用户登出事件
     */
    public function handleLogout(Logout $event)
    {
        $this->auditLogService->logAuthAction(
            AuditLog::ACTION_LOGOUT,
            $event->user->id ?? null,
            [
                'guard' => $event->guard,
                'user_email' => $event->user->email ?? 'unknown',
                'username' => $event->user->name ?? 'unknown',
            ],
            AuditLog::LEVEL_INFO
        );
    }

    /**
     * 处理用户注册事件
     */
    public function handleRegistered(Registered $event)
    {
        $this->auditLogService->logAuthAction(
            AuditLog::ACTION_REGISTER,
            $event->user->id,
            [
                'user_email' => $event->user->email,
                'username' => $event->user->name,
            ],
            AuditLog::LEVEL_INFO
        );
    }

    /**
     * 处理密码重置事件
     */
    public function handlePasswordReset(PasswordReset $event)
    {
        $this->auditLogService->logAuthAction(
            AuditLog::ACTION_PASSWORD_RESET,
            $event->user->id,
            [
                'user_email' => $event->user->email,
            ],
            AuditLog::LEVEL_WARNING
        );
    }

    /**
     * 处理邮箱验证事件
     */
    public function handleVerified(Verified $event)
    {
        $this->auditLogService->logAuthAction(
            AuditLog::ACTION_EMAIL_VERIFIED,
            $event->user->id,
            [
                'user_email' => $event->user->email,
            ],
            AuditLog::LEVEL_INFO
        );
    }
} 