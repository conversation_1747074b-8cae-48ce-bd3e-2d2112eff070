<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AuditLog extends Model
{
    use HasFactory;

    /**
     * 日志级别常量
     */
    const LEVEL_INFO = 'info';
    const LEVEL_WARNING = 'warning';
    const LEVEL_ERROR = 'error';
    const LEVEL_CRITICAL = 'critical';

    /**
     * 操作类型常量
     */
    const ACTION_CREATE = 'create';
    const ACTION_UPDATE = 'update';
    const ACTION_DELETE = 'delete';
    const ACTION_LOGIN = 'login';
    const ACTION_LOGOUT = 'logout';
    const ACTION_VIEW = 'view';
    const ACTION_EXPORT = 'export';
    const ACTION_IMPORT = 'import';
    const ACTION_ACTIVATE = 'activate';
    const ACTION_DEACTIVATE = 'deactivate';
    const ACTION_LOGIN_ATTEMPT = 'login_attempt';
    const ACTION_LOGIN_FAILED = 'login_failed';
    const ACTION_PASSWORD_RESET = 'password_reset';
    const ACTION_EMAIL_VERIFIED = 'email_verified';
    const ACTION_REGISTER = 'register';
    const ACTION_BULK_UPDATE = 'bulk_update';
    const ACTION_BULK_DELETE = 'bulk_delete';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'username',
        'action',
        'module',
        'controller',
        'method',
        'description',
        'request_method',
        'request_url',
        'request_params',
        'request_headers',
        'user_agent',
        'ip_address',
        'session_id',
        'auditable_type',
        'auditable_id',
        'old_values',
        'new_values',
        'changed_fields',
        'response_code',
        'response_message',
        'execution_time',
        'risk_level',
        'is_suspicious',
        'risk_factors',
        'country',
        'region',
        'city',
        'latitude',
        'longitude',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'request_params' => 'array',
        'request_headers' => 'array',
        'old_values' => 'array',
        'new_values' => 'array',
        'changed_fields' => 'array',
        'risk_factors' => 'array',
    ];

    /**
     * 获取关联的用户
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 获取所有操作类型的中文映射
     *
     * @return array
     */
    public static function getActions(): array
    {
        return [
            self::ACTION_CREATE => '创建',
            self::ACTION_UPDATE => '更新',
            self::ACTION_DELETE => '删除',
            self::ACTION_LOGIN => '登录',
            self::ACTION_LOGOUT => '登出',
            self::ACTION_VIEW => '查看',
            self::ACTION_EXPORT => '导出',
            self::ACTION_IMPORT => '导入',
            self::ACTION_ACTIVATE => '激活',
            self::ACTION_DEACTIVATE => '停用',
            self::ACTION_LOGIN_ATTEMPT => '登录尝试',
            self::ACTION_LOGIN_FAILED => '登录失败',
            self::ACTION_PASSWORD_RESET => '密码重置',
            self::ACTION_EMAIL_VERIFIED => '邮箱验证',
            self::ACTION_REGISTER => '注册',
            self::ACTION_BULK_UPDATE => '批量更新',
            self::ACTION_BULK_DELETE => '批量删除',
        ];
    }
}