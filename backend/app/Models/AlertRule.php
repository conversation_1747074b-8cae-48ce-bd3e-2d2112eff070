<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class AlertRule extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'name',
        'description',
        'rule_type',
        'conditions',
        'severity',
        'priority',
        'notification_method',
        'status',
        'last_triggered_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'rule_type' => 'array',
        'conditions' => 'array',
        'notification_method' => 'array',
        'status' => 'boolean',
        'last_triggered_at' => 'datetime',
    ];

    /**
     * 创建者
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 生成的预警
     */
    public function alerts(): HasMany
    {
        return $this->hasMany(Alert::class);
    }

    /**
     * 获取使用此规则的监控任务
     */
    public function monitoringTasks()
    {
        return MonitoringTask::whereJsonContains('alert_rule_ids', $this->id)->get();
    }

    /**
     * 检查规则是否启用
     */
    public function isEnabled(): bool
    {
        return $this->status === 1;
    }

    /**
     * 检查规则是否禁用
     */
    public function isDisabled(): bool
    {
        return $this->status === 0;
    }

    // 移除冷却期和频率限制相关方法，因为这些功能已被简化

    /**
     * 获取可用的规则类型
     */
    public static function getAvailableRuleTypes(): array
    {
        return [
            'promotion_price_deviation' => '促销价偏离率',
            'channel_price_deviation' => '渠道价格偏离率',
            'listing_status_change' => '上下架状态',
        ];
    }

    /**
     * 获取可用的操作符
     */
    public static function getAvailableOperators(): array
    {
        return [
            '>' => '大于',
            '<' => '小于',
            '>=' => '大于等于',
            '<=' => '小于等于',
            '=' => '等于',
            '!=' => '不等于',
        ];
    }

    /**
     * 获取可用的严重级别
     */
    public static function getAvailableSeverities(): array
    {
        return [
            'low' => '低',
            'medium' => '中',
            'high' => '高',
            'critical' => '严重'
        ];
    }

    /**
     * 获取可用的优先级
     */
    public static function getAvailablePriorities(): array
    {
        return [
            'low' => '低',
            'medium' => '中',
            'high' => '高',
            'urgent' => '紧急'
        ];
    }

    /**
     * 获取可用的通知方式
     */
    public static function getAvailableNotificationMethods(): array
    {
        return [
            'email' => '邮件通知',
            'system' => '站内信'
        ];
    }

    /**
     * 检查规则类型是否需要操作符和阈值
     */
    public function needsOperatorAndThreshold(string $ruleType): bool
    {
        $typesWithThreshold = ['promotion_price_deviation', 'channel_price_deviation'];
        return in_array($ruleType, $typesWithThreshold);
    }

    /**
     * 验证阈值配置
     */
    public function validateThresholdValues(): bool
    {
        if ($this->rule_type === 'listing_status_change') {
            return true; // 上下架状态不需要阈值验证
        }

        if (is_array($this->rule_type)) {
            foreach ($this->rule_type as $type) {
                if ($this->needsOperatorAndThreshold($type)) {
                    if (!isset($this->conditions[$type]['operator']) || !isset($this->conditions[$type]['threshold'])) {
                        return false;
                    }
                    $value = $this->conditions[$type]['threshold'];
                    if (!is_numeric($value) || $value < 0) {
                        return false;
                    }
                }
            }
            return true;
        }

        return false;
    }
} 