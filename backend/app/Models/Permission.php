<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Permission extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'display_name',
        'description',
        'group',
        'resource',
        'action',
        'status',
        'sort_order',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'status' => 'integer',
        'sort_order' => 'integer',
    ];

    /**
     * 拥有此权限的角色
     */
    public function roles(): BelongsToMany
    {
        return $this->belongsToMany(Role::class, 'role_permissions', 'permission_id', 'role_id')
                    ->withTimestamps();
    }

    /**
     * 通过角色获取拥有此权限的用户
     */
    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'user_roles', 'role_id', 'user_id')
                    ->join('role_permissions', 'roles.id', '=', 'role_permissions.role_id')
                    ->where('role_permissions.permission_id', $this->id);
    }

    /**
     * 按组分组权限
     */
    public static function getByGroup(): \Illuminate\Support\Collection
    {
        return static::where('status', 1)
                    ->orderBy('group')
                    ->orderBy('sort_order')
                    ->get()
                    ->groupBy('group');
    }

    /**
     * 获取指定资源的权限
     */
    public static function getByResource(string $resource): \Illuminate\Database\Eloquent\Collection
    {
        return static::where('resource', $resource)
                    ->where('status', 1)
                    ->orderBy('sort_order')
                    ->get();
    }

    /**
     * 创建权限的便捷方法
     */
    public static function createPermission(string $name, string $displayName, string $group, string $resource, string $action, string $description = ''): self
    {
        return static::create([
            'name' => $name,
            'display_name' => $displayName,
            'description' => $description,
            'group' => $group,
            'resource' => $resource,
            'action' => $action,
            'status' => 1,
            'sort_order' => 0,
        ]);
    }
}
