<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProductSku extends Model
{
    use HasFactory;

    protected $fillable = [
        'product_data_id',
        'sku_id',
        'name',
        'price',
        'sub_price',
        'sub_price_title',
        'quantity',
        'image_url',
        'official_guide_price',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'sub_price' => 'decimal:2',
        'official_guide_price' => 'decimal:2',
        'quantity' => 'integer',
    ];

    protected $appends = [
        'promotion_deviation_rate',
        'channel_price_deviation_rate',
    ];

    /**
     * Get the product data that owns the SKU.
     */
    public function productData()
    {
        return $this->belongsTo(ProductData::class);
    }

    /**
     * 计算促销价偏离率（sku级）
     * =(Price - subPrice) / Price × 100%
     * 
     * @return float|null
     */
    public function getPromotionDeviationRateAttribute(): ?float
    {
        if (!$this->price || $this->price <= 0) {
            return 0.0;
        }

        // 根据需求文档：当subPrice为0或不存在时，偏离率为100%
        if (!$this->sub_price || $this->sub_price <= 0) {
            return 100.0;
        }
        
        $deviationRate = (($this->price - $this->sub_price) / $this->price) * 100;
        
        return round($deviationRate, 2);
    }

    /**
     * 计算渠道价格偏离率（sku级）
     * =（官方指导价 - subPrice）/ 官方指导价 × 100%
     * 
     * @return float|null
     */
    public function getChannelPriceDeviationRateAttribute(): ?float
    {
        if (!$this->official_guide_price || $this->official_guide_price <= 0) {
            return 0.0;
        }

        $subPrice = $this->sub_price;
        // 根据需求文档：当subPrice为0或不存在时，使用Price代替
        if (!$subPrice || $subPrice <= 0) {
            $subPrice = $this->price ?? 0;
        }
        
        $deviationRate = (($this->official_guide_price - $subPrice) / $this->official_guide_price) * 100;
        
        return round($deviationRate, 2);
    }
} 