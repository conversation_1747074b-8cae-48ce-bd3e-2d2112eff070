<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class TaskGroupAlertSummary extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'task_group_id',
        'monitoring_task_id',
        'collection_batch_id', // 用于标识同一批次的数据采集
        'summary_data',
        'alert_count',
        'product_count',
        'rule_count',
        'status',
        'notification_sent_at',
        'created_at',
        'updated_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'summary_data' => 'array',
        'alert_count' => 'integer',
        'product_count' => 'integer',
        'rule_count' => 'integer',
        'notification_sent_at' => 'datetime',
    ];

    /**
     * 状态常量
     */
    const STATUS_COLLECTING = 'collecting'; // 正在收集预警
    const STATUS_READY = 'ready';          // 准备发送通知
    const STATUS_SENT = 'sent';            // 已发送通知
    const STATUS_FAILED = 'failed';        // 发送失败

    /**
     * 所属任务分组
     */
    public function taskGroup(): BelongsTo
    {
        return $this->belongsTo(TaskGroup::class);
    }

    /**
     * 所属监控任务
     */
    public function monitoringTask(): BelongsTo
    {
        return $this->belongsTo(MonitoringTask::class);
    }

    /**
     * 相关的预警记录
     */
    public function alerts(): HasMany
    {
        return $this->hasMany(Alert::class, 'task_group_alert_summary_id');
    }

    /**
     * 添加预警到汇总中
     */
    public function addAlert(Alert $alert): void
    {
        $summaryData = $this->summary_data ?? [];
        
        // 获取预警规则信息
        $ruleId = $alert->alert_rule_id;
        $ruleName = $alert->alertRule->name ?? '未知规则';
        $ruleType = $alert->alert_type;
        
        // 获取商品信息
        $triggerData = $alert->trigger_data ?? [];
        $productData = $triggerData['product_data'] ?? [];
        $itemId = $productData['item_id'] ?? null;
        $productTitle = $productData['standardized_data']['title'] ?? '未知商品';
        
        // 初始化规则分组
        if (!isset($summaryData['rules'][$ruleId])) {
            $summaryData['rules'][$ruleId] = [
                'rule_name' => $ruleName,
                'rule_type' => $ruleType,
                'products' => [],
                'alert_count' => 0,
            ];
        }
        
        // 初始化商品分组
        if (!isset($summaryData['rules'][$ruleId]['products'][$itemId])) {
            $summaryData['rules'][$ruleId]['products'][$itemId] = [
                'item_id' => $itemId,
                'title' => $productTitle,
                'data_source' => $productData['data_source'] ?? '未知',
                'alerts' => [],
                'sku_alerts' => [], // SKU级预警汇总
            ];
        }
        
        // 添加预警详情
        $alertDetail = [
            'alert_id' => $alert->id,
            'message' => $alert->message,
            'target_value' => $triggerData['target_value'] ?? null,
            'target_field' => $triggerData['target_field'] ?? null,
            'operator' => $triggerData['operator'] ?? null,
            'threshold_values' => $triggerData['threshold_values'] ?? null,
            'created_at' => $alert->created_at->toISOString(),
        ];
        
        // 如果是SKU级预警，添加到sku_alerts中
        if (strpos($alert->message, 'SKU') !== false) {
            $summaryData['rules'][$ruleId]['products'][$itemId]['sku_alerts'][] = $alertDetail;
        } else {
            $summaryData['rules'][$ruleId]['products'][$itemId]['alerts'][] = $alertDetail;
        }
        
        $summaryData['rules'][$ruleId]['alert_count']++;
        
        // 更新汇总统计
        $this->update([
            'summary_data' => $summaryData,
            'alert_count' => $this->alert_count + 1,
        ]);
    }

    /**
     * 标记汇总为准备发送状态
     */
    public function markReady(): void
    {
        $summaryData = $this->summary_data ?? [];
        
        // 计算统计信息
        $productCount = 0;
        $ruleCount = count($summaryData['rules'] ?? []);
        
        foreach ($summaryData['rules'] ?? [] as $rule) {
            $productCount += count($rule['products'] ?? []);
        }
        
        $this->update([
            'status' => self::STATUS_READY,
            'product_count' => $productCount,
            'rule_count' => $ruleCount,
        ]);
    }

    /**
     * 标记通知已发送
     */
    public function markSent(): void
    {
        $this->update([
            'status' => self::STATUS_SENT,
            'notification_sent_at' => now(),
        ]);
    }

    /**
     * 标记发送失败
     */
    public function markFailed(): void
    {
        $this->update([
            'status' => self::STATUS_FAILED,
        ]);
    }

    /**
     * 生成预警简报
     */
    public function generateSummaryReport(): array
    {
        $summaryData = $this->summary_data ?? [];
        $report = [];
        
        foreach ($summaryData['rules'] ?? [] as $ruleId => $ruleData) {
            $ruleReport = [
                'rule_id' => $ruleId,
                'rule_name' => $ruleData['rule_name'],
                'rule_type' => $ruleData['rule_type'],
                'alert_count' => $ruleData['alert_count'],
                'product_count' => count($ruleData['products']),
                'products' => [],
            ];
            
            foreach ($ruleData['products'] as $itemId => $productData) {
                $productReport = [
                    'item_id' => $itemId,
                    'title' => $productData['title'],
                    'data_source' => $productData['data_source'],
                    'alert_summary' => $this->generateProductAlertSummary($productData),
                ];
                
                $ruleReport['products'][] = $productReport;
            }
            
            $report[] = $ruleReport;
        }
        
        return $report;
    }

    /**
     * 生成单个商品的预警简报
     */
    private function generateProductAlertSummary(array $productData): string
    {
        $summary = [];
        
        // 处理商品级预警
        if (!empty($productData['alerts'])) {
            foreach ($productData['alerts'] as $alert) {
                $summary[] = $alert['message'];
            }
        }
        
        // 处理SKU级预警汇总
        if (!empty($productData['sku_alerts'])) {
            $skuCount = count($productData['sku_alerts']);
            $summary[] = "有 {$skuCount} 个SKU触发预警";
            
            // 可以添加更详细的SKU预警信息
            foreach ($productData['sku_alerts'] as $skuAlert) {
                if (isset($skuAlert['target_value'])) {
                    $summary[] = "SKU预警值: {$skuAlert['target_value']}";
                }
            }
        }
        
        return implode('; ', $summary);
    }

    /**
     * 检查是否有预警
     */
    public function hasAlerts(): bool
    {
        return $this->alert_count > 0;
    }

    /**
     * 获取格式化的通知消息
     */
    public function getFormattedNotificationMessage(): string
    {
        $taskGroupName = $this->taskGroup->name ?? '未知分组';
        $report = $this->generateSummaryReport();
        
        $message = "【任务分组预警汇总】{$taskGroupName}\n\n";
        $message .= "本次采集共发现 {$this->alert_count} 个预警，涉及 {$this->product_count} 个商品。\n\n";
        
        foreach ($report as $ruleReport) {
            $message .= "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";
            $message .= "📋 预警规则：{$ruleReport['rule_name']}\n";
            $message .= "📊 预警数量：{$ruleReport['alert_count']} 个\n";
            $message .= "🛍️ 涉及商品：{$ruleReport['product_count']} 个\n\n";
            
            foreach ($ruleReport['products'] as $productReport) {
                $message .= "• 商品：{$productReport['title']}\n";
                $message .= "  来源：{$productReport['data_source']}\n";
                $message .= "  预警：{$productReport['alert_summary']}\n\n";
            }
        }
        
        $message .= "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";
        $message .= "⏰ 生成时间：" . $this->created_at->format('Y-m-d H:i:s');
        
        return $message;
    }
}
