<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\Log;
use Cron\CronExpression;

class MonitoringTask extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'description',
        'user_id',
        'task_group_id',
        'data_source_id',
        'target_products',
        'monitor_fields',
        'frequency_type',
        'frequency_value',
        'cron_expression',
        'schedule_type',
        'execution_time',
        'execution_dates',
        'execution_weekdays',
        'status',
        'last_run_at',
        'next_run_at',
        'run_count',
        'success_count',
        'failed_count',
        'last_error',
        'total_products',
        'active_products',
        'alert_count',
        'auto_start',
        'notify_on_error',
        'notification_config',
        'alert_rule_ids',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'target_products' => 'array',
        'monitor_fields' => 'array',
        'notification_config' => 'array',
        'alert_rule_ids' => 'array',
        'execution_dates' => 'array',
        'execution_weekdays' => 'array',
        'frequency_value' => 'integer',
        'run_count' => 'integer',
        'success_count' => 'integer',
        'failed_count' => 'integer',
        'total_products' => 'integer',
        'active_products' => 'integer',
        'alert_count' => 'integer',
        'auto_start' => 'boolean',
        'notify_on_error' => 'boolean',
        'last_run_at' => 'datetime',
        'next_run_at' => 'datetime',
    ];

    /**
     * 任务创建者
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 所属数据源
     */
    public function dataSource(): BelongsTo
    {
        return $this->belongsTo(DataSource::class);
    }

    /**
     * 任务分组
     */
    public function taskGroup(): BelongsTo
    {
        return $this->belongsTo(TaskGroup::class);
    }

    /**
     * 任务预警
     */
    public function alerts(): HasMany
    {
        return $this->hasMany(Alert::class);
    }

    /**
     * 获取此任务下的所有预警规则
     */
    public function alertRules(): HasMany
    {
        return $this->hasMany(AlertRule::class);
    }

    /**
     * 获取关联的预警规则
     */
    public function getAssociatedAlertRules()
    {
        if (!$this->alert_rule_ids) {
            return collect();
        }
        
        return AlertRule::whereIn('id', $this->alert_rule_ids)
            ->where('status', 1) // 只获取启用的规则
            ->get();
    }

    /**
     * 检查任务是否正在运行
     */
    public function isRunning(): bool
    {
        return $this->status === 'running';
    }

    /**
     * 检查任务是否已暂停
     */
    public function isPaused(): bool
    {
        return $this->status === 'paused';
    }

    /**
     * 检查任务是否已停止
     */
    public function isStopped(): bool
    {
        return $this->status === 'stopped';
    }

    /**
     * 获取成功率
     */
    public function getSuccessRateAttribute(): float
    {
        if ($this->run_count == 0) {
            return 0;
        }
        
        return round(($this->success_count / $this->run_count) * 100, 2);
    }

    /**
     * 获取可用的任务状态
     */
    public static function getAvailableStatuses(): array
    {
        return [
            'pending' => '等待中',
            'running' => '运行中',
            'paused' => '已暂停',
            'stopped' => '已停止',
            'failed' => '失败',
        ];
    }

    /**
     * 获取可用的频率类型
     */
    public static function getFrequencyTypes(): array
    {
        return [
            'interval' => '间隔执行',
            'cron' => 'Cron表达式',
        ];
    }

    /**
     * 计算下次运行时间
     */
    public function calculateNextRunTime(): ?\Carbon\Carbon
    {
        if (!$this->cron_expression) {
            return null;
        }

        try {
            $cron = new CronExpression($this->cron_expression);
            return \Carbon\Carbon::instance($cron->getNextRunDate());
        } catch (\Exception $e) {
            Log::warning('任务的Cron表达式无效', [
                'task_id' => $this->id,
                'cron_expression' => $this->cron_expression,
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }
} 