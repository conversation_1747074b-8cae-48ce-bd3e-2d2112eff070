<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ProductSkuHistory extends Model
{
    use HasFactory;

    protected $table = 'product_skus_history';

    protected $fillable = [
        'product_data_history_id',
        'sku_id',
        'name',
        'price',
        'sub_price',
        'sub_price_title',
        'quantity',
        'image_url',
        'official_guide_price',
        'promotion_deviation_rate',
        'channel_price_deviation_rate',
        'collected_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'price' => 'decimal:2',
        'sub_price' => 'decimal:2',
        'official_guide_price' => 'decimal:2',
        'promotion_deviation_rate' => 'decimal:2',
        'channel_price_deviation_rate' => 'decimal:2',
        'quantity' => 'integer',
        'collected_at' => 'datetime',
    ];

    /**
     * 获取关联的历史商品数据
     */
    public function productDataHistory(): BelongsTo
    {
        return $this->belongsTo(ProductDataHistory::class);
    }

    /**
     * 计算促销价偏离率（sku级）
     * =(Price - subPrice) / Price × 100%
     */
    public function getPromotionDeviationRateAttribute($value): ?float
    {
        if ($value !== null) {
            return (float)$value;
        }

        if (!$this->price || $this->price <= 0) {
            return null;
        }

        $subPrice = $this->sub_price ?? $this->price;
        $deviationRate = (($this->price - $subPrice) / $this->price) * 100;
        
        return round($deviationRate, 2);
    }

    /**
     * 计算渠道价格偏离率（sku级）
     * =（官方指导价 - subPrice）/ 官方指导价 × 100%
     */
    public function getChannelPriceDeviationRateAttribute($value): ?float
    {
        if ($value !== null) {
            return (float)$value;
        }

        if (!isset($this->official_guide_price) || !$this->official_guide_price || $this->official_guide_price <= 0) {
            return null;
        }

        $subPrice = $this->sub_price ?? $this->price;
        if (!$subPrice || $subPrice <= 0) {
            $subPrice = $this->price ?? 0;
        }

        if ($subPrice <= 0) {
            return null;
        }
        
        $deviationRate = (($this->official_guide_price - $subPrice) / $this->official_guide_price) * 100;
        
        return round($deviationRate, 2);
    }
} 