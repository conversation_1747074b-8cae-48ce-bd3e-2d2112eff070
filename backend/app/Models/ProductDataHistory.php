<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ProductDataHistory extends Model
{
    use HasFactory;

    protected $table = 'product_data_history';

    protected $fillable = [
        'monitoring_task_id',
        'item_id',
        'standardized_data',
        'collected_at',
        'code',
        'title',
        'product_image',
        'price',
        'lowest_price',
        'highest_price',
        'min_hand_price',
        'max_hand_price',
        'stock',
        'sales',
        'comment_count',
        'state',
        'has_sku',
        'item_type',
        'category_id',
        'category_path',
        'shop_id',
        'shop_name',
        'props',
        'promotion',
        'delivery_location',
        'product_url',
    ];

    protected $casts = [
        'standardized_data' => 'array',
        'props' => 'json',
        'promotion' => 'json',
        'collected_at' => 'datetime',
        'price' => 'decimal:2',
        'lowest_price' => 'decimal:2',
        'highest_price' => 'decimal:2',
        'min_hand_price' => 'decimal:2',
        'max_hand_price' => 'decimal:2',
        'stock' => 'integer',
        'sales' => 'integer',
        'comment_count' => 'integer',
        'code' => 'integer',
        'shop_id' => 'integer',
        'category_id' => 'integer',
        'has_sku' => 'boolean',
    ];

    /**
     * 获取监控任务
     */
    public function monitoringTask(): BelongsTo
    {
        return $this->belongsTo(MonitoringTask::class);
    }

    /**
     * 获取关联的SKU历史数据
     */
    public function skusHistory(): HasMany
    {
        return $this->hasMany(ProductSkuHistory::class);
    }

    /**
     * 通过监控任务获取数据源
     */
    public function getDataSourceAttribute()
    {
        return $this->monitoringTask?->dataSource;
    }

    /**
     * 获取商品主图
     */
    public function getMainImageAttribute(): ?string
    {
        if (!empty($this->product_image)) {
            return $this->product_image;
        }

        $data = $this->standardized_data;
        
        if (isset($data['main_imgs']) && is_array($data['main_imgs']) && !empty($data['main_imgs'][0])) {
            return $data['main_imgs'][0];
        }
        
        if (isset($data['pic_urls']) && is_array($data['pic_urls']) && !empty($data['pic_urls'][0])) {
            return $data['pic_urls'][0];
        }

        return null;
    }

    /**
     * 按商品ID和时间范围查询历史数据
     */
    public static function getProductHistory($taskId, $itemId, $startDate = null, $endDate = null)
    {
        $query = static::where('monitoring_task_id', $taskId)
                      ->where('item_id', $itemId)
                      ->with('skusHistory')
                      ->orderBy('collected_at', 'desc');

        if ($startDate) {
            $query->where('collected_at', '>=', $startDate);
        }

        if ($endDate) {
            $query->where('collected_at', '<=', $endDate);
        }

        return $query;
    }

    /**
     * 获取最新的历史记录
     */
    public static function getLatestHistory($taskId, $itemId)
    {
        return static::where('monitoring_task_id', $taskId)
                    ->where('item_id', $itemId)
                    ->with('skusHistory')
                    ->orderBy('collected_at', 'desc')
                    ->first();
    }

    /**
     * 获取指定时间的历史记录
     */
    public static function getHistoryAtTime($taskId, $itemId, $targetTime)
    {
        return static::where('monitoring_task_id', $taskId)
                    ->where('item_id', $itemId)
                    ->where('collected_at', '<=', $targetTime)
                    ->with('skusHistory')
                    ->orderBy('collected_at', 'desc')
                    ->first();
    }
} 