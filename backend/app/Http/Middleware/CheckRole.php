<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Log;

class CheckRole
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, ...$roles): Response
    {
        // 检查用户是否已认证
        if (!$request->user()) {
            return response()->json([
                'success' => false,
                'message' => '未认证，请先登录'
            ], 401);
        }

        // 如果没有指定角色，只需要认证即可
        if (empty($roles)) {
            return $next($request);
        }

        $user = $request->user();
        
        // 检查用户状态
        if ($user->status !== 1) {
            return response()->json([
                'success' => false,
                'message' => '账户已被禁用'
            ], 403);
        }
        
        // 加载用户的角色
        $user->load('roles');
        
        // 记录角色检查日志
        Log::info('角色检查', [
            'user_id' => $user->id,
            'username' => $user->username,
            'required_roles' => $roles,
            'user_roles' => $user->roles->pluck('name')->toArray(),
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'url' => $request->fullUrl(),
            'method' => $request->method()
        ]);
        
        // 检查是否满足角色要求
        $hasRole = $this->checkRoles($user, $roles);
        
        if (!$hasRole) {
            Log::warning('角色验证失败', [
                'user_id' => $user->id,
                'username' => $user->username,
                'required_roles' => $roles,
                'user_roles' => $user->roles->pluck('name')->toArray(),
                'ip' => $request->ip(),
                'url' => $request->fullUrl()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => '角色权限不足，无法访问此资源',
                'required_roles' => $roles
            ], 403);
        }

        return $next($request);
    }
    
    /**
     * 检查用户角色
     * 
     * @param \App\Models\User $user
     * @param array $roles
     * @return bool
     */
    private function checkRoles($user, array $roles): bool
    {
        // 检查是否拥有任一角色（OR逻辑）
        foreach ($roles as $role) {
            // 支持角色组合检查，使用 & 分隔表示AND逻辑
            if (str_contains($role, '&')) {
                $requiredRoles = explode('&', $role);
                $hasAllRoles = true;
                
                foreach ($requiredRoles as $requiredRole) {
                    if (!$user->hasRole(trim($requiredRole))) {
                        $hasAllRoles = false;
                        break;
                    }
                }
                
                if ($hasAllRoles) {
                    return true;
                }
            } else {
                // 单个角色检查
                if ($user->hasRole($role)) {
                    return true;
                }
            }
        }
        
        return false;
    }
} 