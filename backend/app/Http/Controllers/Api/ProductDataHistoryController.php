<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\ProductDataHistory;
use App\Models\MonitoringTask;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class ProductDataHistoryController extends Controller
{
    /**
     * 获取商品历史数据列表（按时间分页）
     */
    public function getProductHistory(Request $request): JsonResponse
    {
        $taskId = $request->route('taskId') ?? $request->get('task_id');
        $itemId = $request->route('itemId') ?? $request->get('item_id');
        
        if (!$taskId || !$itemId) {
            return response()->json([
                'error' => '参数缺失：需要task_id和item_id'
            ], 400);
        }

        // 验证用户权限
        $user = Auth::user();
        $taskQuery = MonitoringTask::where('id', $taskId);

        // 如果不是管理员，则限制只能查看自己的任务
        if (!$user->hasRole('admin')) {
            $taskQuery->where('user_id', $user->id);
        }

        $task = $taskQuery->first();
        
        if (!$task) {
            return response()->json(['error' => '无权访问该任务'], 403);
        }

        $query = ProductDataHistory::getProductHistory($taskId, $itemId);

        // 时间范围筛选
        if ($request->filled('date_from')) {
            $query->where('collected_at', '>=', $request->get('date_from'));
        }
        
        if ($request->filled('date_to')) {
            $query->where('collected_at', '<=', $request->get('date_to'));
        }

        // 分页
        $perPage = $request->get('per_page', 20);
        $histories = $query->paginate($perPage);

        // 格式化数据
        $formattedData = $histories->getCollection()->map(function ($history) {
            return [
                'id' => $history->id,
                'item_id' => $history->item_id,
                'title' => $history->title,
                'product_image' => $history->product_image ?? $history->main_image,
                'price' => $history->price,
                'lowest_price' => $history->lowest_price,
                'highest_price' => $history->highest_price,
                'min_hand_price' => $history->min_hand_price,
                'max_hand_price' => $history->max_hand_price,
                'stock' => $history->stock,
                'sales' => $history->sales,
                'comment_count' => $history->comment_count,
                'has_sku' => $history->has_sku,
                'collected_at' => $history->collected_at,
                'formatted_collected_at' => $history->collected_at->format('Y-m-d H:i:s'),
                'data_source' => $history->monitoringTask?->dataSource?->name ?? '未知数据源',
            ];
        });

        return response()->json([
            'success' => true,
            'data' => [
                'data' => $formattedData,
                'current_page' => $histories->currentPage(),
                'per_page' => $histories->perPage(),
                'total' => $histories->total(),
                'last_page' => $histories->lastPage(),
            ]
        ]);
    }

    /**
     * 获取特定时间点的商品详细信息
     */
    public function getProductHistoryDetail(Request $request): JsonResponse
    {
        $historyId = $request->route('historyId');

        // 优先通过 historyId 进行查询
        if ($historyId) {
            $history = ProductDataHistory::with('skusHistory')->find($historyId);

            if (!$history) {
                return response()->json(['error' => '未找到指定的历史记录'], 404);
            }

            // 从找到的记录中获取 taskId 以进行权限验证
            $taskId = $history->monitoring_task_id;
            
            // 验证用户权限
            $user = Auth::user();
            $taskQuery = MonitoringTask::where('id', $taskId);

            if (!$user->hasRole('admin')) {
                $taskQuery->where('user_id', $user->id);
            }
            
            $task = $taskQuery->first();
            
            if (!$task) {
                return response()->json(['error' => '无权访问该任务的历史记录'], 403);
            }
        } else {
            // 兼容旧的通过 taskId 和 itemId 查询的方式
            $taskId = $request->query('task_id');
            $itemId = $request->query('item_id');
            $targetTime = $request->query('target_time');
            
            if (!$taskId || !$itemId) {
                return response()->json(['error' => '参数缺失：需要task_id和item_id'], 400);
            }

            // 验证用户权限
            $user = Auth::user();
            $taskQuery = MonitoringTask::where('id', $taskId);

            if (!$user->hasRole('admin')) {
                $taskQuery->where('user_id', $user->id);
            }
            
            $task = $taskQuery->first();
            
            if (!$task) {
                return response()->json(['error' => '无权访问该任务'], 403);
            }

            if ($targetTime) {
                $history = ProductDataHistory::getHistoryAtTime($taskId, $itemId, $targetTime);
            } else {
                $history = ProductDataHistory::getLatestHistory($taskId, $itemId);
            }
        }

        if (!$history) {
            return response()->json(['error' => '未找到历史记录'], 404);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'id' => $history->id,
                'item_id' => $history->item_id,
                'title' => $history->title,
                'product_image' => $history->product_image ?? $history->main_image,
                'product_url' => $history->product_url,
                
                // 价格信息
                'price' => $history->price,
                'original_price' => $history->original_price,
                'lowest_price' => $history->lowest_price,
                'highest_price' => $history->highest_price,
                'min_hand_price' => $history->min_hand_price,
                'max_hand_price' => $history->max_hand_price,
                
                // 库存和销量
                'stock' => $history->stock,
                'sales' => $history->sales,
                'comment_count' => $history->comment_count,
                
                // 状态信息
                'code' => $history->code ?? 200,
                'status' => $history->state ?? 'ok',
                'state' => $history->state,
                'has_sku' => $history->has_sku,
                'item_type' => $history->item_type,
                
                // 分类和店铺信息
                'category_id' => $history->category_id,
                'category_path' => $history->category_path,
                'shop_id' => $history->shop_id,
                'shop_name' => $history->shop_name,
                
                // 商品属性
                'props' => $history->props,
                'description' => $history->description,
                'promotion' => $history->promotion,
                'delivery_location' => $history->delivery_location,
                
                // 时间信息
                'collected_at' => $history->collected_at,
                'formatted_collected_at' => $history->collected_at->format('Y-m-d H:i:s'),
                
                // 关联数据
                'monitoring_task_id' => $history->monitoring_task_id,
                'monitoring_task' => [
                    'id' => $history->monitoringTask->id,
                    'name' => $history->monitoringTask->name,
                ],
                'data_source' => $history->monitoringTask?->dataSource?->name ?? '未知数据源',
                
                // SKU历史数据
                'skus' => $history->skusHistory->map(function ($sku) {
                    return [
                        'sku_id' => $sku->sku_id,
                        'spec_name' => $sku->spec_name,
                        'name' => $sku->name,
                        'price' => $sku->price,
                        'original_price' => $sku->original_price,
                        'sub_price' => $sku->sub_price,
                        'sub_price_title' => $sku->sub_price_title,
                        'quantity' => $sku->quantity,
                        'status' => $sku->status,
                        'image_url' => $sku->image_url,
                        'sku_url' => $sku->sku_url,
                        'official_guide_price' => $sku->official_guide_price,
                        'promotion_deviation_rate' => $sku->promotion_deviation_rate,
                        'channel_price_deviation_rate' => $sku->channel_price_deviation_rate,
                    ];
                })->toArray(),
                
                // 原始标准化数据（用于调试）
                'standardized_data' => $history->standardized_data,
            ]
        ]);
    }

    /**
     * 获取商品历史趋势数据（用于图表展示）
     */
    public function getProductTrends(Request $request): JsonResponse
    {
        $taskId = $request->route('taskId') ?? $request->get('task_id');
        $itemId = $request->route('itemId') ?? $request->get('item_id');
        $days = $request->get('days', 30); // 默认30天
        
        if (!$taskId || !$itemId) {
            return response()->json([
                'error' => '参数缺失：需要task_id和item_id'
            ], 400);
        }

        // 验证用户权限
        $user = Auth::user();
        $taskQuery = MonitoringTask::where('id', $taskId);

        // 如果不是管理员，则限制只能查看自己的任务
        if (!$user->hasRole('admin')) {
            $taskQuery->where('user_id', $user->id);
        }

        $task = $taskQuery->first();
        
        if (!$task) {
            return response()->json(['error' => '无权访问该任务'], 403);
        }

        $startDate = Carbon::now()->subDays($days);
        
        $histories = ProductDataHistory::where('monitoring_task_id', $taskId)
            ->where('item_id', $itemId)
            ->where('collected_at', '>=', $startDate)
            ->orderBy('collected_at', 'asc')
            ->get(['collected_at', 'price', 'lowest_price', 'highest_price', 'stock', 'sales']);

        // 格式化趋势数据
        $trends = [
            'price_trend' => [],
            'stock_trend' => [],
            'sales_trend' => [],
        ];

        foreach ($histories as $history) {
            $dateKey = $history->collected_at->format('Y-m-d H:i');
            
            $trends['price_trend'][] = [
                'date' => $dateKey,
                'timestamp' => $history->collected_at->timestamp,
                'price' => $history->price,
                'lowest_price' => $history->lowest_price,
                'highest_price' => $history->highest_price,
            ];
            
            $trends['stock_trend'][] = [
                'date' => $dateKey,
                'timestamp' => $history->collected_at->timestamp,
                'stock' => $history->stock,
            ];
            
            $trends['sales_trend'][] = [
                'date' => $dateKey,
                'timestamp' => $history->collected_at->timestamp,
                'sales' => $history->sales,
            ];
        }

        return response()->json([
            'data' => $trends,
            'period' => [
                'start_date' => $startDate->format('Y-m-d'),
                'end_date' => Carbon::now()->format('Y-m-d'),
                'days' => $days,
                'total_records' => $histories->count(),
            ]
        ]);
    }

    /**
     * 获取商品历史数据摘要
     */
    public function getProductHistorySummary(Request $request): JsonResponse
    {
        $taskId = $request->route('taskId') ?? $request->get('task_id');
        $itemId = $request->route('itemId') ?? $request->get('item_id');
        
        if (!$taskId || !$itemId) {
            return response()->json([
                'error' => '参数缺失：需要task_id和item_id'
            ], 400);
        }

        // 验证用户权限
        $user = Auth::user();
        $taskQuery = MonitoringTask::where('id', $taskId);

        // 如果不是管理员，则限制只能查看自己的任务
        if (!$user->hasRole('admin')) {
            $taskQuery->where('user_id', $user->id);
        }
        
        $task = $taskQuery->first();
        
        if (!$task) {
            return response()->json(['error' => '无权访问该任务'], 403);
        }

        $query = ProductDataHistory::where('monitoring_task_id', $taskId)
            ->where('item_id', $itemId);

        $total = $query->count();
        $latest = $query->orderBy('collected_at', 'desc')->first();
        $oldest = $query->orderBy('collected_at', 'asc')->first();

        // 获取价格统计信息
        $priceStats = $query->selectRaw('
            MIN(price) as min_price,
            MAX(price) as max_price,
            AVG(price) as avg_price,
            MIN(lowest_price) as min_lowest_price,
            MAX(lowest_price) as max_lowest_price
        ')->first();

        return response()->json([
            'data' => [
                'total_records' => $total,
                'date_range' => [
                    'first_collected' => $oldest?->collected_at,
                    'last_collected' => $latest?->collected_at,
                ],
                'current_data' => $latest ? [
                    'title' => $latest->title,
                    'price' => $latest->price,
                    'stock' => $latest->stock,
                    'state' => $latest->state,
                ] : null,
                'price_statistics' => [
                    'min_price' => $priceStats->min_price,
                    'max_price' => $priceStats->max_price,
                    'avg_price' => round($priceStats->avg_price, 2),
                    'min_lowest_price' => $priceStats->min_lowest_price,
                    'max_lowest_price' => $priceStats->max_lowest_price,
                ],
            ]
        ]);
    }

    public function getLatestHistoryForAllProducts(Request $request): JsonResponse
    {
        $taskId = $request->route('taskId') ?? $request->get('task_id');
        if (!$taskId) {
            return response()->json([
                'error' => '参数缺失：需要task_id'
            ], 400);
        }

        // 验证用户权限
        $user = Auth::user();
        $taskQuery = MonitoringTask::where('id', $taskId);

        // 如果不是管理员，则限制只能查看自己的任务
        if (!$user->hasRole('admin')) {
            $taskQuery->where('user_id', $user->id);
        }
        
        $task = $taskQuery->first();
        
        if (!$task) {
            return response()->json(['error' => '无权访问该任务'], 403);
        }

        $histories = ProductDataHistory::where('monitoring_task_id', $taskId)
            ->orderBy('collected_at', 'desc')
            ->get();

        // 格式化数据
        $formattedData = $histories->map(function ($history) {
            return [
                'id' => $history->id,
                'item_id' => $history->item_id,
                'title' => $history->title,
                'product_image' => $history->product_image ?? $history->main_image,
                'price' => $history->price,
                'lowest_price' => $history->lowest_price,
                'highest_price' => $history->highest_price,
                'min_hand_price' => $history->min_hand_price,
                'max_hand_price' => $history->max_hand_price,
                'stock' => $history->stock,
                'sales' => $history->sales,
                'comment_count' => $history->comment_count,
                'state' => $history->state,
                'has_sku' => $history->has_sku,
                'collected_at' => $history->collected_at,
                'formatted_collected_at' => $history->collected_at->format('Y-m-d H:i:s'),
                'sku_count' => $history->skusHistory->count(),
                'data_source' => $history->monitoringTask?->dataSource?->name ?? '未知数据源',
            ];
        });

        return response()->json([
            'data' => $formattedData,
            'pagination' => [
                'current_page' => 1,
                'per_page' => $histories->count(),
                'total' => $histories->count(),
                'last_page' => 1,
            ]
        ]);
    }
} 