<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Permission;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;

class PermissionController extends Controller
{
    /**
     * 获取权限列表
     */
    public function index(Request $request): JsonResponse
    {
        $query = Permission::with('roles');
        
        // 支持搜索
        if ($request->has('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('display_name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('group', 'like', "%{$search}%")
                  ->orWhere('resource', 'like', "%{$search}%");
            });
        }
        
        // 按组筛选
        if ($request->has('group')) {
            $query->where('group', $request->get('group'));
        }
        
        // 按资源筛选
        if ($request->has('resource')) {
            $query->where('resource', $request->get('resource'));
        }
        
        // 支持状态筛选
        if ($request->has('status')) {
            $query->where('status', $request->get('status'));
        }
        
        // 排序
        $query->orderBy('group')
              ->orderBy('sort_order')
              ->orderBy('id');
        
        // 是否按组分组返回
        if ($request->get('grouped', false)) {
            $permissions = $query->get()->groupBy('group');
            return response()->json([
                'success' => true,
                'data' => $permissions,
                'message' => '权限列表获取成功（按组分组）'
            ]);
        }
        
        // 分页
        $perPage = $request->get('per_page', 15);
        $permissions = $query->paginate($perPage);
        
        return response()->json([
            'success' => true,
            'data' => $permissions,
            'message' => '权限列表获取成功'
        ]);
    }
    
    /**
     * 获取权限分组列表
     */
    public function groups(): JsonResponse
    {
        $groups = Permission::select('group')
            ->where('status', 1)
            ->distinct()
            ->orderBy('group')
            ->pluck('group');
        
        return response()->json([
            'success' => true,
            'data' => $groups,
            'message' => '权限分组列表获取成功'
        ]);
    }
    
    /**
     * 获取资源列表
     */
    public function resources(): JsonResponse
    {
        $resources = Permission::select('resource')
            ->where('status', 1)
            ->distinct()
            ->orderBy('resource')
            ->pluck('resource');
        
        return response()->json([
            'success' => true,
            'data' => $resources,
            'message' => '资源列表获取成功'
        ]);
    }
    
    /**
     * 获取单个权限详情
     */
    public function show(Permission $permission): JsonResponse
    {
        $permission->load('roles');
        
        return response()->json([
            'success' => true,
            'data' => $permission,
            'message' => '权限详情获取成功'
        ]);
    }
    
    /**
     * 创建新权限
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:100|unique:permissions,name',
            'display_name' => 'required|string|max:100',
            'description' => 'nullable|string|max:500',
            'group' => 'required|string|max:50',
            'resource' => 'required|string|max:50',
            'action' => 'required|string|max:50',
            'status' => 'integer|in:0,1',
            'sort_order' => 'integer|min:0',
        ]);
        
        $permission = Permission::create([
            'name' => $validated['name'],
            'display_name' => $validated['display_name'],
            'description' => $validated['description'] ?? '',
            'group' => $validated['group'],
            'resource' => $validated['resource'],
            'action' => $validated['action'],
            'status' => $validated['status'] ?? 1,
            'sort_order' => $validated['sort_order'] ?? 0,
        ]);
        
        return response()->json([
            'success' => true,
            'data' => $permission,
            'message' => '权限创建成功'
        ], 201);
    }
    
    /**
     * 更新权限
     */
    public function update(Request $request, Permission $permission): JsonResponse
    {
        $validated = $request->validate([
            'name' => [
                'required',
                'string',
                'max:100',
                Rule::unique('permissions', 'name')->ignore($permission->id),
            ],
            'display_name' => 'required|string|max:100',
            'description' => 'nullable|string|max:500',
            'group' => 'required|string|max:50',
            'resource' => 'required|string|max:50',
            'action' => 'required|string|max:50',
            'status' => 'integer|in:0,1',
            'sort_order' => 'integer|min:0',
        ]);
        
        $permission->update([
            'name' => $validated['name'],
            'display_name' => $validated['display_name'],
            'description' => $validated['description'] ?? '',
            'group' => $validated['group'],
            'resource' => $validated['resource'],
            'action' => $validated['action'],
            'status' => $validated['status'] ?? $permission->status,
            'sort_order' => $validated['sort_order'] ?? $permission->sort_order,
        ]);
        
        return response()->json([
            'success' => true,
            'data' => $permission,
            'message' => '权限更新成功'
        ]);
    }
    
    /**
     * 删除权限
     */
    public function destroy(Permission $permission): JsonResponse
    {
        // 检查是否有角色使用此权限
        if ($permission->roles()->count() > 0) {
            return response()->json([
                'success' => false,
                'message' => '该权限正在被角色使用，无法删除'
            ], 400);
        }
        
        $permission->delete();
        
        return response()->json([
            'success' => true,
            'message' => '权限删除成功'
        ]);
    }
    
    /**
     * 批量删除权限
     */
    public function batchDestroy(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'integer|exists:permissions,id',
        ]);
        
        $permissions = Permission::whereIn('id', $validated['ids'])->get();
        $deletedCount = 0;
        $errors = [];
        
        foreach ($permissions as $permission) {
            if ($permission->roles()->count() > 0) {
                $errors[] = "权限 '{$permission->display_name}' 正在被角色使用，无法删除";
                continue;
            }
            
            $permission->delete();
            $deletedCount++;
        }
        
        return response()->json([
            'success' => true,
            'data' => [
                'deleted_count' => $deletedCount,
                'errors' => $errors
            ],
            'message' => "成功删除 {$deletedCount} 个权限"
        ]);
    }
    
    /**
     * 批量创建权限
     */
    public function batchStore(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'permissions' => 'required|array',
            'permissions.*.name' => 'required|string|max:100|unique:permissions,name',
            'permissions.*.display_name' => 'required|string|max:100',
            'permissions.*.description' => 'nullable|string|max:500',
            'permissions.*.group' => 'required|string|max:50',
            'permissions.*.resource' => 'required|string|max:50',
            'permissions.*.action' => 'required|string|max:50',
            'permissions.*.status' => 'integer|in:0,1',
            'permissions.*.sort_order' => 'integer|min:0',
        ]);
        
        $createdPermissions = [];
        $errors = [];
        
        foreach ($validated['permissions'] as $permissionData) {
            try {
                $permission = Permission::create([
                    'name' => $permissionData['name'],
                    'display_name' => $permissionData['display_name'],
                    'description' => $permissionData['description'] ?? '',
                    'group' => $permissionData['group'],
                    'resource' => $permissionData['resource'],
                    'action' => $permissionData['action'],
                    'status' => $permissionData['status'] ?? 1,
                    'sort_order' => $permissionData['sort_order'] ?? 0,
                ]);
                
                $createdPermissions[] = $permission;
            } catch (\Exception $e) {
                $errors[] = "权限 '{$permissionData['name']}' 创建失败: " . $e->getMessage();
            }
        }
        
        return response()->json([
            'success' => true,
            'data' => [
                'created_permissions' => $createdPermissions,
                'created_count' => count($createdPermissions),
                'errors' => $errors
            ],
            'message' => "成功创建 " . count($createdPermissions) . " 个权限"
        ], 201);
    }
    
    /**
     * 获取权限的角色列表
     */
    public function roles(Permission $permission): JsonResponse
    {
        $roles = $permission->roles()->get();
        
        return response()->json([
            'success' => true,
            'data' => $roles,
            'message' => '权限角色列表获取成功'
        ]);
    }
} 