<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Alert;
use Illuminate\Http\JsonResponse;

class AlertsController extends Controller
{
    /**
     * 显示预警历史列表
     */
    public function index(Request $request): JsonResponse
    {
        $query = Alert::with(['alertRule:id,name', 'productData:id,item_id,product_name'])
                      ->orderBy('triggered_at', 'desc');

        // 筛选
        if ($request->filled('start_date')) {
            $query->where('triggered_at', '>=', $request->start_date);
        }
        if ($request->filled('end_date')) {
            $query->where('triggered_at', '<=', $request->end_date . ' 23:59:59');
        }
        if ($request->filled('level')) {
            $query->where('level', $request->level);
        }

        $alerts = $query->paginate($request->get('per_page', 15));

        return response()->json($alerts);
    }

    /**
     * 显示单个预警详情
     */
    public function show(Alert $alert): JsonResponse
    {
        $alert->load(['alertRule', 'productData', 'notification']);
        return response()->json(['data' => $alert]);
    }

    /**
     * 将预警标记为已读
     */
    public function markAsRead(Alert $alert): JsonResponse
    {
        $alert->update(['is_read' => true]);
        return response()->json(['success' => true, 'message' => '已标记为已读']);
    }

    /**
     * 批量删除预警
     */
    public function batchDelete(Request $request): JsonResponse
    {
        $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'integer|exists:alerts,id',
        ]);

        Alert::whereIn('id', $request->ids)->delete();

        return response()->json(['success' => true, 'message' => '批量删除成功']);
    }
} 