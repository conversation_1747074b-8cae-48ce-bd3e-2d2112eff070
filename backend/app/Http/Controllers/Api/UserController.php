<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Role;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;

class UserController extends Controller
{
    /**
     * 获取用户列表
     */
    public function index(Request $request): JsonResponse
    {
        $query = User::with('roles');
        
        // 支持搜索
        if ($request->has('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('username', 'like', "%{$search}%")
                  ->orWhere('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('real_name', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%");
            });
        }
        
        // 支持状态筛选
        if ($request->has('status')) {
            $query->where('status', $request->get('status'));
        }
        
        // 按角色筛选
        if ($request->has('role')) {
            $roleId = $request->get('role');
            $query->whereHas('roles', function ($q) use ($roleId) {
                $q->where('role_id', $roleId);
            });
        }
        
        // 排序
        $query->orderBy('id', 'desc');
        
        // 分页
        $perPage = $request->get('per_page', 15);
        $users = $query->paginate($perPage);
        
        return response()->json([
            'success' => true,
            'data' => $users,
            'message' => '用户列表获取成功'
        ]);
    }
    
    /**
     * 获取单个用户详情
     */
    public function show(User $user): JsonResponse
    {
        $user->load('roles.permissions');
        
        return response()->json([
            'success' => true,
            'data' => $user,
            'message' => '用户详情获取成功'
        ]);
    }
    
    /**
     * 创建新用户
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'username' => 'required|string|max:50|unique:users,username',
            'name' => 'required|string|max:100',
            'email' => 'required|email|unique:users,email',
            'password' => 'required|string|min:6',
            'real_name' => 'nullable|string|max:100',
            'phone' => 'nullable|string|max:20',
            'status' => 'integer|in:0,1',
            'roles' => 'array',
            'roles.*' => 'integer|exists:roles,id',
        ]);
        
        $user = User::create([
            'username' => $validated['username'],
            'name' => $validated['name'],
            'email' => $validated['email'],
            'password' => Hash::make($validated['password']),
            'real_name' => $validated['real_name'] ?? '',
            'phone' => $validated['phone'] ?? '',
            'status' => $validated['status'] ?? 1,
        ]);
        
        // 分配角色
        if (isset($validated['roles'])) {
            $pivotData = [];
            foreach ($validated['roles'] as $roleId) {
                $pivotData[$roleId] = [
                    'assigned_at' => now(),
                    'assigned_by' => Auth::id(),
                ];
            }
            $user->roles()->sync($pivotData);
        }
        
        $user->load('roles');
        
        return response()->json([
            'success' => true,
            'data' => $user,
            'message' => '用户创建成功'
        ], 201);
    }
    
    /**
     * 更新用户信息
     */
    public function update(Request $request, User $user): JsonResponse
    {
        $validated = $request->validate([
            'username' => [
                'required',
                'string',
                'max:50',
                Rule::unique('users', 'username')->ignore($user->id),
            ],
            'name' => 'required|string|max:100',
            'email' => [
                'required',
                'email',
                Rule::unique('users', 'email')->ignore($user->id),
            ],
            'password' => 'nullable|string|min:6',
            'real_name' => 'nullable|string|max:100',
            'phone' => 'nullable|string|max:20',
            'status' => 'integer|in:0,1',
            'roles' => 'array',
            'roles.*' => 'integer|exists:roles,id',
        ]);
        
        $updateData = [
            'username' => $validated['username'],
            'name' => $validated['name'],
            'email' => $validated['email'],
            'real_name' => $validated['real_name'] ?? $user->real_name,
            'phone' => $validated['phone'] ?? $user->phone,
            'status' => $validated['status'] ?? $user->status,
        ];
        
        // 如果提供了新密码，则更新密码
        if (!empty($validated['password'])) {
            $updateData['password'] = Hash::make($validated['password']);
        }
        
        $user->update($updateData);
        
        // 同步角色
        if (isset($validated['roles'])) {
            $pivotData = [];
            foreach ($validated['roles'] as $roleId) {
                $pivotData[$roleId] = [
                    'assigned_at' => now(),
                    'assigned_by' => Auth::id(),
                ];
            }
            $user->roles()->sync($pivotData);
        }
        
        $user->load('roles');
        
        return response()->json([
            'success' => true,
            'data' => $user,
            'message' => '用户更新成功'
        ]);
    }
    
    /**
     * 删除用户
     */
    public function destroy(User $user): JsonResponse
    {
        // 不能删除自己
        if ($user->id === Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => '不能删除自己的账户'
            ], 400);
        }
        
        // 移除所有角色关联
        $user->roles()->detach();
        
        // 删除用户
        $user->delete();
        
        return response()->json([
            'success' => true,
            'message' => '用户删除成功'
        ]);
    }
    
    /**
     * 批量删除用户
     */
    public function batchDestroy(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'integer|exists:users,id',
        ]);
        
        // 过滤掉当前用户ID
        $userIds = array_diff($validated['ids'], [Auth::id()]);
        
        if (empty($userIds)) {
            return response()->json([
                'success' => false,
                'message' => '没有可删除的用户'
            ], 400);
        }
        
        $users = User::whereIn('id', $userIds)->get();
        
        foreach ($users as $user) {
            $user->roles()->detach();
            $user->delete();
        }
        
        return response()->json([
            'success' => true,
            'data' => [
                'deleted_count' => count($users)
            ],
            'message' => "成功删除 " . count($users) . " 个用户"
        ]);
    }
    
    /**
     * 获取用户的角色列表
     */
    public function roles(User $user): JsonResponse
    {
        $roles = $user->roles()->with('permissions')->get();
        
        return response()->json([
            'success' => true,
            'data' => $roles,
            'message' => '用户角色列表获取成功'
        ]);
    }
    
    /**
     * 为用户分配角色
     */
    public function assignRoles(Request $request, User $user): JsonResponse
    {
        $validated = $request->validate([
            'roles' => 'required|array',
            'roles.*' => 'integer|exists:roles,id',
        ]);
        
        $pivotData = [];
        foreach ($validated['roles'] as $roleId) {
            $pivotData[$roleId] = [
                'assigned_at' => now(),
                'assigned_by' => Auth::id(),
            ];
        }
        
        $user->roles()->sync($pivotData);
        $user->load('roles');
        
        return response()->json([
            'success' => true,
            'data' => $user,
            'message' => '角色分配成功'
        ]);
    }
    
    /**
     * 移除用户的角色
     */
    public function removeRoles(Request $request, User $user): JsonResponse
    {
        $validated = $request->validate([
            'roles' => 'required|array',
            'roles.*' => 'integer|exists:roles,id',
        ]);
        
        $user->roles()->detach($validated['roles']);
        $user->load('roles');
        
        return response()->json([
            'success' => true,
            'data' => $user,
            'message' => '角色移除成功'
        ]);
    }
    
    /**
     * 获取用户的所有权限
     */
    public function permissions(User $user): JsonResponse
    {
        $permissions = $user->getAllPermissions();
        
        return response()->json([
            'success' => true,
            'data' => $permissions,
            'message' => '用户权限列表获取成功'
        ]);
    }
    
    /**
     * 更新用户状态
     */
    public function updateStatus(Request $request, User $user): JsonResponse
    {
        $validated = $request->validate([
            'status' => 'required|integer|in:0,1',
        ]);
        
        // 不能禁用自己
        if ($user->id === Auth::id() && $validated['status'] == 0) {
            return response()->json([
                'success' => false,
                'message' => '不能禁用自己的账户'
            ], 400);
        }
        
        $user->update(['status' => $validated['status']]);
        
        $statusText = $validated['status'] == 1 ? '启用' : '禁用';
        
        return response()->json([
            'success' => true,
            'data' => $user,
            'message' => "用户{$statusText}成功"
        ]);
    }
    
    /**
     * 重置用户密码
     */
    public function resetPassword(Request $request, User $user): JsonResponse
    {
        $validated = $request->validate([
            'password' => 'required|string|min:6|confirmed',
        ]);
        
        $user->update([
            'password' => Hash::make($validated['password'])
        ]);
        
        return response()->json([
            'success' => true,
            'message' => '密码重置成功'
        ]);
    }
} 