<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\DataSource;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;

class DataSourceController extends Controller
{
    /**
     * 获取数据源列表
     */
    public function index(Request $request): JsonResponse
    {
        $query = DataSource::with('owner');
        
        // 支持搜索
        if ($request->has('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('type', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('config.url', 'like', "%{$search}%");
            });
        }
        
        // 按类型筛选
        if ($request->has('type')) {
            $query->where('type', $request->get('type'));
        }
        
        // 支持状态筛选
        if ($request->has('status')) {
            $query->where('status', $request->get('status'));
        }
        
        // 按创建者筛选
        if ($request->has('owner_id')) {
            $query->where('owner_id', $request->get('owner_id'));
        }
        
        // 排序
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);
        
        // 分页
        $perPage = $request->get('per_page', 15);
        $dataSources = $query->paginate($perPage);
        
        return response()->json([
            'success' => true,
            'data' => $dataSources,
            'message' => '数据源列表获取成功'
        ]);
    }
    
    /**
     * 获取数据源类型列表
     */
    public function types(): JsonResponse
    {
        $types = DataSource::getAvailableTypes();
        
        return response()->json([
            'success' => true,
            'data' => $types,
            'message' => '数据源类型列表获取成功'
        ]);
    }
    
    /**
     * 获取单个数据源详情
     */
    public function show(DataSource $dataSource): JsonResponse
    {
        $dataSource->load('owner', 'fieldMappings', 'monitoringTasks');
        
        return response()->json([
            'success' => true,
            'data' => $dataSource,
            'message' => '数据源详情获取成功'
        ]);
    }
    
    /**
     * 创建新数据源
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:100|unique:data_sources,name',
            'type' => 'required|string|in:api,db,file,website',
            'description' => 'nullable|string|max:1000',
            'config' => 'required|array',
            'config.url' => 'required_if:type,api|nullable|string|max:255',
            'config.method' => 'required_if:type,api|nullable|string|in:GET,POST',
            'config.auth_type' => 'required_if:type,api|nullable|string',
            'config.token' => 'nullable|string',
            'config.api_key_name' => 'nullable|string',
            'config.api_key_value' => 'nullable|string',
            'config.db_type' => 'required_if:type,db|nullable|string',
            'config.host' => 'required_if:type,db|nullable|string',
            'config.port' => 'required_if:type,db|nullable|integer',
            'config.database' => 'required_if:type,db|nullable|string',
            'config.username' => 'required_if:type,db|nullable|string',
            'config.password' => 'nullable|string',
            'default_params' => 'nullable|array',
            'field_mapping' => 'nullable|array',
            'status' => 'integer|in:0,1',
        ]);
        
        $dataSource = DataSource::create([
            'name' => $validated['name'],
            'type' => $validated['type'],
            'description' => $validated['description'] ?? '',
            'config' => $validated['config'],
            'default_params' => $validated['default_params'] ?? [],
            'field_mapping' => $validated['field_mapping'] ?? [],
            'status' => $validated['status'] ?? 1,
            'owner_id' => Auth::id(),
        ]);
        
        $dataSource->load('owner');
        
        return response()->json([
            'success' => true,
            'data' => $dataSource,
            'message' => '数据源创建成功'
        ], 201);
    }
    
    /**
     * 更新数据源
     */
    public function update(Request $request, DataSource $dataSource): JsonResponse
    {
        $validated = $request->validate([
            'name' => [
                'required',
                'string',
                'max:100',
                Rule::unique('data_sources', 'name')->ignore($dataSource->id),
            ],
            'type' => 'required|string|in:api,db,file,website',
            'description' => 'nullable|string|max:1000',
            'config' => 'required|array',
            'config.url' => 'required_if:type,api|nullable|string|max:255',
            'config.method' => 'required_if:type,api|nullable|string|in:GET,POST',
            'config.auth_type' => 'required_if:type,api|nullable|string',
            'config.token' => 'nullable|string',
            'config.api_key_name' => 'nullable|string',
            'config.api_key_value' => 'nullable|string',
            'config.db_type' => 'required_if:type,db|nullable|string',
            'config.host' => 'required_if:type,db|nullable|string',
            'config.port' => 'required_if:type,db|nullable|integer',
            'config.database' => 'required_if:type,db|nullable|string',
            'config.username' => 'required_if:type,db|nullable|string',
            'config.password' => 'nullable|string',
            'default_params' => 'nullable|array',
            'field_mapping' => 'nullable|array',
            'status' => 'integer|in:0,1',
        ]);
        
        $dataSource->update($validated);
        
        $dataSource->load('owner');
        
        return response()->json([
            'success' => true,
            'data' => $dataSource,
            'message' => '数据源更新成功'
        ]);
    }
    
    /**
     * 删除数据源
     */
    public function destroy(DataSource $dataSource): JsonResponse
    {
        // 检查是否有关联的监控任务
        if ($dataSource->monitoringTasks()->count() > 0) {
            return response()->json([
                'success' => false,
                'message' => '该数据源正在被监控任务使用，无法删除'
            ], 400);
        }
        
        // 删除关联的字段映射
        $dataSource->fieldMappings()->delete();
        
        // 删除数据源
        $dataSource->delete();
        
        return response()->json([
            'success' => true,
            'message' => '数据源删除成功'
        ]);
    }
    
    /**
     * 批量删除数据源
     */
    public function batchDestroy(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'integer|exists:data_sources,id',
        ]);
        
        $dataSources = DataSource::whereIn('id', $validated['ids'])->get();
        $deletedCount = 0;
        $errors = [];
        
        foreach ($dataSources as $dataSource) {
            if ($dataSource->monitoringTasks()->count() > 0) {
                $errors[] = "数据源 '{$dataSource->name}' 正在被监控任务使用，无法删除";
                continue;
            }
            
            $dataSource->fieldMappings()->delete();
            $dataSource->delete();
            $deletedCount++;
        }
        
        return response()->json([
            'success' => true,
            'data' => [
                'deleted_count' => $deletedCount,
                'errors' => $errors
            ],
            'message' => "成功删除 {$deletedCount} 个数据源"
        ]);
    }
    
    /**
     * 测试数据源连接
     */
    public function testConnection(DataSource $dataSource): JsonResponse
    {
        try {
            // 从 config 中获取 headers 和 params
            $config = $dataSource->config ?? [];
            $headers = $config['headers'] ?? [];
            $params = $dataSource->default_params ?? [];
            $url = $config['url'] ?? null;

            if (!$url) {
                 return response()->json([
                    'success' => false,
                    'message' => '数据源配置中缺少URL'
                ], 400);
            }

            // 发送测试请求
            $response = Http::withHeaders($headers)
                ->timeout($config['timeout'] ?? 30)
                ->get($url, $params);
            
            $isSuccess = $response->successful();
            
            // 更新使用统计
            $dataSource->updateUsageStats($isSuccess);
            
            if ($isSuccess) {
                return response()->json([
                    'success' => true,
                    'data' => [
                        'status_code' => $response->status(),
                        'response_time' => $response->transferStats->getTransferTime(),
                        'response_size' => strlen($response->body()),
                    ],
                    'message' => '数据源连接测试成功'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'data' => [
                        'status_code' => $response->status(),
                        'error' => $response->body(),
                    ],
                    'message' => '数据源连接测试失败'
                ], 400);
            }
        } catch (\Exception $e) {
            // 更新使用统计
            $dataSource->updateUsageStats(false);
            
            return response()->json([
                'success' => false,
                'message' => '数据源连接测试失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 更新数据源状态
     */
    public function updateStatus(Request $request, DataSource $dataSource): JsonResponse
    {
        $validated = $request->validate([
            'status' => 'required|integer|in:0,1',
        ]);
        
        $dataSource->update(['status' => $validated['status']]);
        
        $statusText = $validated['status'] == 1 ? '启用' : '禁用';
        
        return response()->json([
            'success' => true,
            'data' => $dataSource,
            'message' => "数据源{$statusText}成功"
        ]);
    }
    
    /**
     * 获取数据源统计信息
     */
    public function statistics(DataSource $dataSource): JsonResponse
    {
        $stats = [
            'total_requests' => $dataSource->total_requests,
            'success_requests' => $dataSource->success_requests,
            'failed_requests' => $dataSource->total_requests - $dataSource->success_requests,
            'success_rate' => $dataSource->success_rate,
            'last_used_at' => $dataSource->last_used_at,
            'monitoring_tasks_count' => $dataSource->monitoringTasks()->count(),
            'field_mappings_count' => $dataSource->fieldMappings()->count(),
        ];
        
        return response()->json([
            'success' => true,
            'data' => $stats,
            'message' => '数据源统计信息获取成功'
        ]);
    }
    
    /**
     * 复制数据源
     */
    public function duplicate(DataSource $dataSource): JsonResponse
    {
        $newDataSource = $dataSource->replicate();
        $newDataSource->name = $dataSource->name . '_副本_' . time();
        $newDataSource->owner_id = Auth::id();
        $newDataSource->total_requests = 0;
        $newDataSource->success_requests = 0;
        $newDataSource->last_used_at = null;
        $newDataSource->save();
        
        // 复制字段映射
        foreach ($dataSource->fieldMappings as $fieldMapping) {
            $newFieldMapping = $fieldMapping->replicate();
            $newFieldMapping->data_source_id = $newDataSource->id;
            $newFieldMapping->save();
        }
        
        $newDataSource->load('owner', 'fieldMappings');
        
        return response()->json([
            'success' => true,
            'data' => $newDataSource,
            'message' => '数据源复制成功'
        ]);
    }

    /**
     * 获取所有数据源的简要列表
     */
    public function all(): JsonResponse
    {
        $dataSources = DataSource::where('status', 1)
            ->select('id', 'name', 'type')
            ->orderBy('name')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $dataSources,
            'message' => '所有数据源列表获取成功'
        ]);
    }
} 