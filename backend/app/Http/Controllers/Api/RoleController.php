<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Role;
use App\Models\Permission;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;

class RoleController extends Controller
{
    /**
     * 获取角色列表
     */
    public function index(Request $request): JsonResponse
    {
        $query = Role::with('permissions');
        
        // 支持搜索
        if ($request->has('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('display_name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }
        
        // 支持状态筛选
        if ($request->has('status')) {
            $query->where('status', $request->get('status'));
        }
        
        // 排序
        $query->orderBy('sort_order')->orderBy('id');
        
        // 分页
        $perPage = $request->get('per_page', 15);
        $roles = $query->paginate($perPage);
        
        return response()->json([
            'success' => true,
            'data' => $roles,
            'message' => '角色列表获取成功'
        ]);
    }
    
    /**
     * 获取单个角色详情
     */
    public function show(Role $role): JsonResponse
    {
        $role->load('permissions', 'users');
        
        return response()->json([
            'success' => true,
            'data' => $role,
            'message' => '角色详情获取成功'
        ]);
    }
    
    /**
     * 创建新角色
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:50|unique:roles,name',
            'display_name' => 'required|string|max:100',
            'description' => 'nullable|string|max:500',
            'status' => 'integer|in:0,1',
            'sort_order' => 'integer|min:0',
            'permissions' => 'array',
            'permissions.*' => 'integer|exists:permissions,id',
        ]);
        
        $role = Role::create([
            'name' => $validated['name'],
            'display_name' => $validated['display_name'],
            'description' => $validated['description'] ?? '',
            'status' => $validated['status'] ?? 1,
            'sort_order' => $validated['sort_order'] ?? 0,
        ]);
        
        // 分配权限
        if (isset($validated['permissions'])) {
            $role->permissions()->sync($validated['permissions']);
        }
        
        $role->load('permissions');
        
        return response()->json([
            'success' => true,
            'data' => $role,
            'message' => '角色创建成功'
        ], 201);
    }
    
    /**
     * 更新角色
     */
    public function update(Request $request, Role $role): JsonResponse
    {
        $validated = $request->validate([
            'name' => [
                'required',
                'string',
                'max:50',
                Rule::unique('roles', 'name')->ignore($role->id),
            ],
            'display_name' => 'required|string|max:100',
            'description' => 'nullable|string|max:500',
            'status' => 'integer|in:0,1',
            'sort_order' => 'integer|min:0',
            'permissions' => 'array',
            'permissions.*' => 'integer|exists:permissions,id',
        ]);
        
        $role->update([
            'name' => $validated['name'],
            'display_name' => $validated['display_name'],
            'description' => $validated['description'] ?? '',
            'status' => $validated['status'] ?? $role->status,
            'sort_order' => $validated['sort_order'] ?? $role->sort_order,
        ]);
        
        // 同步权限
        if (isset($validated['permissions'])) {
            $role->permissions()->sync($validated['permissions']);
        }
        
        $role->load('permissions');
        
        return response()->json([
            'success' => true,
            'data' => $role,
            'message' => '角色更新成功'
        ]);
    }
    
    /**
     * 删除角色
     */
    public function destroy(Role $role): JsonResponse
    {
        // 检查是否有用户使用此角色
        if ($role->users()->count() > 0) {
            return response()->json([
                'success' => false,
                'message' => '该角色正在被用户使用，无法删除'
            ], 400);
        }
        
        // 移除所有权限关联
        $role->permissions()->detach();
        
        // 删除角色
        $role->delete();
        
        return response()->json([
            'success' => true,
            'message' => '角色删除成功'
        ]);
    }
    
    /**
     * 批量删除角色
     */
    public function batchDestroy(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'integer|exists:roles,id',
        ]);
        
        $roles = Role::whereIn('id', $validated['ids'])->get();
        $deletedCount = 0;
        $errors = [];
        
        foreach ($roles as $role) {
            if ($role->users()->count() > 0) {
                $errors[] = "角色 '{$role->display_name}' 正在被用户使用，无法删除";
                continue;
            }
            
            $role->permissions()->detach();
            $role->delete();
            $deletedCount++;
        }
        
        return response()->json([
            'success' => true,
            'data' => [
                'deleted_count' => $deletedCount,
                'errors' => $errors
            ],
            'message' => "成功删除 {$deletedCount} 个角色"
        ]);
    }
    
    /**
     * 获取角色的权限列表
     */
    public function permissions(Role $role): JsonResponse
    {
        $permissions = $role->permissions()->get();
        
        return response()->json([
            'success' => true,
            'data' => $permissions,
            'message' => '角色权限列表获取成功'
        ]);
    }
    
    /**
     * 为角色分配权限
     */
    public function assignPermissions(Request $request, Role $role): JsonResponse
    {
        $validated = $request->validate([
            'permissions' => 'required|array',
            'permissions.*' => 'integer|exists:permissions,id',
        ]);
        
        $role->permissions()->sync($validated['permissions']);
        $role->load('permissions');
        
        return response()->json([
            'success' => true,
            'data' => $role,
            'message' => '权限分配成功'
        ]);
    }
    
    /**
     * 移除角色的权限
     */
    public function removePermissions(Request $request, Role $role): JsonResponse
    {
        $validated = $request->validate([
            'permissions' => 'required|array',
            'permissions.*' => 'integer|exists:permissions,id',
        ]);
        
        $role->permissions()->detach($validated['permissions']);
        $role->load('permissions');
        
        return response()->json([
            'success' => true,
            'data' => $role,
            'message' => '权限移除成功'
        ]);
    }
} 