<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class NotificationController extends Controller
{
    /**
     * 获取用户通知列表
     */
    public function index(Request $request): JsonResponse
    {
        $user = Auth::user();
        
        $query = $user->notifications();
        
        // 支持按类型筛选
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }
        
        // 支持按已读状态筛选
        if ($request->filled('read_status')) {
            if ($request->read_status === 'unread') {
                $query->whereNull('read_at');
            } elseif ($request->read_status === 'read') {
                $query->whereNotNull('read_at');
            }
        }
        
        // 排序
        $query->orderBy('created_at', 'desc');
        
        // 分页
        $perPage = $request->get('per_page', 15);
        $notifications = $query->paginate($perPage);
        
        // 转换通知数据格式
        $notifications->getCollection()->transform(function ($notification) {
            return $this->transformNotification($notification);
        });
        
        return response()->json([
            'success' => true,
            'data' => $notifications,
            'message' => '通知列表获取成功'
        ]);
    }
    
    /**
     * 获取单个通知详情
     */
    public function show(string $id): JsonResponse
    {
        $user = Auth::user();
        $notification = $user->notifications()->findOrFail($id);
        
        return response()->json([
            'success' => true,
            'data' => $this->transformNotification($notification),
            'message' => '通知详情获取成功'
        ]);
    }
    
    /**
     * 标记通知为已读
     */
    public function markAsRead(string $id): JsonResponse
    {
        $user = Auth::user();
        $notification = $user->notifications()->findOrFail($id);
        
        $notification->markAsRead();
        
        return response()->json([
            'success' => true,
            'message' => '已标记为已读'
        ]);
    }
    
    /**
     * 标记所有通知为已读
     */
    public function markAllAsRead(): JsonResponse
    {
        $user = Auth::user();
        $user->unreadNotifications->markAsRead();
        
        return response()->json([
            'success' => true,
            'message' => '所有通知已标记为已读'
        ]);
    }
    
    /**
     * 删除通知
     */
    public function destroy(string $id): JsonResponse
    {
        $user = Auth::user();
        $notification = $user->notifications()->findOrFail($id);
        
        $notification->delete();
        
        return response()->json([
            'success' => true,
            'message' => '通知删除成功'
        ]);
    }
    
    /**
     * 批量删除通知
     */
    public function batchDestroy(Request $request): JsonResponse
    {
        $request->validate([
            'ids' => 'required|array|min:1',
            'ids.*' => 'string'
        ]);
        
        $user = Auth::user();
        $deletedCount = $user->notifications()
            ->whereIn('id', $request->ids)
            ->delete();
        
        return response()->json([
            'success' => true,
            'data' => ['deleted_count' => $deletedCount],
            'message' => "成功删除 {$deletedCount} 条通知"
        ]);
    }
    
    /**
     * 清空所有通知
     */
    public function clear(): JsonResponse
    {
        $user = Auth::user();
        $deletedCount = $user->notifications()->delete();
        
        return response()->json([
            'success' => true,
            'data' => ['deleted_count' => $deletedCount],
            'message' => "成功清空 {$deletedCount} 条通知"
        ]);
    }
    
    /**
     * 获取通知统计信息
     */
    public function statistics(): JsonResponse
    {
        $user = Auth::user();
        
        $stats = [
            'total_count' => $user->notifications()->count(),
            'unread_count' => $user->unreadNotifications()->count(),
            'read_count' => $user->readNotifications()->count(),
            'today_count' => $user->notifications()
                ->whereDate('created_at', today())
                ->count(),
            'this_week_count' => $user->notifications()
                ->whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()])
                ->count(),
        ];
        
        return response()->json([
            'success' => true,
            'data' => $stats,
            'message' => '通知统计获取成功'
        ]);
    }
    
    /**
     * 转换通知数据格式
     */
    private function transformNotification($notification): array
    {
        $data = $notification->data;
        
        // 根据通知类型确定图标和标题
        $type = $this->getNotificationType($notification->type);
        $title = $data['title'] ?? $this->getDefaultTitle($notification->type);
        $content = $data['message'] ?? '';
        
        return [
            'id' => $notification->id,
            'type' => $type,
            'title' => $title,
            'content' => $content,
            'isRead' => $notification->read_at !== null,
            'createdAt' => $notification->created_at,
            'readAt' => $notification->read_at,
            'data' => $data,
        ];
    }
    
    /**
     * 获取通知类型
     */
    private function getNotificationType(string $notificationClass): string
    {
        if (str_contains($notificationClass, 'Alert')) {
            return 'alert';
        } elseif (str_contains($notificationClass, 'Task')) {
            return 'task';
        } elseif (str_contains($notificationClass, 'System')) {
            return 'system';
        }
        
        return 'message';
    }
    
    /**
     * 获取默认标题
     */
    private function getDefaultTitle(string $notificationClass): string
    {
        if (str_contains($notificationClass, 'AlertTriggered')) {
            return '预警通知';
        } elseif (str_contains($notificationClass, 'TaskGroupAlert')) {
            return '任务分组预警汇总';
        }
        
        return '系统通知';
    }
}
