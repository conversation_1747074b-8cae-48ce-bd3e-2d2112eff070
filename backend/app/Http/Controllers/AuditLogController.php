<?php

namespace App\Http\Controllers;

use App\Services\AuditLogService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;

class AuditLogController extends Controller
{
    protected AuditLogService $auditLogService;

    public function __construct(AuditLogService $auditLogService)
    {
        $this->auditLogService = $auditLogService;
    }

    /**
     * 获取审计日志列表
     */
    public function index(Request $request): JsonResponse
    {
        $request->validate([
            'page' => 'integer|min:1',
            'per_page' => 'integer|min:1|max:100',
            'user_id' => 'integer|exists:users,id',
            'action' => 'string|max:50',
            'level' => Rule::in(['info', 'warning', 'error']),
            'auditable_type' => 'string|max:100',
            'date_from' => 'date',
            'date_to' => 'date|after_or_equal:date_from',
            'ip_address' => 'string|max:45',
            'search' => 'string|max:255',
        ]);

        $filters = $request->only([
            'user_id', 'action', 'level', 'auditable_type',
            'date_from', 'date_to', 'ip_address', 'search'
        ]);

        $perPage = $request->get('per_page', 15);

        $logs = $this->auditLogService->getAuditLogs($filters, $perPage);

        return response()->json([
            'success' => true,
            'data' => $logs,
        ]);
    }

    /**
     * 获取单个审计日志详情
     */
    public function show(int $id): JsonResponse
    {
        $log = $this->auditLogService->getAuditLogById($id);

        if (!$log) {
            return response()->json([
                'success' => false,
                'message' => '审计日志不存在',
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $log,
        ]);
    }

    /**
     * 获取审计日志统计信息
     */
    public function statistics(Request $request): JsonResponse
    {
        $request->validate([
            'date_from' => 'date',
            'date_to' => 'date|after_or_equal:date_from',
            'group_by' => Rule::in(['day', 'week', 'month']),
        ]);

        $dateFrom = $request->get('date_from');
        $dateTo = $request->get('date_to');
        $groupBy = $request->get('group_by', 'day');

        $statistics = $this->auditLogService->getAuditLogStatistics(
            $dateFrom,
            $dateTo,
            $groupBy
        );

        return response()->json([
            'success' => true,
            'data' => $statistics,
        ]);
    }

    /**
     * 获取用户操作统计
     */
    public function userStatistics(Request $request): JsonResponse
    {
        $request->validate([
            'date_from' => 'date',
            'date_to' => 'date|after_or_equal:date_from',
            'limit' => 'integer|min:1|max:100',
        ]);

        $dateFrom = $request->get('date_from');
        $dateTo = $request->get('date_to');
        $limit = $request->get('limit', 10);

        $statistics = $this->auditLogService->getUserOperationStatistics(
            $dateFrom,
            $dateTo,
            $limit
        );

        return response()->json([
            'success' => true,
            'data' => $statistics,
        ]);
    }

    /**
     * 获取操作类型统计
     */
    public function actionStatistics(Request $request): JsonResponse
    {
        $request->validate([
            'date_from' => 'date',
            'date_to' => 'date|after_or_equal:date_from',
        ]);

        $dateFrom = $request->get('date_from');
        $dateTo = $request->get('date_to');

        $statistics = $this->auditLogService->getActionStatistics(
            $dateFrom,
            $dateTo
        );

        return response()->json([
            'success' => true,
            'data' => $statistics,
        ]);
    }

    /**
     * 清理过期日志
     */
    public function cleanup(Request $request): JsonResponse
    {
        $request->validate([
            'days' => 'integer|min:1|max:3650', // 最多保留10年
        ]);

        $days = $request->get('days', 90); // 默认清理90天前的日志

        $deletedCount = $this->auditLogService->cleanupOldLogs($days);

        return response()->json([
            'success' => true,
            'message' => "已清理 {$deletedCount} 条过期日志",
            'data' => [
                'deleted_count' => $deletedCount,
                'days' => $days,
            ],
        ]);
    }

    /**
     * 导出审计日志
     */
    public function export(Request $request): JsonResponse
    {
        $request->validate([
            'format' => Rule::in(['csv', 'excel']),
            'user_id' => 'integer|exists:users,id',
            'action' => 'string|max:50',
            'level' => Rule::in(['info', 'warning', 'error']),
            'date_from' => 'date',
            'date_to' => 'date|after_or_equal:date_from',
        ]);

        $format = $request->get('format', 'csv');
        $filters = $request->only([
            'user_id', 'action', 'level', 
            'date_from', 'date_to'
        ]);

        try {
            $exportPath = $this->auditLogService->exportAuditLogs($filters, $format);

            return response()->json([
                'success' => true,
                'message' => '导出成功',
                'data' => [
                    'download_url' => url('storage/' . $exportPath),
                    'format' => $format,
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '导出失败：' . $e->getMessage(),
            ], 500);
        }
    }
} 