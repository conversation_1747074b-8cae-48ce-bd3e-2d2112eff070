<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use App\Models\MonitoringTask;

class StoreMonitoringTaskRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // The route middleware 'permission:monitoring_task.create' already handles authorization.
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:65535',
            'data_source_id' => 'required|integer|exists:data_sources,id',
            'task_group_id' => 'nullable|integer|exists:task_groups,id',
            'target_products' => 'required|array|min:1',
            'target_products.*.product_id' => 'required|string|max:255',
            'target_products.*.official_guide_price' => 'nullable|numeric|min:0',
            'target_products.*.remark' => 'nullable|string|max:500',
            'monitor_fields' => 'nullable|array',
            'frequency_type' => ['required', 'string', Rule::in(array_keys(MonitoringTask::getFrequencyTypes()))],
            'frequency_value' => 'nullable|integer|min:1',
            'cron_expression' => 'nullable|string|max:255',
            'schedule_type' => 'nullable|string|in:daily,specific_date,weekly',
            'execution_time' => 'nullable|date_format:H:i',
            'execution_dates' => 'nullable|array',
            'execution_dates.*' => 'date_format:Y-m-d',
            'execution_weekdays' => 'nullable|array',
            'execution_weekdays.*' => 'integer|between:0,6',
            'alert_rule_ids' => 'nullable|array',
            'alert_rule_ids.*' => 'integer|exists:alert_rules,id',
            'auto_start' => 'sometimes|boolean',
            'notify_on_error' => 'sometimes|boolean',
            'notification_config' => 'nullable|array',
        ];
    }

    /**
     * 自定义验证错误消息
     */
    public function messages(): array
    {
        return [
            'target_products.required' => '请至少添加一个监控商品',
            'target_products.min' => '请至少添加一个监控商品',
            'target_products.*.product_id.required' => '商品ID不能为空',
            'target_products.*.official_guide_price.numeric' => '官方指导价必须为数字',
            'target_products.*.official_guide_price.min' => '官方指导价不能为负数',
            'alert_rule_ids.*.exists' => '选择的预警规则不存在',
            'execution_time.date_format' => '执行时间格式不正确',
            'execution_dates.*.date_format' => '执行日期格式不正确',
            'execution_weekdays.*.between' => '执行星期必须在0-6之间',
        ];
    }
}
