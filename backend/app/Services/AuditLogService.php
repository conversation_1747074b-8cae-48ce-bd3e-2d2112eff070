<?php

namespace App\Services;

use App\Models\AuditLog;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use App\Models\User;

class AuditLogService
{
    /**
     * 记录模型操作日志
     *
     * @param string $action 操作类型
     * @param Model $model 操作的模型
     * @param array $oldValues 操作前的值
     * @param array $newValues 操作后的值
     * @param string $level 日志级别
     * @param string|null $description 操作描述
     * @param array $details 额外详情
     * @return AuditLog|null
     */
    public function logModelAction(
        string $action,
        Model $model,
        array $oldValues = [],
        array $newValues = [],
        string $level = AuditLog::LEVEL_INFO,
        ?string $description = null,
        array $details = []
    ): ?AuditLog {
        try {
            $user = Auth::user();
            $request = request();

            return AuditLog::create([
                'user_id' => $user?->id,
                'username' => $user?->name ?? $user?->username ?? 'System',
                'action' => $action,
                'module' => class_basename($model),
                'controller' => null,
                'method' => $request?->method(),
                'description' => $description ?? $this->generateDescription($action, $model),
                'request_method' => $request?->method() ?? 'CLI',
                'request_url' => $request?->fullUrl() ?? 'N/A',
                'request_params' => $request ? $request->all() : [],
                'request_headers' => $request ? json_encode($request->headers->all()) : null,
                'user_agent' => $request?->userAgent(),
                'ip_address' => $this->getClientIp($request) ?? '127.0.0.1',
                'session_id' => $request && $request->hasSession() ? $request->session()->getId() : null,
                'auditable_type' => get_class($model),
                'auditable_id' => $model->getKey(),
                'old_values' => $this->sanitizeValues($oldValues),
                'new_values' => $this->sanitizeValues($newValues),
                'changed_fields' => count($newValues) > 0 && count($oldValues) > 0 ? array_keys(array_diff_assoc($newValues, $oldValues)) : null,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to create audit log', [
                'action' => $action,
                'model' => get_class($model),
                'model_id' => $model->getKey(),
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 记录用户认证日志
     *
     * @param string $action 操作类型 (login, logout)
     * @param int|null $userId 用户ID
     * @param array $details 额外详情
     * @param string $level 日志级别
     * @param string|null $description 操作描述
     * @return AuditLog|null
     */
    public function logAuthAction(
        string $action,
        ?int $userId = null,
        array $details = [],
        string $level = AuditLog::LEVEL_INFO,
        ?string $description = null
    ): ?AuditLog {
        try {
            $request = request();
            $userName = $details['username'] ?? $details['user_email'] ?? 'Unknown';

            return AuditLog::create([
                'user_id' => $userId,
                'username' => $userName,
                'action' => $action,
                'module' => 'Auth',
                'controller' => null,
                'method' => $request?->method(),
                'description' => $description ?? $this->generateAuthDescription($action, $userName),
                'request_method' => $request?->method() ?? 'CLI',
                'request_url' => $request?->fullUrl() ?? 'N/A',
                'request_params' => $request ? $request->all() : [],
                'request_headers' => $request ? json_encode($request->headers->all()) : null,
                'user_agent' => $request?->userAgent(),
                'ip_address' => $this->getClientIp($request) ?? '127.0.0.1',
                'session_id' => $request && $request->hasSession() ? $request->session()->getId() : null,
                'auditable_type' => null,
                'auditable_id' => null,
                'old_values' => null,
                'new_values' => $details,
                'changed_fields' => null,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to create auth audit log', [
                'action' => $action,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 记录系统操作日志
     *
     * @param string $action 操作类型
     * @param string $description 操作描述
     * @param string $level 日志级别
     * @param array $details 额外详情
     * @return AuditLog|null
     */
    public function logSystemAction(
        string $action,
        string $description,
        string $level = AuditLog::LEVEL_INFO,
        array $details = []
    ): ?AuditLog {
        try {
            $user = Auth::user();
            $request = request();

            return AuditLog::create([
                'user_id' => $user?->id,
                'username' => $user?->name ?? $user?->username ?? 'System',
                'action' => $action,
                'module' => 'System',
                'controller' => null,
                'method' => $request?->method(),
                'description' => $description,
                'request_method' => $request?->method() ?? 'CLI',
                'request_url' => $request?->fullUrl() ?? 'N/A',
                'request_params' => $request ? $request->all() : [],
                'request_headers' => $request ? json_encode($request->headers->all()) : null,
                'user_agent' => $request?->userAgent(),
                'ip_address' => $this->getClientIp($request) ?? '127.0.0.1',
                'session_id' => $request && $request->hasSession() ? $request->session()->getId() : null,
                'auditable_type' => null,
                'auditable_id' => null,
                'old_values' => null,
                'new_values' => $details,
                'changed_fields' => null,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to create system audit log', [
                'action' => $action,
                'description' => $description,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 批量记录操作日志
     *
     * @param array $logs 日志数据数组
     * @return int 成功记录的日志数量
     */
    public function logBatch(array $logs): int
    {
        $successCount = 0;
        
        foreach ($logs as $logData) {
            try {
                AuditLog::create($logData);
                $successCount++;
            } catch (\Exception $e) {
                Log::error('Failed to create batch audit log', [
                    'log_data' => $logData,
                    'error' => $e->getMessage()
                ]);
            }
        }

        return $successCount;
    }

    /**
     * 获取审计日志列表
     *
     * @param array $filters 过滤条件
     * @param int $perPage 每页数量
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator
     */
    public function getAuditLogs(array $filters = [], int $perPage = 15)
    {
        $query = AuditLog::with('user')->recent();

        // 应用过滤条件
        if (!empty($filters['user_id'])) {
            $query->byUser($filters['user_id']);
        }

        if (!empty($filters['action'])) {
            $query->byAction($filters['action']);
        }

        if (!empty($filters['model_type'])) {
            $query->byModelType($filters['model_type']);
        }

        if (!empty($filters['level'])) {
            $query->byLevel($filters['level']);
        }

        if (!empty($filters['start_date']) && !empty($filters['end_date'])) {
            $query->byDateRange($filters['start_date'], $filters['end_date']);
        }

        if (!empty($filters['search'])) {
            $query->where(function ($q) use ($filters) {
                $q->where('description', 'like', '%' . $filters['search'] . '%')
                  ->orWhere('username', 'like', '%' . $filters['search'] . '%')
                  ->orWhere('action', 'like', '%' . $filters['search'] . '%');
            });
        }

        return $query->paginate($perPage);
    }

    /**
     * 获取审计日志统计信息
     *
     * @param string|null $dateFrom
     * @param string|null $dateTo
     * @param string $groupBy
     * @return array
     */
    public function getAuditLogStatistics(?string $dateFrom = null, ?string $dateTo = null, string $groupBy = 'day'): array
    {
        $query = AuditLog::query();

        // 应用时间过滤条件
        if ($dateFrom) {
            $query->where('created_at', '>=', $dateFrom);
        }

        if ($dateTo) {
            $query->where('created_at', '<=', $dateTo . ' 23:59:59');
        }

        // 如果没有指定时间范围，默认统计最近30天
        if (!$dateFrom && !$dateTo) {
            $query->where('created_at', '>=', Carbon::now()->subDays(30)->startOfDay());
        }

        $baseQuery = clone $query;

        // 根据groupBy参数生成时间分组格式
        $dateFormat = match($groupBy) {
            'week' => 'YEARWEEK(created_at)',
            'month' => 'DATE_FORMAT(created_at, "%Y-%m")',
            default => 'DATE(created_at)',
        };

        return [
            'total_logs' => $baseQuery->count(),
            'by_action' => $query->selectRaw('action, COUNT(*) as count')
                ->groupBy('action')
                ->orderBy('count', 'desc')
                ->pluck('count', 'action')
                ->toArray(),
            'by_level' => $query->selectRaw('level, COUNT(*) as count')
                ->groupBy('level')
                ->orderBy('count', 'desc')
                ->pluck('count', 'level')
                ->toArray(),
            'by_user' => $query->selectRaw('username, COUNT(*) as count')
                ->whereNotNull('username')
                ->groupBy('username')
                ->orderBy('count', 'desc')
                ->limit(10)
                ->pluck('count', 'username')
                ->toArray(),
            'by_model' => $query->selectRaw('model_type, COUNT(*) as count')
                ->whereNotNull('model_type')
                ->groupBy('model_type')
                ->orderBy('count', 'desc')
                ->pluck('count', 'model_type')
                ->toArray(),
            'time_series' => $query->selectRaw($dateFormat . ' as period, COUNT(*) as count')
                ->groupBy('period')
                ->orderBy('period', 'desc')
                ->limit(30)
                ->pluck('count', 'period')
                ->toArray(),
        ];
    }

    /**
     * 清理过期的审计日志
     *
     * @param int $days 保留天数
     * @return int 删除的日志数量
     */
    public function cleanupOldLogs(int $days = 90): int
    {
        try {
            $cutoffDate = Carbon::now()->subDays($days);
            $deletedCount = AuditLog::where('created_at', '<', $cutoffDate)->delete();
            
            Log::info('Audit logs cleanup completed', [
                'deleted_count' => $deletedCount,
                'cutoff_date' => $cutoffDate->toDateTimeString()
            ]);

            return $deletedCount;
        } catch (\Exception $e) {
            Log::error('Failed to cleanup audit logs', [
                'days' => $days,
                'error' => $e->getMessage()
            ]);
            return 0;
        }
    }

    /**
     * 获取客户端IP地址
     *
     * @param Request|null $request
     * @return string|null
     */
    private function getClientIp(?Request $request): ?string
    {
        if (!$request) {
            return null;
        }

        return $request->ip();
    }

    /**
     * 清理敏感数据
     *
     * @param array $values
     * @return array
     */
    private function sanitizeValues(array $values): array
    {
        $sensitiveFields = ['password', 'token', 'secret', 'key', 'api_key'];
        
        foreach ($values as $key => $value) {
            if (in_array(strtolower($key), $sensitiveFields)) {
                $values[$key] = '[HIDDEN]';
            }
        }

        return $values;
    }

    /**
     * 生成操作描述
     *
     * @param string $action
     * @param Model $model
     * @return string
     */
    private function generateDescription(string $action, Model $model): string
    {
        $modelName = class_basename($model);
        $actionName = AuditLog::getActions()[$action] ?? $action;
        
        return "{$actionName}{$modelName} (ID: {$model->getKey()})";
    }

    /**
     * 生成认证操作描述
     *
     * @param string $action
     * @param string|null $userName
     * @return string
     */
    private function generateAuthDescription(string $action, ?string $userName): string
    {
        $actionName = AuditLog::getActions()[$action] ?? $action;
        return "用户 {$userName} {$actionName}";
    }

    /**
     * 根据ID获取审计日志
     *
     * @param int $id
     * @return AuditLog|null
     */
    public function getAuditLogById(int $id): ?AuditLog
    {
        return AuditLog::with('user')->find($id);
    }

    /**
     * 获取用户操作统计
     *
     * @param string|null $dateFrom
     * @param string|null $dateTo
     * @param int $limit
     * @return array
     */
    public function getUserOperationStatistics(?string $dateFrom = null, ?string $dateTo = null, int $limit = 10): array
    {
        $query = AuditLog::query()
            ->select('user_id', \DB::raw('COUNT(*) as operation_count'))
            ->with('user:id,name,email')
            ->whereNotNull('user_id')
            ->groupBy('user_id')
            ->orderBy('operation_count', 'desc')
            ->limit($limit);

        if ($dateFrom) {
            $query->where('created_at', '>=', $dateFrom);
        }

        if ($dateTo) {
            $query->where('created_at', '<=', $dateTo . ' 23:59:59');
        }

        return $query->get()->map(function ($item) {
            return [
                'user_id' => $item->user_id,
                'user_name' => $item->user->name ?? 'Unknown',
                'user_email' => $item->user->email ?? 'Unknown',
                'operation_count' => $item->operation_count,
            ];
        })->toArray();
    }

    /**
     * 获取操作类型统计
     *
     * @param string|null $dateFrom
     * @param string|null $dateTo
     * @return array
     */
    public function getActionStatistics(?string $dateFrom = null, ?string $dateTo = null): array
    {
        $query = AuditLog::query()
            ->select('action', \DB::raw('COUNT(*) as count'))
            ->groupBy('action')
            ->orderBy('count', 'desc');

        if ($dateFrom) {
            $query->where('created_at', '>=', $dateFrom);
        }

        if ($dateTo) {
            $query->where('created_at', '<=', $dateTo . ' 23:59:59');
        }

        return $query->get()->map(function ($item) {
            return [
                'action' => $item->action,
                'action_name' => $this->getActionName($item->action),
                'count' => $item->count,
            ];
        })->toArray();
    }

    /**
     * 导出审计日志
     *
     * @param array $filters
     * @param string $format
     * @return string 导出文件路径
     */
    public function exportAuditLogs(array $filters = [], string $format = 'csv'): string
    {
        $query = AuditLog::query()->with('user:id,name,email');

        // 应用过滤条件
        if (!empty($filters['user_id'])) {
            $query->where('user_id', $filters['user_id']);
        }

        if (!empty($filters['action'])) {
            $query->where('action', $filters['action']);
        }

        if (!empty($filters['level'])) {
            $query->where('level', $filters['level']);
        }

        if (!empty($filters['date_from'])) {
            $query->where('created_at', '>=', $filters['date_from']);
        }

        if (!empty($filters['date_to'])) {
            $query->where('created_at', '<=', $filters['date_to'] . ' 23:59:59');
        }

        $logs = $query->orderBy('created_at', 'desc')->get();

        // 准备导出数据
        $exportData = [];
        $exportData[] = [
            'ID', '用户', '操作', '级别', '模型类型', '模型ID', 
            'IP地址', '用户代理', '创建时间'
        ];

        foreach ($logs as $log) {
            $exportData[] = [
                $log->id,
                $log->user ? $log->user->name : '系统',
                $this->getActionName($log->action),
                $this->getLevelName($log->level),
                $log->model_type,
                $log->model_id,
                $log->ip_address,
                $log->user_agent,
                $log->created_at->format('Y-m-d H:i:s'),
            ];
        }

        // 生成文件
        $filename = 'audit_logs_' . date('Y-m-d_H-i-s') . '.' . $format;
        $filepath = 'exports/' . $filename;
        $fullPath = storage_path('app/public/' . $filepath);

        // 确保目录存在
        $directory = dirname($fullPath);
        if (!file_exists($directory)) {
            mkdir($directory, 0755, true);
        }

        if ($format === 'csv') {
            $file = fopen($fullPath, 'w');
            fprintf($file, chr(0xEF).chr(0xBB).chr(0xBF)); // UTF-8 BOM
            
            foreach ($exportData as $row) {
                fputcsv($file, $row);
            }
            
            fclose($file);
        } else {
            // Excel格式需要额外的库支持，这里简化为CSV
            throw new \Exception('Excel格式暂不支持，请使用CSV格式');
        }

        return $filepath;
    }

    /**
     * 获取操作名称
     *
     * @param string $action
     * @return string
     */
    private function getActionName(string $action): string
    {
        $actionNames = [
            AuditLog::ACTION_CREATE => '创建',
            AuditLog::ACTION_UPDATE => '更新',
            AuditLog::ACTION_DELETE => '删除',
            AuditLog::ACTION_LOGIN => '登录',
            AuditLog::ACTION_LOGOUT => '登出',
            AuditLog::ACTION_LOGIN_FAILED => '登录失败',
            AuditLog::ACTION_LOGIN_ATTEMPT => '登录尝试',
            AuditLog::ACTION_REGISTER => '注册',
            AuditLog::ACTION_PASSWORD_RESET => '密码重置',
            AuditLog::ACTION_EMAIL_VERIFIED => '邮箱验证',
            AuditLog::ACTION_EXPORT => '导出',
            AuditLog::ACTION_IMPORT => '导入',
            AuditLog::ACTION_BULK_DELETE => '批量删除',
            AuditLog::ACTION_BULK_UPDATE => '批量更新',
        ];

        return $actionNames[$action] ?? $action;
    }

    /**
     * 获取级别名称
     *
     * @param string $level
     * @return string
     */
    private function getLevelName(string $level): string
    {
        $levelNames = [
            AuditLog::LEVEL_INFO => '信息',
            AuditLog::LEVEL_WARNING => '警告',
            AuditLog::LEVEL_ERROR => '错误',
        ];

        return $levelNames[$level] ?? $level;
    }
} 