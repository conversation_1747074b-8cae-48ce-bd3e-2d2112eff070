<?php

namespace App\Services;

use App\Jobs\ProcessDataSourceTask;
use App\Models\DataSource;
use App\Models\MonitoringTask;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Queue;

class QueueProducerService
{
    /**
     * 发布数据源处理任务到队列
     *
     * @param int $dataSourceId 数据源ID
     * @param array $targetProducts 目标产品列表
     * @param array $taskParams 任务参数
     * @param int|null $monitoringTaskId 监控任务ID
     * @param string $queue 队列名称
     * @param int $delay 延迟秒数
     * @return string 任务ID
     */
    public function publishDataSourceTask(
        int $dataSourceId,
        array $targetProducts = [],
        array $taskParams = [],
        ?int $monitoringTaskId = null,
        string $queue = 'default',
        int $delay = 0
    ): string {
        // 验证数据源是否存在
        $dataSource = DataSource::find($dataSourceId);
        if (!$dataSource) {
            throw new \InvalidArgumentException("数据源不存在: {$dataSourceId}");
        }

        if (!$dataSource->isAvailable()) {
            throw new \InvalidArgumentException("数据源不可用: {$dataSource->name}");
        }

        // 验证监控任务（如果提供）
        if ($monitoringTaskId) {
            $monitoringTask = MonitoringTask::find($monitoringTaskId);
            if (!$monitoringTask) {
                throw new \InvalidArgumentException("监控任务不存在: {$monitoringTaskId}");
            }
        }

        // 创建任务实例
        $job = new ProcessDataSourceTask(
            $dataSourceId,
            $targetProducts,
            $taskParams,
            $monitoringTaskId
        );

        // 设置队列和延迟
        $job->onQueue($queue);
        if ($delay > 0) {
            $job->delay($delay);
        }

        // 发布任务
        $jobId = Queue::push($job);

        Log::info('数据源任务已发布到队列', [
            'job_id' => $jobId,
            'data_source_id' => $dataSourceId,
            'monitoring_task_id' => $monitoringTaskId,
            'queue' => $queue,
            'delay' => $delay,
            'products_count' => count($targetProducts),
        ]);

        return $jobId;
    }

    /**
     * 批量发布数据源任务
     *
     * @param array $tasks 任务列表
     * @param string $queue 队列名称
     * @return array 任务ID列表
     */
    public function batchPublishDataSourceTasks(array $tasks, string $queue = 'default'): array
    {
        $jobIds = [];

        foreach ($tasks as $task) {
            try {
                $jobId = $this->publishDataSourceTask(
                    $task['data_source_id'],
                    $task['target_products'] ?? [],
                    $task['task_params'] ?? [],
                    $task['monitoring_task_id'] ?? null,
                    $queue,
                    $task['delay'] ?? 0
                );

                $jobIds[] = $jobId;

            } catch (\Exception $e) {
                Log::error('批量发布任务失败', [
                    'task' => $task,
                    'error' => $e->getMessage(),
                ]);

                // 继续处理其他任务，不中断整个批次
                $jobIds[] = null;
            }
        }

        Log::info('批量任务发布完成', [
            'total_tasks' => count($tasks),
            'successful_tasks' => count(array_filter($jobIds)),
            'failed_tasks' => count(array_filter($jobIds, fn($id) => $id === null)),
        ]);

        return $jobIds;
    }

    /**
     * 根据监控任务自动创建队列任务
     *
     * @param int $monitoringTaskId 监控任务ID
     * @return string 任务ID
     */
    public function publishMonitoringTask(int $monitoringTaskId): string
    {
        $monitoringTask = MonitoringTask::with(['dataSource'])->find($monitoringTaskId);
        if (!$monitoringTask) {
            throw new \InvalidArgumentException("监控任务不存在: {$monitoringTaskId}");
        }

        if (!$monitoringTask->dataSource) {
            throw new \InvalidArgumentException("监控任务的数据源不存在");
        }

        // 解析目标产品
        $targetProducts = $this->parseTargetProducts($monitoringTask->target_products);

        // 解析监控字段作为任务参数
        $taskParams = $this->parseMonitorFields($monitoringTask->monitor_fields);

        return $this->publishDataSourceTask(
            $monitoringTask->data_source_id,
            $targetProducts,
            $taskParams,
            $monitoringTaskId
        );
    }

    /**
     * 获取队列状态信息
     *
     * @param string $queue 队列名称
     * @return array 队列状态
     */
    public function getQueueStatus(string $queue = 'default'): array
    {
        try {
            $connection = config('queue.default');
            
            switch ($connection) {
                case 'database':
                    return $this->getDatabaseQueueStatus($queue);
                    
                case 'redis':
                    return $this->getRedisQueueStatus($queue);
                    
                default:
                    return [
                        'connection' => $connection,
                        'queue' => $queue,
                        'status' => 'unknown',
                        'pending_jobs' => 0,
                        'failed_jobs' => 0,
                    ];
            }
            
        } catch (\Exception $e) {
            Log::error('获取队列状态失败', [
                'queue' => $queue,
                'error' => $e->getMessage(),
            ]);
            
            return [
                'connection' => config('queue.default'),
                'queue' => $queue,
                'status' => 'error',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * 解析目标产品
     */
    private function parseTargetProducts($targetProducts): array
    {
        if (is_string($targetProducts)) {
            $targetProducts = json_decode($targetProducts, true);
        }

        if (!is_array($targetProducts)) {
            return [];
        }

        return $targetProducts;
    }

    /**
     * 解析监控字段
     */
    private function parseMonitorFields($monitorFields): array
    {
        if (is_string($monitorFields)) {
            $monitorFields = json_decode($monitorFields, true);
        }

        if (!is_array($monitorFields)) {
            return [];
        }

        return $monitorFields;
    }

    /**
     * 获取数据库队列状态
     */
    private function getDatabaseQueueStatus(string $queue): array
    {
        $pendingJobs = \DB::table('jobs')
            ->where('queue', $queue)
            ->count();

        $failedJobs = \DB::table('failed_jobs')
            ->where('queue', $queue)
            ->count();

        return [
            'connection' => 'database',
            'queue' => $queue,
            'status' => 'active',
            'pending_jobs' => $pendingJobs,
            'failed_jobs' => $failedJobs,
        ];
    }

    /**
     * 获取Redis队列状态
     */
    private function getRedisQueueStatus(string $queue): array
    {
        $redis = \Redis::connection();
        $queueKey = 'queues:' . $queue;
        
        $pendingJobs = $redis->llen($queueKey);
        $failedJobs = $redis->llen('queues:' . $queue . ':failed');

        return [
            'connection' => 'redis',
            'queue' => $queue,
            'status' => 'active',
            'pending_jobs' => $pendingJobs,
            'failed_jobs' => $failedJobs,
        ];
    }
} 