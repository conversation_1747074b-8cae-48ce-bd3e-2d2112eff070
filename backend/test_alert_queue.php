<?php

require_once __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== 测试预警队列系统 ===\n";

// 获取第一个监控任务
$monitoringTask = \App\Models\MonitoringTask::first();
if (!$monitoringTask) {
    echo "未找到监控任务\n";
    exit(1);
}

echo "找到监控任务: {$monitoringTask->name} (ID: {$monitoringTask->id})\n";

// 获取目标产品ID
$targetProducts = $monitoringTask->target_products;
if (empty($targetProducts) || !is_array($targetProducts)) {
    echo "监控任务没有配置目标产品\n";
    exit(1);
}

$firstProduct = $targetProducts[0];
$targetItemId = $firstProduct['product_id'] ?? null;
if (!$targetItemId) {
    echo "无法获取目标产品ID\n";
    exit(1);
}

echo "目标产品ID: {$targetItemId}\n";

// 生成采集批次ID
$collectionBatchId = 'batch_' . date('YmdHis') . '_' . $monitoringTask->id;
echo "生成采集批次ID: {$collectionBatchId}\n";

// 获取数据采集服务
$dataCollectionService = app(\App\Services\DataCollectionService::class);

try {
    // 执行数据采集
    echo "开始执行数据采集...\n";
    $result = $dataCollectionService->collectAndStandardize(
        $monitoringTask->data_source_id,
        $targetItemId,
        [],
        $monitoringTask->id,
        $collectionBatchId
    );
    
    echo "数据采集完成！\n";
    echo "结果: " . json_encode($result, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n";

    // 为了测试预警功能，模拟一个有价格的在售商品来触发预警
    if ($result['success'] && isset($result['product_data_id'])) {
        echo "\n=== 模拟更新商品数据以触发预警 ===\n";

        $productData = \App\Models\ProductData::find($result['product_data_id']);
        if ($productData) {
            // 模拟一个在售商品，价格为150元（超过官方指导价100元的50%，应该触发20%和15%的预警阈值）
            $mockStandardizedData = [
                'product_fields' => [
                    'state' => 1, // 在售状态
                    'code' => 200, // 成功状态
                    'price' => 150.00, // 当前价格
                    'title' => '测试商品标题',
                ],
                'sku_fields' => [
                    [
                        'sku_id' => 'test_sku_001',
                        'name' => '默认规格',
                        'price' => 150.00,
                        'promotion_price' => 130.00, // 券后价
                        'stock' => 100
                    ]
                ]
            ];

            $productData->update([
                'standardized_data' => $mockStandardizedData,
                'state' => 1,
                'code' => 200,
                'price' => 150.00,
                'title' => '测试商品标题',
                'stock' => 100
            ]);

            echo "商品数据已更新为模拟的在售状态\n";
            echo "当前价格: 150元, 官方指导价: 100元, 偏离率: 50%\n";
            echo "应该触发渠道价格偏离率预警（阈值20%和15%）\n";

            // 手动触发预警检查
            echo "\n=== 手动触发预警检查 ===\n";
            \App\Jobs\ProcessAlertChecking::dispatch($result['product_data_id'], $collectionBatchId);
            echo "预警检查任务已重新分发\n";
        }
    }

} catch (\Exception $e) {
    echo "数据采集失败: " . $e->getMessage() . "\n";
    echo "错误详情: " . $e->getTraceAsString() . "\n";
}

echo "=== 测试完成 ===\n";
