<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\DataSource;

// 启动Laravel应用
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

try {
    echo "=== 修复数据源字段映射配置 ===\n\n";
    
    $dataSource = DataSource::find(3);
    
    if (!$dataSource) {
        echo "数据源ID 3 不存在\n";
        exit(1);
    }
    
    echo "当前数据源: {$dataSource->name}\n\n";
    
    // 获取当前配置
    $fieldMapping = $dataSource->field_mapping;
    
    // 修复字段映射
    echo "修复字段映射...\n";
    
    // 1. 修复SKU字段映射 - 将sub_price改为subPrice
    if (isset($fieldMapping['fields']['skus']['fields']['promotion_price'])) {
        $fieldMapping['fields']['skus']['fields']['promotion_price'] = 'subPrice';
        echo "✓ 修复SKU promotion_price字段映射: sub_price -> subPrice\n";
    }
    
    // 2. 修复主要字段中的sub_price映射
    if (isset($fieldMapping['fields']['sub_price'])) {
        $fieldMapping['fields']['sub_price'] = 'subPrice';
        echo "✓ 修复主要字段sub_price映射: sub_price -> subPrice\n";
    }
    
    // 3. 修复sub_price_title映射
    if (isset($fieldMapping['fields']['sub_price_title'])) {
        $fieldMapping['fields']['sub_price_title'] = 'subPriceTitle';
        echo "✓ 修复sub_price_title字段映射: sub_price_title -> subPriceTitle\n";
    }
    
    // 4. 添加SKU的sub_price_title映射
    if (isset($fieldMapping['fields']['skus']['fields'])) {
        $fieldMapping['fields']['skus']['fields']['promotion_price_title'] = 'subPriceTitle';
        echo "✓ 添加SKU promotion_price_title字段映射: subPriceTitle\n";
    }
    
    // 5. 修复最低/最高到手价的映射 - 这些需要从SKU数组中计算
    // 移除错误的映射，让服务层来计算
    if (isset($fieldMapping['fields']['min_hand_price'])) {
        unset($fieldMapping['fields']['min_hand_price']);
        echo "✓ 移除min_hand_price直接映射，将由服务层计算\n";
    }
    
    if (isset($fieldMapping['fields']['max_hand_price'])) {
        unset($fieldMapping['fields']['max_hand_price']);
        echo "✓ 移除max_hand_price直接映射，将由服务层计算\n";
    }
    
    // 保存更新后的配置
    $dataSource->field_mapping = $fieldMapping;
    $dataSource->save();
    
    echo "\n✅ 字段映射配置已更新！\n\n";
    
    // 显示更新后的SKU字段映射
    echo "更新后的SKU字段映射:\n";
    if (isset($fieldMapping['fields']['skus']['fields'])) {
        foreach ($fieldMapping['fields']['skus']['fields'] as $key => $value) {
            echo "  {$key} => {$value}\n";
        }
    }
    
} catch (\Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . "\n";
    echo "行号: " . $e->getLine() . "\n";
}
