<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\RoleController;
use App\Http\Controllers\Api\PermissionController;
use App\Http\Controllers\Api\UserController;
use App\Http\Controllers\Api\DataSourceController;
use App\Http\Controllers\Api\MonitoringTaskController;
use App\Http\Controllers\Api\TaskGroupController;
use App\Http\Controllers\Api\AlertRuleController;
use App\Http\Controllers\Api\ProductDataController;
use App\Http\Controllers\Api\ProductDataHistoryController;
use App\Http\Controllers\Api\AnalyticsController;
use App\Http\Controllers\AuditLogController;
use App\Http\Controllers\Api\AlertsController;
use App\Http\Controllers\Api\NotificationController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// 公开的认证路由（不需要认证）
Route::prefix('auth')->group(function () {
    Route::post('register', [AuthController::class, 'register']);
    Route::post('login', [AuthController::class, 'login']);
});

// 临时的公开dashboard统计端点（用于测试）
Route::get('dashboard-stats', function () {
    return response()->json([
        'success' => true,
        'data' => [
            'taskGroups' => \App\Models\TaskGroup::count(),
            'monitoringProducts' => \App\Models\ProductData::distinct('item_id')->count(),
            'runningTasks' => \App\Models\MonitoringTask::where('status', 'running')->count(),
            'alerts' => \App\Models\Alert::count(),
        ],
        'message' => '仪表盘统计数据获取成功'
    ]);
});

// 临时移到认证组外的产品数据API（用于调试）
/*
Route::prefix('product-data')->group(function () {
    Route::get('/', [ProductDataController::class, 'index']);
    Route::get('/statistics', [ProductDataController::class, 'statistics']);
    Route::post('/compare', [ProductDataController::class, 'compare']);
    Route::get('/price-history', [ProductDataController::class, 'priceHistory']);
    Route::get('/{productData}', [ProductDataController::class, 'show']);
});
*/

// 产品历史数据API路由（用于调试）
/*
Route::prefix('product-data-history')->group(function () {
    Route::get('/task/{taskId}/item/{itemId}', [ProductDataHistoryController::class, 'getProductHistory']);
    Route::get('/detail/{historyId}', [ProductDataHistoryController::class, 'getProductHistoryDetail']);
    Route::get('/task/{taskId}/item/{itemId}/at/{targetTime}', [ProductDataHistoryController::class, 'getProductHistoryByTime']);
    Route::get('/task/{taskId}/item/{itemId}/trends', [ProductDataHistoryController::class, 'getProductTrends']);
    Route::get('/task/{taskId}/item/{itemId}/summary', [ProductDataHistoryController::class, 'getProductHistorySummary']);
    Route::get('/task/{taskId}/items/latest', [ProductDataHistoryController::class, 'getLatestHistoryForAllProducts']);
});
*/

// 需要认证的路由
Route::middleware('auth:sanctum')->group(function () {
    // 认证相关
    Route::prefix('auth')->group(function () {
        Route::post('logout', [AuthController::class, 'logout']);
        Route::get('me', [AuthController::class, 'me']);
    });
    
    // 测试路由
    Route::get('test', function (Request $request) {
        return response()->json([
            'success' => true,
            'message' => '认证成功！',
            'user' => $request->user()->only(['id', 'username', 'name', 'email'])
        ]);
    });
    
    // 测试权限保护的路由
    Route::middleware('permission:user.create')->get('test-permission', function (Request $request) {
        return response()->json([
            'success' => true,
            'message' => '权限验证成功！您拥有user.create权限',
            'user' => $request->user()->only(['id', 'username', 'name', 'email'])
        ]);
    });
    
    // 角色管理路由
    Route::prefix('roles')->group(function () {
        Route::get('/', [RoleController::class, 'index'])->middleware('permission:role.read');
        Route::post('/', [RoleController::class, 'store'])->middleware('permission:role.create');
        Route::get('/{role}', [RoleController::class, 'show'])->middleware('permission:role.read');
        Route::put('/{role}', [RoleController::class, 'update'])->middleware('permission:role.update');
        Route::delete('/{role}', [RoleController::class, 'destroy'])->middleware('permission:role.delete');
        Route::delete('/', [RoleController::class, 'batchDestroy'])->middleware('permission:role.delete');
        
        // 角色权限管理
        Route::get('/{role}/permissions', [RoleController::class, 'permissions'])->middleware('permission:role.read');
        Route::put('/{role}/permissions', [RoleController::class, 'assignPermissions'])->middleware('permission:role.update');
        Route::delete('/{role}/permissions', [RoleController::class, 'removePermissions'])->middleware('permission:role.update');
    });
    
    // 权限管理路由
    Route::prefix('permissions')->group(function () {
        Route::get('/', [PermissionController::class, 'index'])->middleware('permission:permission.read');
        Route::post('/', [PermissionController::class, 'store'])->middleware('permission:permission.create');
        Route::post('/batch', [PermissionController::class, 'batchStore'])->middleware('permission:permission.create');
        Route::get('/groups', [PermissionController::class, 'groups'])->middleware('permission:permission.read');
        Route::get('/resources', [PermissionController::class, 'resources'])->middleware('permission:permission.read');
        Route::get('/{permission}', [PermissionController::class, 'show'])->middleware('permission:permission.read');
        Route::put('/{permission}', [PermissionController::class, 'update'])->middleware('permission:permission.update');
        Route::delete('/{permission}', [PermissionController::class, 'destroy'])->middleware('permission:permission.delete');
        Route::delete('/', [PermissionController::class, 'batchDestroy'])->middleware('permission:permission.delete');
        
        // 权限角色管理
        Route::get('/{permission}/roles', [PermissionController::class, 'roles'])->middleware('permission:permission.read');
    });
    
    // 用户管理路由
    Route::prefix('users')->group(function () {
        Route::get('/', [UserController::class, 'index'])->middleware('permission:user.read');
        Route::post('/', [UserController::class, 'store'])->middleware('permission:user.create');
        Route::get('/{user}', [UserController::class, 'show'])->middleware('permission:user.read');
        Route::put('/{user}', [UserController::class, 'update'])->middleware('permission:user.update');
        Route::delete('/{user}', [UserController::class, 'destroy'])->middleware('permission:user.delete');
        Route::delete('/', [UserController::class, 'batchDestroy'])->middleware('permission:user.delete');
        
        // 用户状态管理
        Route::patch('/{user}/status', [UserController::class, 'updateStatus'])->middleware('permission:user.update');
        Route::patch('/{user}/password', [UserController::class, 'resetPassword'])->middleware('permission:user.update');
        
        // 用户角色管理
        Route::get('/{user}/roles', [UserController::class, 'roles'])->middleware('permission:user.read');
        Route::put('/{user}/roles', [UserController::class, 'assignRoles'])->middleware('permission:user.update');
        Route::delete('/{user}/roles', [UserController::class, 'removeRoles'])->middleware('permission:user.update');
        
        // 用户权限查询
        Route::get('/{user}/permissions', [UserController::class, 'permissions'])->middleware('permission:user.read');
    });
    
    // 产品数据API
    Route::prefix('product-data')->group(function () {
        Route::get('/', [ProductDataController::class, 'index']);
        Route::get('/statistics', [ProductDataController::class, 'statistics']);
        Route::post('/compare', [ProductDataController::class, 'compare']);
        Route::get('/price-history', [ProductDataController::class, 'priceHistory']);
        Route::get('/{productData}', [ProductDataController::class, 'show']);
    });

    // 产品历史数据API
    Route::prefix('product-data-history')->group(function () {
        Route::get('/task/{taskId}/item/{itemId}', [ProductDataHistoryController::class, 'getProductHistory']);
        Route::get('/detail/{historyId}', [ProductDataHistoryController::class, 'getProductHistoryDetail']);
        Route::get('/task/{taskId}/item/{itemId}/at/{targetTime}', [ProductDataHistoryController::class, 'getProductHistoryByTime']);
        Route::get('/task/{taskId}/item/{itemId}/trends', [ProductDataHistoryController::class, 'getProductTrends']);
        Route::get('/task/{taskId}/item/{itemId}/summary', [ProductDataHistoryController::class, 'getProductHistorySummary']);
        Route::get('/task/{taskId}/items/latest', [ProductDataHistoryController::class, 'getLatestHistoryForAllProducts']);
    });
    
    // 数据源管理路由
    Route::prefix('data-sources')->group(function () {
        Route::get('/', [DataSourceController::class, 'index']);
        Route::post('/', [DataSourceController::class, 'store'])->middleware('permission:data_source.create');
        Route::get('/types', [DataSourceController::class, 'types']);
        Route::get('/all', [DataSourceController::class, 'all']);
        Route::get('/{dataSource}', [DataSourceController::class, 'show']);
        Route::put('/{dataSource}', [DataSourceController::class, 'update'])->middleware('permission:data_source.update');
        Route::delete('/{dataSource}', [DataSourceController::class, 'destroy'])->middleware('permission:data_source.delete');
        Route::delete('/', [DataSourceController::class, 'batchDestroy'])->middleware('permission:data_source.delete');
        
        // 数据源功能路由
        Route::post('/{dataSource}/test', [DataSourceController::class, 'testConnection']);
        Route::patch('/{dataSource}/status', [DataSourceController::class, 'updateStatus'])->middleware('permission:data_source.update');
        Route::get('/{dataSource}/statistics', [DataSourceController::class, 'statistics']);
        Route::post('/{dataSource}/duplicate', [DataSourceController::class, 'duplicate'])->middleware('permission:data_source.create');
    });
    
    // 任务组管理路由
    Route::prefix('task-groups')->group(function () {
        Route::get('/', [TaskGroupController::class, 'index']);
        Route::post('/', [TaskGroupController::class, 'store'])->middleware('permission:task_group.create');
        Route::get('/all', [TaskGroupController::class, 'all']);
        Route::get('/{taskGroup}', [TaskGroupController::class, 'show']);
        Route::put('/{taskGroup}', [TaskGroupController::class, 'update'])->middleware('permission:task_group.update');
        Route::delete('/{taskGroup}', [TaskGroupController::class, 'destroy'])->middleware('permission:task_group.delete');
        Route::delete('/', [TaskGroupController::class, 'batchDestroy'])->middleware('permission:task_group.delete');
        
        // 任务组功能路由
        Route::patch('/{taskGroup}/status', [TaskGroupController::class, 'updateStatus'])->middleware('permission:task_group.update');
        Route::put('/sort-order', [TaskGroupController::class, 'updateSortOrder'])->middleware('permission:task_group.update');
        Route::get('/{taskGroup}/statistics', [TaskGroupController::class, 'statistics']);
    });
    
    // 监控任务管理路由
    Route::prefix('monitoring-tasks')->group(function () {
        Route::get('/', [MonitoringTaskController::class, 'index']);
        Route::post('/', [MonitoringTaskController::class, 'store'])->middleware('permission:monitoring_task.create');
        Route::get('/statuses', [MonitoringTaskController::class, 'statuses']);
        Route::get('/frequency-types', [MonitoringTaskController::class, 'frequencyTypes']);
        Route::get('/all', [MonitoringTaskController::class, 'all']);
        Route::get('/{monitoringTask}', [MonitoringTaskController::class, 'show']);
        Route::put('/{monitoringTask}', [MonitoringTaskController::class, 'update'])->middleware('permission:monitoring_task.update');
        Route::delete('/{monitoringTask}', [MonitoringTaskController::class, 'destroy'])->middleware('permission:monitoring_task.delete');
        Route::delete('/', [MonitoringTaskController::class, 'batchDestroy'])->middleware('permission:monitoring_task.delete');
        
        // 监控任务功能路由
        Route::patch('/{monitoringTask}/status', [MonitoringTaskController::class, 'updateStatus'])->middleware('permission:monitoring_task.update');
        Route::get('/{monitoringTask}/statistics', [MonitoringTaskController::class, 'statistics']);
        Route::post('/{monitoringTask}/duplicate', [MonitoringTaskController::class, 'duplicate'])->middleware('permission:monitoring_task.create');

        // 立即执行任务
        Route::post('/{monitoringTask}/run-now', [MonitoringTaskController::class, 'runNow'])->middleware('permission:monitoring_task.update');
    });
    
    // 预警规则管理路由
    Route::prefix('alert-rules')->group(function () {
        // 重要：所有具体路径必须在参数化路径之前
        Route::get('/', [AlertRuleController::class, 'index']); // 移除权限限制，允许所有认证用户访问
        Route::post('/', [AlertRuleController::class, 'store']); // 移除权限限制，允许所有认证用户创建
        Route::get('/options', [AlertRuleController::class, 'options']); // 移除权限限制，允许所有认证用户访问
        Route::get('/statistics', [AlertRuleController::class, 'statistics']); // 移除权限限制，允许所有认证用户访问
        Route::get('/stats', [AlertRuleController::class, 'stats']); // 预警规则统计数据
        Route::delete('/', [AlertRuleController::class, 'batchDestroy']); // 移除权限限制，允许所有认证用户批量删除
        Route::patch('/batch-status', [AlertRuleController::class, 'batchUpdateStatus']); // 移除权限限制，允许所有认证用户访问
        
        // 参数化路由放在最后，避免冲突
        Route::get('/{alertRule}', [AlertRuleController::class, 'show']); // 移除权限限制，允许所有认证用户访问
        Route::put('/{alertRule}', [AlertRuleController::class, 'update']); // 移除权限限制，允许所有认证用户更新
        Route::delete('/{alertRule}', [AlertRuleController::class, 'destroy']); // 移除权限限制，允许所有认证用户删除
        Route::patch('/{alertRule}/activate', [AlertRuleController::class, 'activate']); // 移除权限限制，允许所有认证用户访问
        Route::patch('/{alertRule}/deactivate', [AlertRuleController::class, 'deactivate']); // 移除权限限制，允许所有认证用户访问
        Route::put('/{alertRule}/status', [AlertRuleController::class, 'updateStatus']); // 单个状态更新路由
    });
    
    // 数据分析路由
    Route::prefix('analytics')->group(function () {
        Route::get('/dashboard', [AnalyticsController::class, 'getDashboardStatistics']);
        Route::get('/statistics', [AnalyticsController::class, 'getDashboardStatistics']);
        Route::get('/promotion-analysis', [AnalyticsController::class, 'getPromotionAnalysis']);
        Route::get('/price-trend/{item_id}', [AnalyticsController::class, 'getPriceTrendAnalysis']);
        Route::post('/price-trend-chart', [AnalyticsController::class, 'getPriceTrendChart']);
        Route::post('/product-metrics', [AnalyticsController::class, 'calculateProductMetrics']);
        Route::get('/promotion-intensity', [AnalyticsController::class, 'getPromotionIntensityIndex']);
        Route::get('/category-price-distribution', [AnalyticsController::class, 'getCategoryPriceDistribution']);
        Route::post('/comprehensive-analysis-report', [AnalyticsController::class, 'getComprehensiveAnalysisReport']);
        Route::post('/comprehensive-report', [AnalyticsController::class, 'getComprehensiveAnalysisReport']);
        Route::delete('/cache', [AnalyticsController::class, 'clearCache']);
    });
    
    // 审计日志路由
    Route::prefix('audit-logs')->group(function () {
        Route::get('/', [AuditLogController::class, 'index'])->middleware('permission:audit_log.read');
        Route::get('/statistics', [AuditLogController::class, 'statistics'])->middleware('permission:audit_log.read');
        Route::get('/user-statistics', [AuditLogController::class, 'userStatistics'])->middleware('permission:audit_log.read');
        Route::get('/action-statistics', [AuditLogController::class, 'actionStatistics'])->middleware('permission:audit_log.read');
        Route::get('/export', [AuditLogController::class, 'export'])->middleware('permission:audit_log.export');
        Route::delete('/cleanup', [AuditLogController::class, 'cleanup'])->middleware('permission:audit_log.delete');
        Route::get('/{id}', [AuditLogController::class, 'show'])->middleware('permission:audit_log.read');
    });

    // 预警历史路由
    Route::prefix('alerts')->group(function () {
        Route::get('/', [AlertsController::class, 'index'])->middleware('permission:alert.read');
        Route::delete('/', [AlertsController::class, 'batchDelete'])->middleware('permission:alert.delete');
        Route::get('/{alert}', [AlertsController::class, 'show'])->middleware('permission:alert.read');
        Route::patch('/{alert}/read', [AlertsController::class, 'markAsRead'])->middleware('permission:alert.update');
    });

    // 用户通知路由
    Route::prefix('notifications')->group(function () {
        Route::get('/', [NotificationController::class, 'index']);
        Route::get('/statistics', [NotificationController::class, 'statistics']);
        Route::post('/mark-all-read', [NotificationController::class, 'markAllAsRead']);
        Route::delete('/clear', [NotificationController::class, 'clear']);
        Route::delete('/', [NotificationController::class, 'batchDestroy']);
        Route::get('/{id}', [NotificationController::class, 'show']);
        Route::patch('/{id}/read', [NotificationController::class, 'markAsRead']);
        Route::delete('/{id}', [NotificationController::class, 'destroy']);
    });

    // 添加正确的dashboard-stats路由
    Route::get('/dashboard-stats', [AnalyticsController::class, 'getDashboardStatistics']);
});