# 任务分组预警汇总系统实现总结

## 项目背景

原有的预警系统会为每个触发预警的商品单独发送通知，导致通知数量过多。用户要求改为按任务分组汇总预警，特别是SKU级预警需要汇总到商品级别，最终按照"分组 -> 预警规则1 -> 预警商品及简报 -> 预警规则2 -> 预警商品及简报"的格式发送站内信。

## 系统架构

### 核心组件

1. **TaskGroupAlertSummary 模型**：存储任务分组预警汇总信息
2. **TaskGroupAlertSummaryService 服务**：处理预警汇总逻辑
3. **TaskGroupAlertSummaryNotification 通知类**：发送汇总通知
4. **ProcessTaskGroupCollectionComplete 队列任务**：处理任务分组采集完成

### 数据库变更

1. **新增表**：`task_group_alert_summaries`
   - 存储汇总信息、状态、统计数据
   - 支持JSON格式的汇总数据存储

2. **修改表**：`alerts`
   - 添加 `task_group_alert_summary_id` 外键
   - 关联预警记录到汇总

## 工作流程

### 1. 数据采集阶段
```
DataCollectionService.collectAndStandardize()
├── 生成采集批次ID (collectionBatchId)
├── 执行数据采集
├── 分发预警检查任务 (ProcessAlertChecking)
└── 分发任务分组完成检查 (ProcessTaskGroupCollectionComplete)
```

### 2. 预警检查阶段
```
ProcessAlertChecking.handle()
├── AlertService.processAlerts(productData, collectionBatchId)
├── 检查各种预警规则
├── 创建Alert记录
└── 添加到TaskGroupAlertSummary (而不是立即发送通知)
```

### 3. 汇总处理阶段
```
ProcessTaskGroupCollectionComplete.handle()
├── 检查任务是否完成采集
├── 等待预警检查完成
├── AlertService.completeTaskGroupCollection()
└── TaskGroupAlertSummaryService.finalizeSummary()
    ├── 汇总预警数据
    ├── 按规则分类
    ├── SKU级预警汇总到商品级
    └── 发送TaskGroupAlertSummaryNotification
```

## 关键实现细节

### 1. SKU级预警汇总

在 `TaskGroupAlertSummary.addAlert()` 方法中：
```php
// SKU级预警汇总到商品级别
if (in_array($alert->rule_type, ['promotion_price_deviation', 'channel_price_deviation'])) {
    $productId = $alert->productData->item_id;
    // 如果同一商品已有预警，则合并；否则新增
}
```

### 2. 采集批次ID机制

- 每次数据采集生成唯一的批次ID
- 用于标识同一轮采集的所有预警
- 确保预警汇总的完整性和一致性

### 3. 任务完成检测

在 `TaskGroupAlertSummaryService.isMonitoringTaskCollectionComplete()` 中：
```php
// 检查目标商品是否都已完成采集
$collectedProductCount = ProductData::where('monitoring_task_id', $monitoringTaskId)
    ->whereIn('item_id', $productIds)
    ->where('last_collected_at', '>=', now()->subHours(1))
    ->count();
```

### 4. 通知格式化

在 `TaskGroupAlertSummaryNotification.toDatabase()` 中：
```php
$message = "【{$taskGroupName}】预警汇总\n\n";
foreach ($rules as $ruleType => $ruleData) {
    $message .= "📋 {$ruleData['name']}\n";
    foreach ($ruleData['products'] as $productId => $productInfo) {
        $message .= "  • {$productInfo['title']}: {$productInfo['summary']}\n";
    }
    $message .= "\n";
}
```

## 队列配置

### 队列分类
- `alerts`：预警检查任务
- `task_groups`：任务分组处理
- `notifications`：通知发送

### 延迟设置
- 预警检查：延迟5秒
- 任务分组完成检查：延迟2分钟
- 预警检查等待：30秒

## 测试方案

### 1. 单元测试
- 创建测试命令：`TestTaskGroupAlertSummary`
- 测试脚本：`test_alert_summary.php`

### 2. 集成测试
```bash
# 创建测试数据
php artisan test:task-group-alert-summary --create-test-data

# 运行测试
php artisan test:task-group-alert-summary --task-group-id=1
```

## 性能优化

### 1. 数据库优化
- 添加必要的索引
- 使用JSON字段存储汇总数据
- 定期清理过期汇总记录

### 2. 队列优化
- 使用专门的队列处理不同类型任务
- 设置合理的延迟时间
- 避免重复任务分发

### 3. 内存优化
- 批量处理预警记录
- 及时释放不需要的对象
- 使用数据库分页查询

## 监控和日志

### 关键日志点
1. 采集批次ID生成
2. 预警添加到汇总
3. 任务完成检测
4. 汇总处理和通知发送

### 监控指标
- 汇总处理时间
- 通知发送成功率
- 队列任务积压情况
- 预警汇总准确性

## 扩展性考虑

### 1. 支持更多预警规则类型
- 在汇总逻辑中添加新的规则处理
- 更新通知格式化逻辑

### 2. 支持多种通知渠道
- 扩展通知类支持邮件、短信等
- 添加通知渠道配置

### 3. 支持自定义汇总规则
- 允许用户配置汇总策略
- 支持不同级别的汇总粒度

## 部署注意事项

1. **数据库迁移**：确保新表和字段正确创建
2. **队列配置**：确保新的队列正常运行
3. **缓存清理**：清理相关缓存确保新代码生效
4. **监控设置**：设置相关监控和告警

## 总结

本次实现成功将原有的单个商品预警通知改为任务分组级别的汇总通知，主要特点：

1. **减少通知数量**：从每个商品一个通知改为每个任务分组一个通知
2. **提高信息密度**：按预警规则分类，便于用户快速了解问题
3. **支持SKU汇总**：SKU级预警自动汇总到商品级别
4. **保证数据完整性**：使用采集批次ID确保汇总的准确性
5. **良好的扩展性**：支持未来添加更多预警规则和通知渠道

系统已通过测试验证，可以正常工作并满足用户需求。
