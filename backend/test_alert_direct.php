<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\ProductData;
use App\Models\MonitoringTask;
use App\Models\AlertRule;
use App\Services\AlertService;
use App\Services\TaskGroupAlertSummaryService;
use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\Log;

// 启动Laravel应用
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== 直接测试预警逻辑 ===\n";

try {
    // 获取产品数据
    $productData = ProductData::find(17);
    if (!$productData) {
        echo "错误：找不到产品数据 ID 17\n";
        exit(1);
    }
    
    echo "产品数据ID: {$productData->id}\n";
    echo "商品ID: {$productData->item_id}\n";
    echo "监控任务ID: {$productData->monitoring_task_id}\n";
    
    // 获取监控任务
    $task = MonitoringTask::find($productData->monitoring_task_id);
    if (!$task) {
        echo "错误：找不到监控任务\n";
        exit(1);
    }
    
    echo "监控任务名称: {$task->name}\n";
    
    // 获取预警规则（使用前两个规则进行测试）
    $alertRules = AlertRule::whereIn('id', [5, 6])->get();
    
    echo "找到 " . $alertRules->count() . " 个预警规则\n";
    
    // 创建AlertService实例
    $summaryService = new TaskGroupAlertSummaryService();
    $alertService = new AlertService($summaryService);
    
    // 模拟标准化数据
    $mockStandardizedData = [
        'product_fields' => [
            'state' => 1, // 在售状态
            'code' => 200, // 成功状态
            'price' => 150.00, // 当前价格
            'title' => '测试商品标题',
        ],
        'sku_fields' => [
            [
                'sku_id' => 'test_sku_001',
                'name' => '默认规格',
                'price' => 150.00,
                'promotion_price' => 130.00, // 券后价
                'stock' => 100
            ]
        ]
    ];
    
    echo "模拟标准化数据:\n";
    echo json_encode($mockStandardizedData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
    
    // 更新产品数据的标准化数据
    $productData->standardized_data = json_encode($mockStandardizedData);
    $productData->save();
    
    echo "\n=== 开始检查预警规则 ===\n";
    
    foreach ($alertRules as $rule) {
        echo "\n--- 检查规则: {$rule->name} (ID: {$rule->id}) ---\n";
        echo "规则类型: " . json_encode($rule->rule_type) . "\n";
        echo "条件: " . json_encode($rule->conditions) . "\n";
        
        // 直接调用预警检查方法
        $result = $alertService->processAlerts($productData, 'batch_test_direct');

        echo "处理结果: " . json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";

        if ($result['success'] && !empty($result['triggered_alerts'])) {
            echo "触发的预警数量: " . $result['alerts_count'] . "\n";
            foreach ($result['triggered_alerts'] as $alert) {
                if (is_object($alert)) {
                    echo "预警详情:\n";
                    echo "  - 规则ID: {$alert->alert_rule_id}\n";
                    echo "  - 类型: {$alert->alert_type}\n";
                    echo "  - 消息: {$alert->message}\n";
                    echo "  - 数值: {$alert->alert_value}\n";
                } else {
                    echo "预警数据: " . json_encode($alert, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
                }
            }
        } else {
            echo "未触发预警或处理失败\n";
        }
    }
    
    echo "\n=== 测试完成 ===\n";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    echo "堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
}
