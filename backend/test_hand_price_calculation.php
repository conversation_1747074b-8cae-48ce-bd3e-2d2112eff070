<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Services\DataCollectionService;
use Illuminate\Support\Facades\Log;

// 启动Laravel应用
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== 测试到手价计算逻辑 ===\n\n";

// 创建一个模拟的数据集合来测试计算逻辑
$mockSkuData = [
    [
        'sku_id' => '1',
        'sku_name' => 'SKU 1',
        'price' => '100.00',
        'promotion_price' => '80.00',  // 有促销价
        'stock' => '10'
    ],
    [
        'sku_id' => '2', 
        'sku_name' => 'SKU 2',
        'price' => '120.00',
        'promotion_price' => '0.00',   // 无促销价
        'stock' => '5'
    ],
    [
        'sku_id' => '3',
        'sku_name' => 'SKU 3', 
        'price' => '90.00',
        'promotion_price' => '75.00',  // 有促销价
        'stock' => '20'
    ],
    [
        'sku_id' => '4',
        'sku_name' => 'SKU 4',
        'price' => '110.00', 
        'promotion_price' => '95.00',  // 有促销价
        'stock' => '15'
    ]
];

echo "模拟SKU数据:\n";
foreach ($mockSkuData as $sku) {
    echo "  - {$sku['sku_name']}: 原价 {$sku['price']}, 促销价 {$sku['promotion_price']}\n";
}
echo "\n";

// 模拟计算逻辑
$handPrices = [];
foreach ($mockSkuData as $sku) {
    $promotionPrice = $sku['promotion_price'] ?? null;
    if ($promotionPrice !== null && is_numeric($promotionPrice) && $promotionPrice > 0) {
        $handPrices[] = (float)$promotionPrice;
        echo "包含促销价: {$promotionPrice}\n";
    } else {
        echo "跳过促销价: {$promotionPrice} (无效或为0)\n";
    }
}

echo "\n";

if (!empty($handPrices)) {
    $minHandPrice = min($handPrices);
    $maxHandPrice = max($handPrices);
    
    echo "计算结果:\n";
    echo "  - 有效促销价格: " . implode(', ', $handPrices) . "\n";
    echo "  - 最低到手价: {$minHandPrice}\n";
    echo "  - 最高到手价: {$maxHandPrice}\n";
} else {
    echo "计算结果: 没有有效的促销价格\n";
}

echo "\n=== 现在测试实际的商品数据 ===\n\n";

// 测试实际商品，但修改数据来模拟有促销价的情况
$dataCollectionService = new DataCollectionService();

// 使用反射来访问私有方法进行测试
$reflection = new ReflectionClass($dataCollectionService);
$standardizeDataMethod = $reflection->getMethod('standardizeData');
$standardizeDataMethod->setAccessible(true);

// 创建模拟的原始数据
$mockRawData = [
    'code' => 200,
    'data' => [
        'id' => '123456789',
        'title' => '测试商品',
        'price' => '100.00',
        'skus' => [
            [
                'id' => '1',
                'name' => 'SKU 1',
                'price' => '100.00',
                'subPrice' => '80.00',  // 有促销价
                'quantity' => '10'
            ],
            [
                'id' => '2',
                'name' => 'SKU 2', 
                'price' => '120.00',
                'subPrice' => '95.00',  // 有促销价
                'quantity' => '5'
            ]
        ]
    ]
];

// 获取数据源配置
$dataSource = \App\Models\DataSource::find(3);
$fieldMapping = is_string($dataSource->field_mapping) ?
    json_decode($dataSource->field_mapping, true) :
    $dataSource->field_mapping;

echo "使用模拟数据测试标准化:\n";
echo "原始SKU数据:\n";
foreach ($mockRawData['data']['skus'] as $sku) {
    echo "  - {$sku['name']}: 原价 {$sku['price']}, subPrice {$sku['subPrice']}\n";
}
echo "\n";

try {
    $result = $standardizeDataMethod->invoke($dataCollectionService, $mockRawData, $fieldMapping);
    
    $productFields = $result['product_fields'] ?? [];
    $skuFields = $result['sku_fields'] ?? [];
    
    echo "标准化后的产品字段:\n";
    if (isset($productFields['min_hand_price'])) {
        echo "  - min_hand_price: {$productFields['min_hand_price']}\n";
    } else {
        echo "  - min_hand_price: 未设置\n";
    }
    
    if (isset($productFields['max_hand_price'])) {
        echo "  - max_hand_price: {$productFields['max_hand_price']}\n";
    } else {
        echo "  - max_hand_price: 未设置\n";
    }
    
    echo "\n标准化后的SKU字段:\n";
    foreach ($skuFields as $sku) {
        echo "  - {$sku['sku_name']}: promotion_price = {$sku['promotion_price']}\n";
    }
    
} catch (Exception $e) {
    echo "测试失败: " . $e->getMessage() . "\n";
}

echo "\n=== 测试完成 ===\n";
