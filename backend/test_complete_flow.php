<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Support\Facades\Log;

// 启动Laravel应用
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== 测试完整的到手价采集流程 ===\n\n";

// 创建一个测试任务
$dataSourceId = 3;
$productId = '999888777'; // 使用一个测试商品ID
$monitoringTaskId = 2;

echo "1. 测试参数:\n";
echo "   数据源ID: {$dataSourceId}\n";
echo "   目标商品: {$productId}\n";
echo "   监控任务ID: {$monitoringTaskId}\n\n";

echo "2. 现在我们需要模拟API返回有促销价格的数据\n";
echo "   由于我们无法控制真实API的返回，我们将直接测试DataCollectionService\n\n";

// 直接测试DataCollectionService
use App\Services\DataCollectionService;

$dataCollectionService = new DataCollectionService();

// 使用反射来模拟API调用
$reflection = new ReflectionClass($dataCollectionService);

// 模拟有促销价格的API响应
$mockApiResponse = [
    'code' => 200,
    'total_remaining' => '999.000',
    'data' => [
        'id' => '999888777',
        'is_sku' => true,
        'itemType' => '淘宝',
        'price' => '100.00',
        'promotion' => [],
        'sale' => '1000',
        'commentCount' => '500',
        'category_id' => '123456',
        'category_path' => '测试分类->子分类',
        'pic_urls' => [
            'https://example.com/image1.jpg',
            'https://example.com/image2.jpg'
        ],
        'skus' => [
            [
                'picUrl' => 'https://example.com/sku1.jpg',
                'id' => '111111',
                'name' => '颜色:红色;尺寸:M',
                'price' => '100.00',
                'subPrice' => '85.00',  // 有促销价
                'subPriceTitle' => '限时优惠',
                'quantity' => '50'
            ],
            [
                'picUrl' => 'https://example.com/sku2.jpg',
                'id' => '222222',
                'name' => '颜色:蓝色;尺寸:L',
                'price' => '120.00',
                'subPrice' => '95.00',  // 有促销价
                'subPriceTitle' => '限时优惠',
                'quantity' => '30'
            ],
            [
                'picUrl' => 'https://example.com/sku3.jpg',
                'id' => '333333',
                'name' => '颜色:绿色;尺寸:S',
                'price' => '90.00',
                'subPrice' => '0.00',   // 无促销价
                'subPriceTitle' => '',
                'quantity' => '100'
            ]
        ],
        'state' => '上架',
        'timestamp' => date('Y-m-d H:i:s'),
        'title' => '测试商品-有促销价格',
        'shopName' => '测试店铺',
        'shopId' => 12345,
        'nick' => '测试卖家',
        'sellerId' => 67890,
        'postage' => 0,
        'from' => '浙江杭州',
        'props' => [
            ['name' => '品牌', 'value' => '测试品牌'],
            ['name' => '材质', 'value' => '棉质']
        ]
    ]
];

echo "3. 模拟API响应数据:\n";
echo "   - 商品标题: {$mockApiResponse['data']['title']}\n";
echo "   - SKU数量: " . count($mockApiResponse['data']['skus']) . "\n";
echo "   - 促销价格SKU:\n";
foreach ($mockApiResponse['data']['skus'] as $sku) {
    $hasPromotion = $sku['subPrice'] !== '0.00' && $sku['subPrice'] !== '';
    echo "     * {$sku['name']}: 原价 {$sku['price']}, 促销价 {$sku['subPrice']} " . 
         ($hasPromotion ? '✓' : '✗') . "\n";
}
echo "\n";

// 获取数据源配置
$dataSource = \App\Models\DataSource::find($dataSourceId);
$fieldMapping = is_string($dataSource->field_mapping) ? 
    json_decode($dataSource->field_mapping, true) : 
    $dataSource->field_mapping;

// 调用标准化方法
$standardizeDataMethod = $reflection->getMethod('standardizeData');
$standardizeDataMethod->setAccessible(true);

try {
    echo "4. 执行数据标准化...\n";
    $standardizedResult = $standardizeDataMethod->invoke($dataCollectionService, $mockApiResponse, $fieldMapping);

    $productFields = $standardizedResult['product_fields'] ?? [];
    $skuFields = $standardizedResult['sku_fields'] ?? [];

    echo "5. 标准化结果:\n";
    echo "   产品字段:\n";
    echo "     - 商品ID: " . ($productFields['product_id'] ?? 'N/A') . "\n";
    echo "     - 标题: " . ($productFields['title'] ?? 'N/A') . "\n";
    echo "     - 最低到手价: " . ($productFields['min_hand_price'] ?? 'NULL') . "\n";
    echo "     - 最高到手价: " . ($productFields['max_hand_price'] ?? 'NULL') . "\n";
    
    echo "   SKU字段:\n";
    foreach ($skuFields as $sku) {
        echo "     - {$sku['sku_name']}: promotion_price = {$sku['promotion_price']}\n";
    }
    
    // 模拟存储到数据库
    echo "\n6. 模拟数据库存储...\n";
    
    $safeNumeric = function($value) {
        if ($value === null || $value === '') return null;
        if (is_numeric($value)) return (float)$value;
        return null;
    };
    
    $updateData = [
        'monitoring_task_id' => $monitoringTaskId,
        'item_id' => $productId,
        'title' => $productFields['title'] ?? null,
        'min_hand_price' => $safeNumeric($productFields['min_hand_price'] ?? null),
        'max_hand_price' => $safeNumeric($productFields['max_hand_price'] ?? null),
        'last_collected_at' => now(),
    ];
    
    echo "   准备存储的数据:\n";
    foreach ($updateData as $key => $value) {
        echo "     - {$key}: " . ($value ?? 'NULL') . "\n";
    }
    
    if ($updateData['min_hand_price'] !== null && $updateData['max_hand_price'] !== null) {
        echo "\n✅ 成功！到手价采集和计算功能正常工作\n";
        echo "   - 最低到手价: {$updateData['min_hand_price']}\n";
        echo "   - 最高到手价: {$updateData['max_hand_price']}\n";
    } else {
        echo "\n❌ 失败！到手价计算有问题\n";
    }
    
} catch (Exception $e) {
    echo "❌ 测试失败: " . $e->getMessage() . "\n";
    echo "堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== 测试完成 ===\n";
