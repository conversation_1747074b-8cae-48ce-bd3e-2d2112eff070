<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Services\DataCollectionService;
use App\Models\MonitoringTask;

// 启动Laravel应用
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== 手动触发数据采集测试 ===\n";

// 获取监控任务
$task = MonitoringTask::find(7);
if (!$task) {
    echo "监控任务不存在\n";
    exit;
}

// 生成采集批次ID
$collectionBatchId = 'manual_test_' . now()->format('YmdHis') . '_' . uniqid();
echo "采集批次ID: {$collectionBatchId}\n";

// 调用数据采集服务
$dataCollectionService = app(DataCollectionService::class);

try {
    $result = $dataCollectionService->collectAndStandardize(
        $task->data_source_id,
        'TEST002',
        [],
        $task->id,
        $collectionBatchId
    );
    
    echo "数据采集完成\n";
    echo "结果: " . json_encode($result, JSON_PRETTY_PRINT) . "\n";
    
} catch (Exception $e) {
    echo "数据采集失败: " . $e->getMessage() . "\n";
    echo "堆栈跟踪: " . $e->getTraceAsString() . "\n";
}
