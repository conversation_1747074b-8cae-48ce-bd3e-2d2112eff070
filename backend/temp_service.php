<?php

namespace App\Services;

use App\Jobs\ProcessAlertChecking;
use App\Models\DataSource;
use App\Models\ProductData;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;

class DataCollectionService
{

    /**
     * 执行数据采集和标准化
     *
     * @param int $dataSourceId 数据源ID
     * @param mixed $itemId 目标商品ID
     * @param array $extraParams 额外参数
     * @param int|null $monitoringTaskId 监控任务ID（如果提供，将用于数据存储）
     * @return array
     */
    public function collectAndStandardize(int $dataSourceId, $itemId, array $extraParams = [], ?int $monitoringTaskId = null): array
    {
        Log::info('开始数据采集与标准化', [
            'data_source_id' => $dataSourceId,
            'item_id' => $itemId,
            'extra_params' => $extraParams,
            'monitoring_task_id' => $monitoringTaskId,
            'process_start_time' => now()->toDateTimeString()
        ]);

        try {
            // 1. 获取数据源配置
            Log::info('步骤1：开始获取数据源配置', ['data_source_id' => $dataSourceId]);
            $dataSource = DataSource::findOrFail($dataSourceId);
            Log::info('步骤1：数据源配置加载完成', [
                'data_source_id' => $dataSource->id,
                'data_source_name' => $dataSource->name,
                'api_method' => $dataSource->api_method,
                'api_url' => $dataSource->config['url'] ?? 'not_set',
                'timeout' => $dataSource->timeout,
                'config_full' => $dataSource->config,
                'field_mapping' => $dataSource->field_mapping,
                'default_params' => $dataSource->default_params
            ]);

            // 2. 构造API请求
            Log::info('步骤2：开始构造API请求', [
                'data_source_id' => $dataSourceId,
                'item_id' => $itemId,
                'extra_params' => $extraParams
            ]);
            $requestData = $this->buildApiRequest($dataSource, $itemId, $extraParams);
            Log::info('步骤2：API请求构造完成', [
                'request_url' => $requestData['url'],
                'request_method' => $requestData['method'],
                'request_params' => $requestData['params'],
                'request_headers' => $requestData['headers'] ?? 'None',
                'request_timeout' => $requestData['timeout']
            ]);

            // 3. 发起HTTP请求
            Log::info('步骤3：开始发起HTTP请求');
            $response = $this->makeHttpRequest($requestData);
            Log::info('步骤3：HTTP请求完成', [
                'response_status' => $response->status(),
                'response_headers' => $response->headers(),
                'response_size' => strlen($response->body()) . ' bytes',
                'response_successful' => $response->successful()
            ]);

            // 4. 解析响应
            Log::info('步骤4：开始解析API响应');
            $rawData = $this->parseResponse($response, $dataSource);
            Log::info('步骤4：API响应解析完成', [
                'raw_data_keys' => array_keys($rawData),
                'raw_data_sample' => array_slice($rawData, 0, 3, true), // 只记录前3个字段作为样本
                'raw_data_full' => $rawData // 完整的原始数据
            ]);

            // 5. 标准化数据
            Log::info('步骤5：开始数据标准化', [
                'field_mapping_rules' => $dataSource->field_mapping,
                'raw_data_to_standardize' => $rawData
            ]);
            $standardizedData = $this->standardizeData($rawData, $dataSource->field_mapping);
            Log::info('步骤5：数据标准化完成', [
                'field_mapping_rules' => $dataSource->field_mapping,
                'standardized_data' => $standardizedData,
                'standardization_summary' => [
                    'input_fields_count' => count($rawData),
                    'output_fields_count' => count($standardizedData),
                    'mapping_rules_count' => count($dataSource->field_mapping['fields'] ?? [])
                ]
            ]);

            // 6. 存储数据
            Log::info('步骤6：开始存储数据到数据库');
            $productData = $this->storeData($dataSource, $itemId, $rawData, $standardizedData, $monitoringTaskId);
            Log::info('步骤6：数据存储完成', [
                'product_data_id' => $productData->id,
                'item_id' => $productData->item_id,
                'monitoring_task_id' => $productData->monitoring_task_id,
                'operation_type' => $productData->wasRecentlyCreated ? 'CREATE' : 'UPDATE',
                'created_at' => $productData->created_at,
                'updated_at' => $productData->updated_at,
                'last_collected_at' => $productData->last_collected_at
            ]);

            // 7. 触发警报检查
            Log::info('步骤7：开始触发警报检查');
            $this->dispatchAlertChecking($productData);
            Log::info('步骤7：警报检查任务已分发');

            Log::info('数据采集与标准化流程全部完成', [
                'data_source_id' => $dataSourceId,
                'item_id' => $itemId,
                'monitoring_task_id' => $monitoringTaskId,
                'product_data_id' => $productData->id,
                'process_end_time' => now()->toDateTimeString(),
                'final_result' => [
                    'raw_data' => $rawData,
                    'standardized_data' => $standardizedData,
                    'database_record_id' => $productData->id
                ]
            ]);

            return [
                'success' => true,
                'product_data_id' => $productData->id,
                'raw_data' => $rawData,
                'standardized_data' => $standardizedData,
            ];

        } catch (\Exception $e) {
            Log::error('数据采集与标准化流程失败', [
                'data_source_id' => $dataSourceId,
                'item_id' => $itemId,
                'monitoring_task_id' => $monitoringTaskId,
                'error_message' => $e->getMessage(),
                'error_file' => $e->getFile(),
                'error_line' => $e->getLine(),
                'process_end_time' => now()->toDateTimeString()
            ]);
            
            $this->handleException($e, $dataSourceId, $itemId);
            
            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * 构造API请求
     */
    private function buildApiRequest(DataSource $dataSource, $itemId, array $extraParams): array
    {
        $config = $dataSource->config ?? [];
        Log::info('开始构造API请求', [
            'data_source_id' => $dataSource->id,
            'item_id' => $itemId,
            'config_keys' => array_keys($config),
            'auth_type' => $config['auth_type'] ?? 'none',
        ]);

        $url = $config['url'] ?? '';

        // 动态替换URL中的占位符
        $url = str_replace('{item_id}', $itemId, $url);

        // 合并基础参数 (来自config的默认参数、来自任务的额外参数)
        $params = array_merge($config['params'] ?? [], $dataSource->default_params ?? [], $extraParams);
        
        // 确保商品ID作为参数传递
        // 参数名可以在数据源配置中指定，默认为'item_id'
        $itemIdParamName = $config['item_id_param'] ?? 'item_id';
        if (!isset($params[$itemIdParamName]) && !empty($itemId)) {
            $params[$itemIdParamName] = $itemId;
        }

        // 处理认证配置
        $headers = $config['headers'] ?? [];
        $authType = $config['auth_type'] ?? 'none';
        
        Log::info('处理认证配置', [
            'auth_type' => $authType,
            'api_key_name' => $config['api_key_name'] ?? null,
            'has_api_key_value' => !empty($config['api_key_value']),
            'has_token' => !empty($config['token']),
        ]);

        switch ($authType) {
            case 'bearer':
                if (!empty($config['token'])) {
                    $headers['Authorization'] = 'Bearer ' . $config['token'];
                    Log::info('添加Bearer认证头');
                }
                break;
                
            case 'header_key':
                if (!empty($config['api_key_name']) && !empty($config['api_key_value'])) {
                    $headers[$config['api_key_name']] = $config['api_key_value'];
                    Log::info('添加API Key到请求头', [
                        'key_name' => $config['api_key_name']
                    ]);
                }
                break;
                
            case 'query_key':
                if (!empty($config['api_key_name']) && !empty($config['api_key_value'])) {
                    $params[$config['api_key_name']] = $config['api_key_value'];
                    Log::info('添加API Key到查询参数', [
                        'key_name' => $config['api_key_name']
                    ]);
                }
                break;
                
            case 'none':
            default:
                // 兼容旧版本：如果没有设置认证方式但config.params中有token，则使用它
                if (empty($authType) || $authType === 'none') {
                    if (!empty($config['params']['token'])) {
                        Log::info('检测到旧版本token配置，使用config.params中的token');
                        // token已经在上面合并参数时包含了，这里只记录日志
                    }
                }
                break;
        }
        
        // 构造请求数据
        $requestData = [
            'method' => strtoupper($config['method'] ?? 'GET'),
            'url' => $url,
            'params' => $params,
            'timeout' => $config['timeout'] ?? $dataSource->timeout ?? 30,
        ];
        
        // 只有当headers不为空时才添加
        if (!empty($headers)) {
            $requestData['headers'] = $headers;
        }
        
        Log::info('API请求构造完成', [
            'final_url' => $url,
            'final_params' => $params,
            'final_headers' => $headers,
            'auth_applied' => $authType !== 'none'
        ]);
        
        return $requestData;
    }

    /**
     * 发起HTTP请求
     */
    private function makeHttpRequest(array $requestData): \Illuminate\Http\Client\Response
    {
        // 构造完整的请求URL用于日志记录
        $fullUrl = $requestData['url'] . '?' . http_build_query($requestData['params']);
        Log::info('发起API请求', [
            'url' => $fullUrl,
            'method' => $requestData['method'],
            'headers' => $requestData['headers'] ?? 'None',
            'params' => $requestData['params'],
            'timeout' => $requestData['timeout'],
        ]);

        $response = Http::timeout($requestData['timeout']);
        
        // 只有当headers存在且不为空时才设置
        if (isset($requestData['headers']) && !empty($requestData['headers'])) {
            $response = $response->withHeaders($requestData['headers']);
        }
        
        // 解决SSL证书问题
        // 在生产环境中，应该配置php.ini指向正确的cacert.pem文件
        // 这里为了开发方便，暂时禁用SSL验证
        if (app()->environment('local')) {
            $response->withoutVerifying();
        }

        switch ($requestData['method']) {
            case 'POST':
                return $response->post($requestData['url'], $requestData['params']);
            case 'PUT':
                return $response->put($requestData['url'], $requestData['params']);
            case 'PATCH':
                return $response->patch($requestData['url'], $requestData['params']);
            case 'DELETE':
                return $response->delete($requestData['url'], $requestData['params']);
            default: // GET
                return $response->get($requestData['url'], $requestData['params']);
        }
    }

    /**
     * 解析HTTP响应
     */
    private function parseResponse(\Illuminate\Http\Client\Response $response, DataSource $dataSource): array
    {
        Log::info('收到API响应', [
            'status_code' => $response->status(),
            'headers' => $response->headers(),
            'body_preview' => mb_substr($response->body(), 0, 1000) . (mb_strlen($response->body()) > 1000 ? '...' : ''), // 记录前1000个字符
        ]);

        // 记录完整的响应体
        Log::info('API完整响应体', [
            'response_body' => $response->body(),
            'response_length' => strlen($response->body())
        ]);

        if (!$response->successful()) {
            throw new \Exception('API请求失败: HTTP ' . $response->status());
        }

        $responseBody = $response->body();
        Log::info('API响应体获取成功', [
            'response_length' => strlen($responseBody),
            'response_preview' => Str::limit($responseBody, 1000)
        ]);

        $data = $response->json();

        if (is_null($data)) {
            Log::error('JSON解析失败', [
                'response_body' => $responseBody,
                'json_last_error' => json_last_error_msg()
            ]);
            throw new \Exception("无法解析JSON响应: " . $responseBody);
        }

        Log::info('JSON解析成功', [
            'data_type' => gettype($data),
            'data_structure' => is_array($data) ? array_keys($data) : 'not_array',
            'data_count' => is_array($data) ? count($data) : 'not_countable',
            'parsed_data' => $data // 记录完整的解析后数据
        ]);

        return $data;
    }

    /**
     * 根据字段映射标准化数据
     *
     * @param array $rawData
     * @param array $mappingRules
     * @return array
     */
    private function standardizeData(array $rawData, ?array $mappingRules): array
    {
        Log::info('开始数据标准化', [
            'raw_data' => $rawData,
            'mapping_rules' => $mappingRules
        ]);

        if (empty($mappingRules)) {
            Log::info('没有映射规则，返回原始数据', [
                'returned_data' => $rawData
            ]);
            return $rawData; // No rules, return raw data
        }

        $standardizedData = [];
        // Get the root data node if specified
        $dataNode = Arr::get($rawData, $mappingRules['data_path'] ?? null);

        Log::info('获取数据节点', [
            'data_path' => $mappingRules['data_path'] ?? 'null',
            'data_node' => $dataNode,
            'data_node_type' => gettype($dataNode)
        ]);

        // If data_path is invalid or not specified, use rawData itself
        if (is_null($dataNode)) {
            $dataNode = $rawData;
            Log::info('数据路径无效，使用原始数据', [
                'data_node' => $dataNode
            ]);
        }

        Log::info('开始字段映射处理', [
            'fields_to_map' => $mappingRules['fields'] ?? []
        ]);

        foreach ($mappingRules['fields'] as $targetField => $sourcePath) {
            Log::info('处理字段映射', [
                'target_field' => $targetField,
                'source_path' => $sourcePath,
                'source_path_type' => gettype($sourcePath)
            ]);

            if (is_array($sourcePath)) { // Handle nested structures like SKUs
                Log::info('处理嵌套结构字段', [
                    'target_field' => $targetField,
                    'nested_config' => $sourcePath
                ]);

                $nestedItems = [];
                $nestedDataNode = Arr::get($dataNode, $sourcePath['path']);
                
                Log::info('获取嵌套数据节点', [
                    'nested_path' => $sourcePath['path'],
                    'nested_data_node' => $nestedDataNode,
                    'nested_data_type' => gettype($nestedDataNode)
                ]);

                if (is_array($nestedDataNode)) {
                    foreach ($nestedDataNode as $index => $item) {
                        Log::info('处理嵌套项目', [
                            'item_index' => $index,
                            'item_data' => $item
                        ]);

                        $nestedItem = [];
                        foreach ($sourcePath['fields'] as $nestedTarget => $nestedSource) {
                            $value = Arr::get($item, $nestedSource);
                            $nestedItem[$nestedTarget] = $value;
                            
                            Log::info('嵌套字段映射', [
                                'nested_target' => $nestedTarget,
                                'nested_source' => $nestedSource,
                                'extracted_value' => $value
                            ]);
                        }
                        $nestedItems[] = $nestedItem;
                    }
                }
                $standardizedData[$targetField] = $nestedItems;
                
                Log::info('嵌套结构处理完成', [
                    'target_field' => $targetField,
                    'nested_items_count' => count($nestedItems),
                    'nested_items' => $nestedItems
                ]);
            } else { // Handle simple fields
                $value = Arr::get($dataNode, $sourcePath);
                $standardizedData[$targetField] = $value;
                
                Log::info('简单字段映射完成', [
                    'target_field' => $targetField,
                    'source_path' => $sourcePath,
                    'extracted_value' => $value,
                    'value_type' => gettype($value)
                ]);
            }
        }

        Log::info('数据标准化完成', [
            'standardized_data' => $standardizedData,
            'original_fields_count' => count($mappingRules['fields'] ?? []),
            'mapped_fields_count' => count($standardizedData)
        ]);

        return $standardizedData;
    }

    /**
     * 转换单个值
     */
    private function transformValue($value, ?array $transformRule, string $fieldType)
    {
        // 1. 类型转换
        switch ($fieldType) {
            case 'integer':
                $value = (int) $value;
                break;
            case 'float':
                $value = (float) $value;
                break;
            case 'boolean':
                // 处理 'true', 'false', 1, 0 等情况
                $value = filter_var($value, FILTER_VALIDATE_BOOLEAN);
                break;
            case 'string':
                $value = (string) $value;
                break;
        }

        // 2. 自定义转换规则
        if (!empty($transformRule)) {
            foreach ($transformRule as $rule => $options) {
                switch ($rule) {
                    case 'min_from_array': // 例如: '最低到手价'
                        if (is_array($value)) {
                            $value = min(array_filter($value, 'is_numeric'));
                        }
                        break;
                    case 'regex_match':
                        if (is_string($value) && isset($options['pattern'])) {
                            preg_match($options['pattern'], $value, $matches);
                            $value = $matches[$options['index'] ?? 0] ?? null;
                        }
                        break;
                    case 'string_replace':
                        if (is_string($value) && isset($options['search'], $options['replace'])) {
                            $value = str_replace($options['search'], $options['replace'], $value);
                        }
                        break;
                    case 'timestamp_to_date':
                        $value = \Carbon\Carbon::createFromTimestamp($value)->format($options['format'] ?? 'Y-m-d H:i:s');
                        break;
                    // 可以添加更多自定义规则...
                }
            }
        }

        return $value;
    }

    /**
     * 存储数据到数据库
     */
    private function storeData(DataSource $dataSource, $itemId, array $rawData, array $standardizedData, ?int $monitoringTaskId): ProductData
    {
        Log::info('开始存储数据到数据库', [
            'data_source_id' => $dataSource->id,
            'data_source_name' => $dataSource->name,
            'monitoring_task_id' => $monitoringTaskId,
            'item_id' => $itemId,
            'raw_data_keys' => array_keys($rawData),
            'raw_data_full' => $rawData,
            'standardized_data_keys' => array_keys($standardizedData),
            'standardized_data_full' => $standardizedData,
            'raw_data_size_bytes' => strlen(json_encode($rawData)),
            'standardized_data_size_bytes' => strlen(json_encode($standardizedData))
        ]);

        try {
            // 如果没有提供monitoring_task_id，尝试查找或创建一个默认的监控任务
            if (!$monitoringTaskId) {
                Log::warning('未提供monitoring_task_id，尝试查找默认监控任务', [
                    'data_source_id' => $dataSource->id,
                    'item_id' => $itemId
                ]);

                // 查找与此数据源相关的监控任务
                $monitoringTask = \App\Models\MonitoringTask::where('data_source_id', $dataSource->id)
                    ->where('status', 'active')
                    ->first();

                if ($monitoringTask) {
                    $monitoringTaskId = $monitoringTask->id;
                    Log::info('找到默认监控任务', [
                        'monitoring_task_id' => $monitoringTaskId,
                        'task_name' => $monitoringTask->name
                    ]);
                } else {
                    Log::error('未找到可用的监控任务', [
                        'data_source_id' => $dataSource->id,
                        'item_id' => $itemId
                    ]);
                    throw new \Exception("无法找到可用的监控任务，数据源ID: {$dataSource->id}");
                }
            }

            // 检查是否已存在相同的记录
            $existingRecord = ProductData::where('monitoring_task_id', $monitoringTaskId)
                ->where('item_id', $itemId)
                ->first();

            if ($existingRecord) {
                Log::info('找到现有记录，准备更新', [
                    'existing_id' => $existingRecord->id,
                    'existing_monitoring_task_id' => $existingRecord->monitoring_task_id,
                    'existing_created_at' => $existingRecord->created_at,
                    'existing_updated_at' => $existingRecord->updated_at,
                    'existing_last_collected_at' => $existingRecord->last_collected_at,
                    'existing_raw_data' => $existingRecord->raw_data,
                    'existing_standardized_data' => $existingRecord->standardized_data
                ]);
            } else {
                Log::info('未找到现有记录，准备创建新记录', [
                    'monitoring_task_id' => $monitoringTaskId,
                    'item_id' => $itemId
                ]);
            }

            // 从标准化数据中提取价格
            $price = null;
            if (isset($standardizedData['price'])) {
                $price = is_numeric($standardizedData['price']) ? (float)$standardizedData['price'] : null;
            }

            Log::info('执行数据库updateOrCreate操作', [
                'where_conditions' => [
                    'monitoring_task_id' => $monitoringTaskId,
                    'item_id' => $itemId,
                ],
                'update_data' => [
                    'raw_data' => $rawData,
                    'standardized_data' => $standardizedData,
                    'price' => $price,
                    'last_collected_at' => now()->toDateTimeString(),
                ]
            ]);

            $productData = ProductData::updateOrCreate(
                [
                    'monitoring_task_id' => $monitoringTaskId,
                    'item_id' => $itemId,
                ],
                [
                    'raw_data' => $rawData,
                    'standardized_data' => $standardizedData,
                    'price' => $price,
                    'last_collected_at' => now(),
                ]
            );

            Log::info('数据库操作执行完成', [
                'operation_type' => $productData->wasRecentlyCreated ? 'CREATE' : 'UPDATE',
                'product_data_id' => $productData->id,
                'was_recently_created' => $productData->wasRecentlyCreated,
                'item_id' => $productData->item_id,
                'monitoring_task_id' => $productData->monitoring_task_id,
                'price' => $productData->price,
                'created_at' => $productData->created_at,
                'updated_at' => $productData->updated_at,
                'last_collected_at' => $productData->last_collected_at,
                'saved_raw_data' => $productData->raw_data,
                'saved_standardized_data' => $productData->standardized_data,
                'raw_data_size_bytes' => strlen(json_encode($productData->raw_data)),
                'standardized_data_size_bytes' => strlen(json_encode($productData->standardized_data))
            ]);

            // 验证保存的数据
            $verificationData = ProductData::find($productData->id);
            Log::info('数据保存验证', [
                'verification_id' => $verificationData->id,
                'verification_item_id' => $verificationData->item_id,
                'verification_monitoring_task_id' => $verificationData->monitoring_task_id,
                'verification_price' => $verificationData->price,
                'verification_raw_data' => $verificationData->raw_data,
                'verification_standardized_data' => $verificationData->standardized_data,
                'verification_last_collected_at' => $verificationData->last_collected_at,
                'data_integrity_check' => [
                    'raw_data_matches' => $verificationData->raw_data === $rawData,
                    'standardized_data_matches' => $verificationData->standardized_data === $standardizedData,
                    'item_id_matches' => $verificationData->item_id === $itemId,
                    'monitoring_task_id_matches' => $verificationData->monitoring_task_id === $monitoringTaskId
                ]
            ]);

            return $productData;
            
        } catch (\Exception $e) {
            Log::error('数据库保存失败', [
                'data_source_id' => $dataSource->id,
                'monitoring_task_id' => $monitoringTaskId,
                'item_id' => $itemId,
                'error_message' => $e->getMessage(),
                'error_file' => $e->getFile(),
                'error_line' => $e->getLine(),
                'error_trace' => $e->getTraceAsString(),
                'failed_raw_data' => $rawData,
                'failed_standardized_data' => $standardizedData
            ]);
            throw $e;
        }
    }

    /**
     * 分发预警检查任务到队列
     *
     * @param ProductData $productData
     * @return void
     */
    private function dispatchAlertChecking(ProductData $productData): void
    {
        try {
            ProcessAlertChecking::dispatch($productData->id)
                ->onQueue('alerts') // 使用专门的队列处理预警
                ->delay(now()->addSeconds(5)); // 延迟5秒执行，确保数据已完全保存

            Log::debug('预警检查任务已分发到队列', [
                'product_data_id' => $productData->id,
            ]);
        } catch (\Exception $e) {
            Log::error('分发预警检查任务失败', [
                'product_data_id' => $productData->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    private function handleException(\Exception $e, int $dataSourceId, string $itemId = null): void
    {
        $context = [
            'data_source_id' => $dataSourceId,
        ];
        if ($itemId) {
            $context['item_id'] = $itemId;
        }

        // 包含更详细的错误信息
        $context['error'] = $e->getMessage();
        $context['trace'] = mb_substr($e->getTraceAsString(), 0, 2000); // 记录部分堆栈跟踪

        Log::error('数据采集与标准化失败', $context);
    }
} 