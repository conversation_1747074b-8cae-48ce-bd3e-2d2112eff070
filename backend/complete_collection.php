<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Services\AlertService;
use App\Services\TaskGroupAlertSummaryService;
use Illuminate\Support\Facades\Log;

// 启动Laravel应用
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "=== 手动完成任务分组采集并发送通知 ===\n\n";

try {
    // 创建服务实例
    $summaryService = new TaskGroupAlertSummaryService();
    $alertService = new AlertService($summaryService);

    // 最新的采集批次ID
    $collectionBatchId = 'task_2_20250626143814_685d5b569f771';
    $monitoringTaskId = 2;

    echo "处理采集批次: {$collectionBatchId}\n";
    echo "监控任务ID: {$monitoringTaskId}\n\n";

    // 检查监控任务是否完成采集
    $isComplete = $summaryService->isMonitoringTaskCollectionComplete($monitoringTaskId, $collectionBatchId);
    echo "监控任务采集是否完成: " . ($isComplete ? '是' : '否') . "\n\n";

    if ($isComplete) {
        // 完成任务分组采集并触发汇总通知
        echo "开始完成任务分组采集...\n";
        $alertService->completeTaskGroupCollection($monitoringTaskId, $collectionBatchId);
        echo "任务分组采集完成！\n\n";
        
        // 检查汇总状态
        echo "检查汇总状态...\n";
        $summaries = \App\Models\TaskGroupAlertSummary::where('collection_batch_id', $collectionBatchId)
            ->get();
            
        foreach ($summaries as $summary) {
            echo "汇总ID: {$summary->id}\n";
            echo "状态: {$summary->status}\n";
            echo "预警数量: {$summary->alert_count}\n";
            echo "通知发送时间: " . ($summary->notification_sent_at ? $summary->notification_sent_at : '未发送') . "\n\n";
        }
    } else {
        echo "监控任务尚未完成采集，无法发送汇总通知\n";
    }

} catch (\Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    echo "堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
}

echo "\n测试完成！\n";
