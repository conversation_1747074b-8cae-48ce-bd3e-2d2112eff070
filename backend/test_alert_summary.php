<?php

/**
 * 预警汇总功能测试脚本
 * 
 * 使用方法：
 * php test_alert_summary.php
 */

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\Alert;
use App\Models\MonitoringTask;
use App\Models\TaskGroup;
use App\Models\ProductData;
use App\Models\ProductSku;
use App\Services\TaskGroupAlertSummaryService;
use App\Services\AlertService;
use Illuminate\Foundation\Application;

// 初始化Laravel应用
$app = new Application(realpath(__DIR__));
$app->singleton(
    Illuminate\Contracts\Http\Kernel::class,
    App\Http\Kernel::class
);
$app->singleton(
    Illuminate\Contracts\Console\Kernel::class,
    App\Console\Kernel::class
);
$app->singleton(
    Illuminate\Contracts\Debug\ExceptionHandler::class,
    App\Exceptions\Handler::class
);

$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "=== 预警汇总功能测试 ===\n\n";

try {
    // 获取服务实例
    $summaryService = app(TaskGroupAlertSummaryService::class);
    $alertService = app(AlertService::class);

    echo "1. 检查测试数据...\n";
    
    // 查找测试任务分组
    $taskGroup = TaskGroup::where('name', '测试任务分组')->first();
    if (!$taskGroup) {
        echo "   未找到测试任务分组，请先运行: php artisan test:task-group-alert-summary --create-test-data\n";
        exit(1);
    }
    
    echo "   找到任务分组: {$taskGroup->name} (ID: {$taskGroup->id})\n";
    
    // 查找监控任务
    $monitoringTask = $taskGroup->monitoringTasks()->first();
    if (!$monitoringTask) {
        echo "   任务分组下没有监控任务\n";
        exit(1);
    }
    
    echo "   找到监控任务: {$monitoringTask->name} (ID: {$monitoringTask->id})\n";

    echo "\n2. 生成采集批次ID...\n";
    $collectionBatchId = $summaryService->generateCollectionBatchId();
    echo "   采集批次ID: {$collectionBatchId}\n";

    echo "\n3. 模拟预警检查过程...\n";
    
    // 获取商品数据
    $productDataList = ProductData::where('monitoring_task_id', $monitoringTask->id)->get();
    echo "   找到 " . $productDataList->count() . " 个商品\n";
    
    foreach ($productDataList as $productData) {
        echo "   处理商品: {$productData->title} (ID: {$productData->item_id})\n";
        
        // 模拟预警检查，调用AlertService的processAlerts方法
        $result = $alertService->processAlerts($productData, $collectionBatchId);
        echo "     预警检查结果: " . ($result ? '有预警' : '无预警') . "\n";
    }

    echo "\n4. 检查汇总状态...\n";
    $summary = $summaryService->getSummary($monitoringTask->id, $collectionBatchId);
    if ($summary) {
        echo "   汇总状态: {$summary->status}\n";
        echo "   预警数量: {$summary->alert_count}\n";
        echo "   商品数量: {$summary->product_count}\n";
        echo "   规则数量: {$summary->rule_count}\n";
    } else {
        echo "   未找到汇总记录\n";
    }

    echo "\n5. 完成汇总并发送通知...\n";
    $summaryService->finalizeSummary($monitoringTask->id, $collectionBatchId);
    echo "   汇总完成\n";

    echo "\n6. 检查最终状态...\n";
    $summary = $summaryService->getSummary($monitoringTask->id, $collectionBatchId);
    if ($summary) {
        echo "   最终状态: {$summary->status}\n";
        echo "   通知发送时间: " . ($summary->notification_sent_at ? $summary->notification_sent_at->format('Y-m-d H:i:s') : '未发送') . "\n";
        
        // 显示汇总数据
        if ($summary->summary_data) {
            echo "\n   汇总数据:\n";
            $summaryData = $summary->summary_data;
            
            if (isset($summaryData['task_group'])) {
                echo "   任务分组: {$summaryData['task_group']['name']}\n";
            }
            
            if (isset($summaryData['rules'])) {
                foreach ($summaryData['rules'] as $ruleType => $ruleData) {
                    echo "   预警规则: {$ruleData['name']} ({$ruleType})\n";
                    echo "     预警商品数: " . count($ruleData['products']) . "\n";
                    
                    foreach ($ruleData['products'] as $productId => $productInfo) {
                        echo "       - {$productInfo['title']} (ID: {$productId})\n";
                        echo "         简报: {$productInfo['summary']}\n";
                    }
                }
            }
        }
    }

    echo "\n=== 测试完成 ===\n";

} catch (Exception $e) {
    echo "测试失败: " . $e->getMessage() . "\n";
    echo "错误详情: " . $e->getTraceAsString() . "\n";
    exit(1);
}
