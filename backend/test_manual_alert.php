<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\ProductData;
use App\Services\AlertService;
use App\Services\TaskGroupAlertSummaryService;
use Illuminate\Support\Facades\Log;

// 启动Laravel应用
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "=== 手动触发预警检测测试 ===\n\n";

try {
    // 创建服务实例
    $summaryService = new TaskGroupAlertSummaryService();
    $alertService = new AlertService($summaryService);
    
    // 获取商品数据
    $productData = ProductData::find(1); // 商品 804460152151
    
    if (!$productData) {
        echo "未找到商品数据 ID: 1\n";
        exit(1);
    }
    
    echo "商品信息:\n";
    echo "- ID: {$productData->id}\n";
    echo "- Item ID: {$productData->item_id}\n";
    echo "- 监控任务ID: {$productData->monitoring_task_id}\n";
    echo "- 创建时间: {$productData->created_at}\n";
    echo "- 最后采集时间: {$productData->last_collected_at}\n";
    
    // 检查SKU数据
    $skus = $productData->skus;
    echo "- SKU数量: {$skus->count()}\n";
    
    if ($skus->count() > 0) {
        echo "\nSKU详情:\n";
        foreach ($skus as $sku) {
            echo "  SKU ID: {$sku->sku_id}\n";
            echo "  价格: {$sku->price}\n";
            echo "  到手价: {$sku->sub_price}\n";
            echo "  官方指导价: {$sku->official_guide_price}\n";
            
            // 计算偏离率
            if ($sku->price > 0) {
                $promotionDeviation = round((($sku->price - $sku->sub_price) / $sku->price) * 100, 2);
                echo "  促销价偏离率: {$promotionDeviation}%\n";
            }
            
            if ($sku->official_guide_price > 0 && $sku->sub_price > 0) {
                $channelDeviation = round((($sku->official_guide_price - $sku->sub_price) / $sku->official_guide_price) * 100, 2);
                echo "  渠道价格偏离率: {$channelDeviation}%\n";
            }
            echo "\n";
        }
    }
    
    echo "开始处理预警...\n";
    
    // 处理预警
    $result = $alertService->processAlerts($productData);
    
    echo "\n预警处理结果:\n";
    echo "- 成功: " . ($result['success'] ? '是' : '否') . "\n";
    echo "- 触发预警数量: " . ($result['alerts_count'] ?? 0) . "\n";
    
    if (isset($result['triggered_alerts']) && !empty($result['triggered_alerts'])) {
        echo "\n触发的预警:\n";
        foreach ($result['triggered_alerts'] as $alert) {
            echo "  - 预警ID: {$alert->id}\n";
            echo "  - 标题: {$alert->title}\n";
            echo "  - 消息: {$alert->message}\n";
            echo "  - 触发值: {$alert->trigger_value}\n";
            echo "  - 阈值: {$alert->threshold_value}\n";
            echo "  - 操作符: {$alert->operator}\n";
            echo "\n";
        }
    }
    
    if (isset($result['error'])) {
        echo "错误: {$result['error']}\n";
    }
    
} catch (Exception $e) {
    echo "发生错误: " . $e->getMessage() . "\n";
    echo "堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
}

echo "\n测试完成！\n";
