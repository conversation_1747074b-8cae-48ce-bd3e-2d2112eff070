# 任务分组预警汇总功能测试指南

## 功能概述

任务分组预警汇总功能将原来的单个商品预警通知改为按任务分组汇总的预警通知，减少通知数量，提高信息的可读性。

### 主要特性

1. **SKU级预警汇总到商品级**：同一商品的多个SKU预警会汇总为一个商品级预警
2. **按任务分组汇总**：同一任务分组下的所有预警会汇总到一个通知中
3. **按预警规则分类**：预警按规则类型分类显示
4. **批次处理**：使用采集批次ID来标识同一轮数据采集的预警

### 通知格式

```
分组 -> 预警规则1 -> 预警商品及简报 -> 预警规则2 -> 预警商品及简报
```

## 测试方法

### 方法1：使用Artisan命令测试

1. **创建测试数据**：
```bash
php artisan test:task-group-alert-summary --create-test-data
```

2. **运行测试**：
```bash
php artisan test:task-group-alert-summary --task-group-id=1
```

### 方法2：使用PHP脚本测试

1. **先创建测试数据**（使用方法1的第一步）

2. **运行测试脚本**：
```bash
cd backend
php test_alert_summary.php
```

## 核心组件

### 1. 数据库表

- `task_group_alert_summaries`：存储任务分组预警汇总信息
- `alerts`：添加了`task_group_alert_summary_id`外键

### 2. 核心服务类

- `TaskGroupAlertSummaryService`：处理预警汇总逻辑
- `AlertService`：修改后支持预警汇总

### 3. 通知类

- `TaskGroupAlertSummaryNotification`：发送汇总预警通知
- `SendTaskGroupAlertNotification`：队列任务处理通知发送

### 4. 队列任务

- `ProcessAlertChecking`：修改后支持采集批次ID
- `ProcessTaskGroupCollectionComplete`：处理任务分组采集完成

## 工作流程

1. **数据采集开始**：生成采集批次ID
2. **商品数据采集**：每个商品采集完成后触发预警检查
3. **预警检查**：将预警添加到汇总中，而不是立即发送通知
4. **任务完成检测**：检测任务分组中所有商品是否完成采集
5. **汇总处理**：完成汇总并发送统一通知

## 配置说明

### 队列配置

确保以下队列正常运行：
- `alerts`：处理预警检查
- `task_groups`：处理任务分组完成检查
- `notifications`：处理通知发送

### 延迟设置

- 预警检查延迟：5秒
- 任务分组完成检查延迟：2分钟
- 预警检查等待时间：30秒

## 故障排除

### 1. 预警汇总不工作

检查以下几点：
- 队列是否正常运行
- 采集批次ID是否正确传递
- 任务完成检测是否正常

### 2. 重复通知

检查：
- 任务分组完成检查是否有重复分发
- 汇总状态是否正确更新

### 3. 通知格式不正确

检查：
- `TaskGroupAlertSummaryNotification`类的实现
- 汇总数据结构是否正确

## 日志查看

相关日志标签：
- `task_group_alert_summary`
- `alert_processing`
- `task_group_collection`

查看日志：
```bash
tail -f storage/logs/laravel.log | grep "task_group"
```

## 数据库查询

查看汇总记录：
```sql
SELECT * FROM task_group_alert_summaries ORDER BY created_at DESC;
```

查看预警记录：
```sql
SELECT * FROM alerts WHERE task_group_alert_summary_id IS NOT NULL ORDER BY created_at DESC;
```

## 性能考虑

1. **批次大小**：建议单个任务分组不超过100个商品
2. **延迟设置**：根据实际情况调整延迟时间
3. **队列处理**：确保队列worker数量足够

## 扩展功能

未来可以考虑的扩展：
1. 支持自定义通知模板
2. 支持多种通知渠道（邮件、短信等）
3. 支持预警级别过滤
4. 支持预警统计报表
