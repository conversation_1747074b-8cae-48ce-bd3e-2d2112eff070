<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Services\DataCollectionService;
use App\Models\DataSource;
use Illuminate\Support\Facades\Log;

// 启动Laravel应用
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

try {
    echo "=== 测试修复后的数据采集服务 ===\n\n";
    
    // 获取数据源
    $dataSource = DataSource::find(3);
    if (!$dataSource) {
        echo "错误：数据源ID=3不存在\n";
        exit(1);
    }
    
    echo "1. 数据源信息:\n";
    echo "   ID: {$dataSource->id}\n";
    echo "   名称: {$dataSource->name}\n";
    echo "   URL: " . ($dataSource->config['url'] ?? 'N/A') . "\n\n";
    
    // 测试清理后的商品ID
    $testItemId = '804460152151'; // 清理后的商品ID
    $monitoringTaskId = 2;
    
    echo "2. 测试参数:\n";
    echo "   原始商品ID: {$testItemId}\n";
    echo "   监控任务ID: {$monitoringTaskId}\n\n";
    
    // 执行数据采集
    echo "3. 开始执行数据采集...\n";
    $dataCollectionService = new DataCollectionService();
    
    $result = $dataCollectionService->collectAndStandardize(
        $dataSource->id,
        $testItemId,
        [],
        $monitoringTaskId
    );
    
    echo "4. 采集结果:\n";
    echo "   成功: " . ($result['success'] ? '是' : '否') . "\n";
    if (isset($result['product_data_id'])) {
        echo "   产品数据ID: {$result['product_data_id']}\n";
    }
    if (isset($result['raw_data'])) {
        echo "   原始数据长度: " . strlen(json_encode($result['raw_data'])) . " 字符\n";
    }
    
    echo "\n=== 测试完成 ===\n";
    
} catch (\Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . "\n";
    echo "行号: " . $e->getLine() . "\n";
    echo "堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
}
