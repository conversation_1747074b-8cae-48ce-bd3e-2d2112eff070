<?php

require_once __DIR__ . '/vendor/autoload.php';

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\User;
use App\Models\AlertRule;
use App\Models\MonitoringTask;
use App\Models\DataSource;
use App\Models\ProductData;
use App\Models\ProductSku;
use App\Services\AlertService;
use Illuminate\Support\Facades\Log;

echo "=== 预警系统测试 ===\n\n";

try {
    // 创建测试用户
    $user = User::firstOrCreate(['email' => '<EMAIL>'], [
        'name' => '测试用户',
        'username' => 'test_user',
        'password' => bcrypt('password'),
    ]);

    echo "1. 创建测试数据源...\n";
    $dataSource = DataSource::firstOrCreate(['name' => '测试数据源'], [
        'type' => 'api',
        'config' => ['url' => 'http://test.com'],
        'owner_id' => $user->id,
    ]);

    echo "2. 创建测试预警规则...\n";
    
    // 创建促销价偏离率预警规则
    $promotionRule = AlertRule::create([
        'user_id' => $user->id,
        'name' => '促销价偏离率测试规则',
        'description' => '当促销价偏离率大于10%时触发预警',
        'rule_type' => ['promotion_price_deviation'],
        'conditions' => [
            'promotion_price_deviation' => [
                'operator' => '>',
                'threshold' => 10
            ]
        ],
        'severity' => 'medium',
        'priority' => 'normal',
        'notification_method' => ['email'],
        'status' => 1,
    ]);
    
    // 创建渠道价格偏离率预警规则
    $channelRule = AlertRule::create([
        'user_id' => $user->id,
        'name' => '渠道价格偏离率测试规则',
        'description' => '当渠道价格偏离率大于15%时触发预警',
        'rule_type' => ['channel_price_deviation'],
        'conditions' => [
            'channel_price_deviation' => [
                'operator' => '>',
                'threshold' => 15
            ]
        ],
        'severity' => 'high',
        'priority' => 'high',
        'notification_method' => ['email'],
        'status' => 1,
    ]);

    echo "3. 创建监控任务...\n";
    $monitoringTask = MonitoringTask::create([
        'name' => '测试监控任务',
        'user_id' => $user->id,
        'data_source_id' => $dataSource->id,
        'status' => 'active',
        'monitor_fields' => ['price', 'sub_price', 'state'], // 添加必需字段
        'alert_rule_ids' => [$promotionRule->id, $channelRule->id],
        'target_products' => [
            [
                'product_id' => 'TEST001',
                'official_guide_price' => 100.00,
                'remark' => '测试商品1'
            ],
            [
                'product_id' => 'TEST002',
                'official_guide_price' => 200.00,
                'remark' => '测试商品2'
            ]
        ],
    ]);

    echo "4. 创建测试商品数据...\n";
    
    // 测试商品1：促销价偏离率超过阈值
    $productData1 = ProductData::create([
        'monitoring_task_id' => $monitoringTask->id,
        'item_id' => 'TEST001',
        'title' => '测试商品1',
        'price' => 100.00,
        'state' => 1,
        'has_sku' => true,
        'standardized_data' => [
            'title' => '测试商品1',
            'Price' => 100.00,
            'subPrice' => 75.00, // 25%的促销，应该触发预警
        ],
        'last_collected_at' => now(),
    ]);

    // 为商品1创建SKU
    $sku1 = ProductSku::create([
        'product_data_id' => $productData1->id,
        'sku_id' => 'SKU001',
        'name' => '规格1',
        'price' => 100.00,
        'sub_price' => 75.00, // 25%促销偏离率
        'official_guide_price' => 100.00,
        'quantity' => 50,
    ]);

    // 测试商品2：渠道价格偏离率超过阈值
    $productData2 = ProductData::create([
        'monitoring_task_id' => $monitoringTask->id,
        'item_id' => 'TEST002',
        'title' => '测试商品2',
        'price' => 150.00,
        'state' => 1,
        'has_sku' => true,
        'standardized_data' => [
            'title' => '测试商品2',
            'Price' => 150.00,
            'subPrice' => 160.00, // 渠道价格偏离率约为20%，应该触发预警
        ],
        'last_collected_at' => now(),
    ]);

    // 为商品2创建SKU
    $sku2 = ProductSku::create([
        'product_data_id' => $productData2->id,
        'sku_id' => 'SKU002',
        'name' => '规格2',
        'price' => 150.00,
        'sub_price' => 160.00, // 渠道价格偏离率约为20%
        'official_guide_price' => 200.00, // 官方指导价
        'quantity' => 30,
    ]);

    echo "5. 测试偏离率计算...\n";
    
    // 测试促销价偏离率计算
    $promotionRate1 = $sku1->promotion_deviation_rate;
    echo "商品1 SKU促销价偏离率: {$promotionRate1}% (应该为25%)\n";
    
    $promotionRate2 = $sku2->promotion_deviation_rate;
    echo "商品2 SKU促销价偏离率: {$promotionRate2}% (应该为-6.67%)\n";
    
    // 测试渠道价格偏离率计算
    $channelRate1 = $sku1->channel_price_deviation_rate;
    echo "商品1 SKU渠道价格偏离率: {$channelRate1}% (应该为25%)\n";
    
    $channelRate2 = $sku2->channel_price_deviation_rate;
    echo "商品2 SKU渠道价格偏离率: {$channelRate2}% (应该为20%)\n";

    echo "\n6. 测试预警触发...\n";
    
    $alertService = new AlertService();
    
    // 测试商品1的预警
    echo "测试商品1预警检查...\n";
    $result1 = $alertService->processAlerts($productData1);
    echo "商品1预警结果: " . json_encode($result1, JSON_UNESCAPED_UNICODE) . "\n";
    
    // 测试商品2的预警
    echo "测试商品2预警检查...\n";
    $result2 = $alertService->processAlerts($productData2);
    echo "商品2预警结果: " . json_encode($result2, JSON_UNESCAPED_UNICODE) . "\n";

    echo "\n7. 检查生成的预警记录...\n";
    $alerts = \App\Models\Alert::where('monitoring_task_id', $monitoringTask->id)->get();
    echo "共生成了 {$alerts->count()} 条预警记录:\n";
    
    foreach ($alerts as $alert) {
        echo "- 预警类型: {$alert->alert_type}\n";
        echo "  预警标题: {$alert->title}\n";
        echo "  预警消息: {$alert->message}\n";
        echo "  触发数据: " . json_encode($alert->trigger_data, JSON_UNESCAPED_UNICODE) . "\n";
        echo "  创建时间: {$alert->created_at}\n\n";
    }

    echo "=== 测试完成 ===\n";
    echo "预期结果：\n";
    echo "- 商品1应该触发促销价偏离率预警（25% > 10%）\n";
    echo "- 商品2应该触发渠道价格偏离率预警（20% > 15%）\n";
    echo "- 总共应该生成2条预警记录\n";

} catch (\Exception $e) {
    echo "测试失败: " . $e->getMessage() . "\n";
    echo "错误文件: " . $e->getFile() . ":" . $e->getLine() . "\n";
    echo "错误堆栈: " . $e->getTraceAsString() . "\n";
} 