<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\DataSource;

// 启动Laravel应用
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

try {
    echo "=== 检查数据源配置 ===\n\n";
    
    $dataSource = DataSource::find(3);
    
    if (!$dataSource) {
        echo "数据源ID 3 不存在\n";
        exit(1);
    }
    
    echo "数据源名称: {$dataSource->name}\n";
    echo "数据源ID: {$dataSource->id}\n\n";
    
    echo "字段映射配置:\n";
    $fieldMapping = $dataSource->field_mapping;
    
    if (isset($fieldMapping['fields'])) {
        echo "主要字段映射:\n";
        foreach ($fieldMapping['fields'] as $key => $value) {
            if (is_array($value)) {
                echo "  {$key} => " . json_encode($value) . "\n";
            } else {
                echo "  {$key} => {$value}\n";
            }
        }
    }
    
    if (isset($fieldMapping['fields']['skus'])) {
        echo "\nSKU字段映射:\n";
        $skuMapping = $fieldMapping['fields']['skus'];
        if (is_array($skuMapping) && isset($skuMapping['fields'])) {
            foreach ($skuMapping['fields'] as $key => $value) {
                echo "  {$key} => {$value}\n";
            }
        } else {
            echo "  SKU映射配置格式异常: " . json_encode($skuMapping) . "\n";
        }
    }
    
    echo "\n完整字段映射配置:\n";
    echo json_encode($fieldMapping, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
    
} catch (\Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . "\n";
    echo "行号: " . $e->getLine() . "\n";
}
