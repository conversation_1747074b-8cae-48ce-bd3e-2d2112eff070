<?php

require_once __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\User;
use App\Models\AlertRule;

// 获取或创建测试用户
$user = User::first();
if (!$user) {
    echo "No users found. Creating a test user...\n";
    $user = User::create([
        'username' => 'testuser',
        'name' => 'Test User',
        'email' => '<EMAIL>',
        'password' => bcrypt('password'),
        'status' => 1
    ]);
}

echo "Creating test alert rules...\n";

// 删除现有的测试数据
AlertRule::where('user_id', $user->id)->delete();

// 创建测试预警规则
$rule1 = AlertRule::create([
    'user_id' => $user->id,
    'name' => '促销价偏离率预警',
    'description' => '监控促销价格偏离率',
    'rule_type' => ['promotion_price_deviation'],
    'conditions' => [
        'promotion_price_deviation' => [
            'operator' => '>',
            'threshold' => 15
        ]
    ],
    'severity' => 'medium',
    'priority' => 'normal',
    'notification_method' => ['email'],
    'status' => true
]);

$rule2 = AlertRule::create([
    'user_id' => $user->id,
    'name' => '渠道价格偏离率预警',
    'description' => '监控渠道价格偏离率',
    'rule_type' => ['channel_price_deviation'],
    'conditions' => [
        'channel_price_deviation' => [
            'operator' => '>',
            'threshold' => 20
        ]
    ],
    'severity' => 'high',
    'priority' => 'high',
    'notification_method' => ['email', 'system'],
    'status' => true
]);

$rule3 = AlertRule::create([
    'user_id' => $user->id,
    'name' => '综合监控预警',
    'description' => '同时监控促销价和渠道价偏离率',
    'rule_type' => ['promotion_price_deviation', 'channel_price_deviation'],
    'conditions' => [
        'promotion_price_deviation' => [
            'operator' => '>',
            'threshold' => 10
        ],
        'channel_price_deviation' => [
            'operator' => '>',
            'threshold' => 15
        ]
    ],
    'severity' => 'critical',
    'priority' => 'urgent',
    'notification_method' => ['email', 'system'],
    'status' => true
]);

$rule4 = AlertRule::create([
    'user_id' => $user->id,
    'name' => '上下架状态预警',
    'description' => '监控商品上下架状态变化',
    'rule_type' => ['listing_status_change'],
    'conditions' => [],
    'severity' => 'low',
    'priority' => 'normal',
    'notification_method' => ['system'],
    'status' => true
]);

echo "Created " . AlertRule::count() . " alert rules.\n";
echo "Test data created successfully!\n";
