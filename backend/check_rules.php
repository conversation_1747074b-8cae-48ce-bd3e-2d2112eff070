<?php

require_once __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\AlertRule;

$rules = AlertRule::all();
echo "Alert Rules in database:\n";
foreach ($rules as $rule) {
    echo "ID: {$rule->id}, Name: {$rule->name}, User ID: {$rule->user_id}\n";
}
echo "Total: " . $rules->count() . " rules\n";
