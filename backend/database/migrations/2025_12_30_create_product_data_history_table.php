<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_data_history', function (Blueprint $table) {
            $table->id();
            $table->foreignId('monitoring_task_id')->constrained('monitoring_tasks')->onDelete('cascade')->comment('监控任务ID');
            $table->string('item_id', 100)->comment('外部商品/数据唯一ID');
            $table->json('standardized_data')->comment('根据映射规则标准化后的数据');
            $table->timestamp('collected_at')->comment('数据采集时间');
            
            // 复制product_data表的主要字段
            $table->string('code')->nullable()->comment('状态码');
            $table->string('title')->nullable()->comment('商品标题');
            $table->string('product_image')->nullable()->comment('商品主图URL');
            $table->decimal('price', 10, 2)->nullable()->comment('商品价格');
            $table->decimal('lowest_price', 10, 2)->nullable()->comment('最低到手价');
            $table->decimal('highest_price', 10, 2)->nullable()->comment('最高到手价');
            $table->decimal('min_hand_price', 10, 2)->nullable()->comment('最低到手价');
            $table->decimal('max_hand_price', 10, 2)->nullable()->comment('最高到手价');
            $table->integer('stock')->nullable()->comment('库存数量');
            $table->integer('sales')->nullable()->comment('商品销量');
            $table->integer('comment_count')->nullable()->comment('商品评论数');
            $table->tinyInteger('state')->nullable()->comment('商品状态：1-上架，0-下架');
            $table->boolean('has_sku')->default(false)->comment('是否有SKU：1-是，0-否');
            $table->string('item_type')->nullable()->comment('商品平台类型');
            $table->string('category_id')->nullable()->comment('类目ID');
            $table->string('category_path')->nullable()->comment('类目全路径');
            $table->string('shop_id')->nullable()->comment('店铺ID');
            $table->string('shop_name')->nullable()->comment('店铺名称');
            $table->json('props')->nullable()->comment('商品参数信息');
            $table->json('promotion')->nullable()->comment('促销信息');
            $table->string('delivery_location')->nullable()->comment('发货地');
            $table->string('product_url')->nullable()->comment('商品链接');
            
            $table->timestamps();

            // 索引优化
            $table->index(['monitoring_task_id', 'item_id', 'collected_at'], 'product_history_main_index');
            $table->index(['item_id', 'collected_at'], 'product_history_item_time_index');
            $table->index('collected_at', 'product_history_time_index');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_data_history');
    }
}; 