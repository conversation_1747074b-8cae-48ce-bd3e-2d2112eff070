<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('product_data', function (Blueprint $table) {
            // Re-sync with what the user wants.
            // Let's add columns that should exist for a product.
            
            if (!Schema::hasColumn('product_data', 'code')) {
                $table->string('code')->nullable();
            }
            if (!Schema::hasColumn('product_data', 'monitoring_task_id')) {
                 $table->foreignId('monitoring_task_id')->nullable()->constrained('monitoring_tasks')->onDelete('cascade');
            }
            if (!Schema::hasColumn('product_data', 'title')) {
                 $table->string('title')->nullable();
            }
            if (!Schema::hasColumn('product_data', 'product_image')) {
                $table->string('product_image')->nullable()->comment('商品主图URL');
            }
            if (!Schema::hasColumn('product_data', 'price')) {
                $table->decimal('price', 10, 2)->nullable();
            }
            if (!Schema::hasColumn('product_data', 'lowest_price')) {
                $table->decimal('lowest_price', 10, 2)->nullable()->comment('最低到手价');
            }
            if (!Schema::hasColumn('product_data', 'highest_price')) {
                $table->decimal('highest_price', 10, 2)->nullable()->comment('最高到手价');
            }
             if (!Schema::hasColumn('product_data', 'stock')) {
                $table->integer('stock')->nullable();
            }
            if (!Schema::hasColumn('product_data', 'sales')) {
                $table->integer('sales')->nullable()->comment('商品销量');
            }
            if (!Schema::hasColumn('product_data', 'comment_count')) {
                $table->integer('comment_count')->nullable()->comment('商品评论数');
            }
            if (!Schema::hasColumn('product_data', 'state')) {
                $table->tinyInteger('state')->nullable()->comment('商品状态：1-上架，0-下架');
            }
            if (!Schema::hasColumn('product_data', 'has_sku')) {
                $table->boolean('has_sku')->default(false)->comment('是否有SKU：1-是，0-否');
            }
            if (!Schema::hasColumn('product_data', 'item_type')) {
                $table->string('item_type')->nullable()->comment('商品平台类型');
            }
            if (!Schema::hasColumn('product_data', 'category_id')) {
                $table->string('category_id')->nullable()->comment('类目ID');
            }
            if (!Schema::hasColumn('product_data', 'category_path')) {
                $table->string('category_path')->nullable()->comment('类目全路径');
            }
            if (!Schema::hasColumn('product_data', 'shop_id')) {
                $table->string('shop_id')->nullable()->comment('店铺ID');
            }
            if (!Schema::hasColumn('product_data', 'shop_name')) {
                $table->string('shop_name')->nullable()->comment('店铺名称');
            }
            if (!Schema::hasColumn('product_data', 'props')) {
                $table->json('props')->nullable()->comment('商品参数信息');
            }
            if (!Schema::hasColumn('product_data', 'promotion_info')) {
                $table->json('promotion_info')->nullable()->comment('优惠信息列表');
            }
            if (!Schema::hasColumn('product_data', 'delivery_location')) {
                $table->string('delivery_location')->nullable()->comment('发货地');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('product_data', function (Blueprint $table) {
            $columnsToDrop = [
                'code', 'monitoring_task_id', 'title', 'product_image', 'price', 'lowest_price', 'highest_price', 'stock', 'sales',
                'comment_count', 'state', 'has_sku', 'item_type', 'category_id',
                'category_path', 'shop_id', 'shop_name', 'props', 'promotion_info',
                'delivery_location'
            ];
            
            // Need to check for foreign key existence before dropping
            $foreignKeys = Schema::getConnection()->getDoctrineSchemaManager()->listTableForeignKeys('product_data');
            $hasForeignKey = collect($foreignKeys)->contains(function ($fk) {
                return $fk->getForeignTableName() === 'monitoring_tasks' && in_array('monitoring_task_id', $fk->getLocalColumns());
            });

            if ($hasForeignKey) {
                 $table->dropForeign(['monitoring_task_id']);
            }

            $existingColumns = Schema::getColumnListing('product_data');
            $columns = array_intersect($columnsToDrop, $existingColumns);
            if (!empty($columns)) {
                $table->dropColumn($columns);
            }
        });
    }
}; 