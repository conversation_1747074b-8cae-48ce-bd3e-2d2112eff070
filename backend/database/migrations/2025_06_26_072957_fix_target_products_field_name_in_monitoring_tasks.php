<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('monitoring_tasks', function (Blueprint $table) {
            // 将 monitored_products 字段重命名为 target_products
            $table->renameColumn('monitored_products', 'target_products');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('monitoring_tasks', function (Blueprint $table) {
            // 回滚时将 target_products 字段重命名回 monitored_products
            $table->renameColumn('target_products', 'monitored_products');
        });
    }
};
