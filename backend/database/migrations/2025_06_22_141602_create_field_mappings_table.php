<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('field_mappings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('data_source_id')->constrained('data_sources')->onDelete('cascade')->comment('数据源ID');
            $table->string('source_field', 100)->comment('源字段名');
            $table->string('target_field', 100)->comment('目标字段名(标准化字段)');
            $table->string('field_type', 50)->default('string')->comment('字段类型：string,number,boolean,date,json');
            $table->text('transform_rule')->nullable()->comment('转换规则(JSON格式)');
            $table->string('default_value', 255)->nullable()->comment('默认值');
            $table->tinyInteger('is_required')->default(0)->comment('是否必填：1-是，0-否');
            $table->text('validation_rule')->nullable()->comment('验证规则');
            $table->text('description')->nullable()->comment('字段描述');
            $table->integer('sort_order')->default(0)->comment('排序');
            $table->tinyInteger('status')->default(1)->comment('状态：1-启用，0-禁用');
            $table->timestamps();
            
            $table->unique(['data_source_id', 'source_field', 'target_field']);
            $table->index(['data_source_id', 'status']);
            $table->index('target_field');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('field_mappings');
    }
};
