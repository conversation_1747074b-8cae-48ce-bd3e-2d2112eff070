<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('product_data', function (Blueprint $table) {
            $table->string('collection_batch_id', 100)->nullable()->after('monitoring_task_id')->comment('数据采集批次ID');
            $table->index('collection_batch_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('product_data', function (Blueprint $table) {
            $table->dropIndex(['collection_batch_id']);
            $table->dropColumn('collection_batch_id');
        });
    }
};
