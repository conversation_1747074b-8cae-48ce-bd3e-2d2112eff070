<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('monitoring_tasks', function (Blueprint $table) {
            // 添加定时执行相关字段
            $table->string('schedule_type')->nullable()->comment('执行类型：daily, specific_date, weekly');
            $table->time('execution_time')->nullable()->comment('执行时间');
            $table->json('execution_dates')->nullable()->comment('指定执行日期列表');
            $table->json('execution_weekdays')->nullable()->comment('指定执行星期列表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('monitoring_tasks', function (Blueprint $table) {
            $table->dropColumn([
                'schedule_type',
                'execution_time',
                'execution_dates',
                'execution_weekdays'
            ]);
        });
    }
};
