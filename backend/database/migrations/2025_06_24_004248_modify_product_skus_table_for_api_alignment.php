<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('product_skus', function (Blueprint $table) {
            // 首先重命名列以匹配服务层逻辑
            $table->renameColumn('sub_price', 'promotion_price');
            $table->renameColumn('sub_price_title', 'promotion_price_title');

            // 添加新的 image_url 列
            $table->string('image_url')->nullable()->after('quantity')->comment('SKU图片链接');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('product_skus', function (Blueprint $table) {
            // 撤销上面的操作
            $table->dropColumn('image_url');
            $table->renameColumn('promotion_price', 'sub_price');
            $table->renameColumn('promotion_price_title', 'sub_price_title');
        });
    }
};
