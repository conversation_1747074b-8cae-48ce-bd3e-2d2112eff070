<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class RefactorDataSourcesTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('data_sources', function (Blueprint $table) {
            // Add a new unified config column
            $table->json('config')->nullable()->after('type');

            // Drop old individual config columns
            if (Schema::hasColumn('data_sources', 'api_base_url')) {
                $table->dropColumn('api_base_url');
            }
            if (Schema::hasColumn('data_sources', 'api_config')) {
                $table->dropColumn('api_config');
            }
            if (Schema::hasColumn('data_sources', 'token')) {
                $table->dropColumn('token');
            }
            if (Schema::hasColumn('data_sources', 'headers')) {
                $table->dropColumn('headers');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('data_sources', function (Blueprint $table) {
            // Re-add old columns
            $table->string('api_base_url', 255)->nullable();
            $table->json('api_config')->nullable();
            $table->string('token', 255)->nullable();
            $table->json('headers')->nullable();

            // Drop the new unified config column
            $table->dropColumn('config');
        });
    }
} 