<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('product_skus', function (Blueprint $table) {
            // Revert field names to align with the specification and API
            if (Schema::hasColumn('product_skus', 'promotion_price')) {
                $table->renameColumn('promotion_price', 'sub_price');
            }
            if (Schema::hasColumn('product_skus', 'promotion_price_title')) {
                $table->renameColumn('promotion_price_title', 'sub_price_title');
            }
            if (Schema::hasColumn('product_skus', 'stock')) {
                $table->renameColumn('stock', 'quantity');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('product_skus', function (Blueprint $table) {
            // Revert back to the incorrect names if needed
            if (Schema::hasColumn('product_skus', 'sub_price')) {
                $table->renameColumn('sub_price', 'promotion_price');
            }
            if (Schema::hasColumn('product_skus', 'sub_price_title')) {
                $table->renameColumn('sub_price_title', 'promotion_price_title');
            }
             if (Schema::hasColumn('product_skus', 'quantity')) {
                $table->renameColumn('quantity', 'stock');
            }
        });
    }
};
