<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('alert_rules', function (Blueprint $table) {
            // 先删除旧字段
            $table->dropColumn(['rule_type', 'notification_method']);
        });

        Schema::table('alert_rules', function (Blueprint $table) {
            // 重新创建为JSON类型
            $table->json('rule_type')->after('description');
            $table->json('notification_method')->after('priority');

            // 添加conditions字段
            $table->json('conditions')->nullable()->after('rule_type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('alert_rules', function (Blueprint $table) {
            // 删除JSON字段
            $table->dropColumn(['rule_type', 'notification_method', 'conditions']);
        });

        Schema::table('alert_rules', function (Blueprint $table) {
            // 重新创建为字符串类型
            $table->string('rule_type', 50)->after('description');
            $table->string('notification_method', 20)->after('priority');
        });
    }
};
