<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('roles', function (Blueprint $table) {
            $table->id();
            $table->string('name', 50)->unique()->comment('角色名称');
            $table->string('display_name', 100)->comment('显示名称');
            $table->text('description')->nullable()->comment('角色描述');
            $table->tinyInteger('status')->default(1)->comment('状态：1-启用，0-禁用');
            $table->integer('sort_order')->default(0)->comment('排序');
            $table->timestamps();
            
            $table->index(['status', 'sort_order']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('roles');
    }
};
