<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('task_group_alert_summaries', function (Blueprint $table) {
            $table->id();
            $table->foreignId('task_group_id')->nullable()->constrained('task_groups')->onDelete('cascade')->comment('任务分组ID');
            $table->foreignId('monitoring_task_id')->nullable()->constrained('monitoring_tasks')->onDelete('cascade')->comment('监控任务ID');
            $table->string('collection_batch_id', 100)->comment('采集批次ID，用于标识同一批次的数据采集');
            $table->json('summary_data')->comment('预警汇总数据');
            $table->integer('alert_count')->default(0)->comment('预警总数');
            $table->integer('product_count')->default(0)->comment('涉及商品数');
            $table->integer('rule_count')->default(0)->comment('涉及规则数');
            $table->enum('status', ['collecting', 'ready', 'sent', 'failed'])->default('collecting')->comment('状态');
            $table->timestamp('notification_sent_at')->nullable()->comment('通知发送时间');
            $table->timestamps();
            
            // 索引
            $table->index(['task_group_id', 'status']);
            $table->index(['monitoring_task_id', 'status']);
            $table->index('collection_batch_id');
            $table->index('status');
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('task_group_alert_summaries');
    }
};
