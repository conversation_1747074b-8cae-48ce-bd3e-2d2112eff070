<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_skus', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('product_data_id');
            $table->string('sku_id')->comment('SKU唯一标识符');
            $table->string('name')->comment('SKU名称（规格组合）');
            $table->decimal('price', 10, 2)->comment('SKU原价');
            $table->decimal('sub_price', 10, 2)->nullable()->comment('SKU券后价（实际到手价）');
            $table->string('sub_price_title')->nullable()->comment('券后价名称');
            $table->integer('quantity')->comment('SKU库存数量');
            $table->timestamps();

            $table->foreign('product_data_id')->references('id')->on('product_data')->onDelete('cascade');
            $table->index('sku_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_skus');
    }
}; 