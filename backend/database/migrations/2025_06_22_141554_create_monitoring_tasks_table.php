<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('monitoring_tasks', function (Blueprint $table) {
            $table->id();
            $table->string('name', 100)->comment('任务名称');
            $table->text('description')->nullable()->comment('任务描述');
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade')->comment('创建者ID');
            $table->foreignId('task_group_id')->nullable()->constrained('task_groups')->onDelete('set null')->comment('任务分组ID');
            $table->foreignId('data_source_id')->constrained('data_sources')->onDelete('cascade')->comment('数据源ID');
            
            // 监控配置
            $table->json('target_products')->comment('目标商品配置(商品ID列表或搜索条件)');
            $table->json('monitor_fields')->comment('监控字段配置');
            $table->string('frequency_type', 20)->default('interval')->comment('频率类型：interval,cron');
            $table->integer('frequency_value')->nullable()->comment('频率值(分钟数)');
            $table->string('cron_expression', 100)->nullable()->comment('Cron表达式');
            
            // 任务状态
            $table->string('status', 20)->default('pending')->comment('任务状态：pending,running,paused,stopped,failed');
            $table->timestamp('last_run_at')->nullable()->comment('最后执行时间');
            $table->timestamp('next_run_at')->nullable()->comment('下次执行时间');
            $table->integer('run_count')->default(0)->comment('执行次数');
            $table->integer('success_count')->default(0)->comment('成功次数');
            $table->integer('failed_count')->default(0)->comment('失败次数');
            $table->text('last_error')->nullable()->comment('最后错误信息');
            
            // 数据统计
            $table->integer('total_products')->default(0)->comment('监控商品总数');
            $table->integer('active_products')->default(0)->comment('活跃商品数');
            $table->integer('alert_count')->default(0)->comment('预警次数');
            
            // 配置选项
            $table->tinyInteger('auto_start')->default(1)->comment('自动启动：1-是，0-否');
            $table->tinyInteger('notify_on_error')->default(1)->comment('错误通知：1-是，0-否');
            $table->json('notification_config')->nullable()->comment('通知配置');
            
            $table->timestamps();
            
            // 索引
            $table->index(['user_id', 'status']);
            $table->index(['task_group_id']);
            $table->index(['data_source_id']);
            $table->index(['status', 'next_run_at']);
            $table->index(['last_run_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('monitoring_tasks');
    }
};
