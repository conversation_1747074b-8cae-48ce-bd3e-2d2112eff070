<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 获取所有监控任务
        $tasks = DB::table('monitoring_tasks')->get();
        
        foreach ($tasks as $task) {
            if ($task->target_products) {
                $targetProducts = json_decode($task->target_products, true);
                
                if (is_array($targetProducts)) {
                    $newFormat = [];
                    
                    foreach ($targetProducts as $product) {
                        if (is_string($product)) {
                            // 旧格式：字符串数组 -> 新格式：对象数组
                            $newFormat[] = [
                                'product_id' => $product,
                                'official_guide_price' => null,
                                'remark' => ''
                            ];
                        } else {
                            // 已经是新格式，保持不变
                            $newFormat[] = [
                                'product_id' => $product['product_id'] ?? '',
                                'official_guide_price' => $product['official_guide_price'] ?? null,
                                'remark' => $product['remark'] ?? ''
                            ];
                        }
                    }
                    
                    // 更新数据库
                    DB::table('monitoring_tasks')
                        ->where('id', $task->id)
                        ->update([
                            'target_products' => json_encode($newFormat),
                            'updated_at' => now()
                        ]);
                }
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // 回滚时将新格式转换回旧格式
        $tasks = DB::table('monitoring_tasks')->get();
        
        foreach ($tasks as $task) {
            if ($task->target_products) {
                $targetProducts = json_decode($task->target_products, true);
                
                if (is_array($targetProducts)) {
                    $oldFormat = [];
                    
                    foreach ($targetProducts as $product) {
                        if (is_array($product) && isset($product['product_id'])) {
                            // 新格式 -> 旧格式：只保留product_id
                            $oldFormat[] = $product['product_id'];
                        } else if (is_string($product)) {
                            // 已经是旧格式
                            $oldFormat[] = $product;
                        }
                    }
                    
                    // 更新数据库
                    DB::table('monitoring_tasks')
                        ->where('id', $task->id)
                        ->update([
                            'target_products' => json_encode($oldFormat),
                            'updated_at' => now()
                        ]);
                }
            }
        }
    }
};
