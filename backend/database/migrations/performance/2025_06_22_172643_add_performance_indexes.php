<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('product_data', function (Blueprint $table) {
            $table->index(['monitoring_task_id', 'created_at'], 'product_data_task_id_created_at_idx');
            $table->index('item_id', 'product_data_item_id_idx');
            $table->index('price', 'product_data_price_idx');
        });

        Schema::table('alerts', function (Blueprint $table) {
            $table->index('monitoring_task_id', 'alerts_monitoring_task_id_idx');
            $table->index('alert_rule_id', 'alerts_alert_rule_id_idx');
            $table->index('status', 'alerts_status_idx');
            $table->index('created_at', 'alerts_created_at_idx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('product_data', function (Blueprint $table) {
            $table->dropIndex('product_data_task_id_created_at_idx');
            $table->dropIndex('product_data_item_id_idx');
            $table->dropIndex('product_data_price_idx');
        });

        Schema::table('alerts', function (Blueprint $table) {
            $table->dropIndex('alerts_monitoring_task_id_idx');
            $table->dropIndex('alerts_alert_rule_id_idx');
            $table->dropIndex('alerts_status_idx');
            $table->dropIndex('alerts_created_at_idx');
        });
    }
};
