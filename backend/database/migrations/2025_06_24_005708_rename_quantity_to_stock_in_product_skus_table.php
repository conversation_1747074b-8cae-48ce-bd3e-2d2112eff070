<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('product_skus', function (Blueprint $table) {
            // 将 quantity 列重命名为 stock，以匹配应用层代码
            if (Schema::hasColumn('product_skus', 'quantity')) {
                $table->renameColumn('quantity', 'stock');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('product_skus', function (Blueprint $table) {
            // 回滚操作，将 stock 重命名回 quantity
            if (Schema::hasColumn('product_skus', 'stock')) {
                $table->renameColumn('stock', 'quantity');
            }
        });
    }
};
