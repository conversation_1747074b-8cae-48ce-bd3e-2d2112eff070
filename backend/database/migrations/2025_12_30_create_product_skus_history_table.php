<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_skus_history', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_data_history_id')->constrained('product_data_history')->onDelete('cascade')->comment('历史商品数据ID');
            $table->string('sku_id')->comment('SKU唯一标识符');
            $table->string('name')->comment('SKU名称（规格组合）');
            $table->decimal('price', 10, 2)->comment('SKU原价');
            $table->decimal('sub_price', 10, 2)->nullable()->comment('SKU券后价（实际到手价）');
            $table->string('sub_price_title')->nullable()->comment('券后价名称');
            $table->integer('quantity')->comment('SKU库存数量');
            $table->string('image_url')->nullable()->comment('SKU图片URL');
            $table->timestamp('collected_at')->comment('数据采集时间');
            $table->timestamps();

            // 索引优化
            $table->index(['product_data_history_id', 'sku_id'], 'sku_history_main_index');
            $table->index(['sku_id', 'collected_at'], 'sku_history_time_index');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_skus_history');
    }
}; 