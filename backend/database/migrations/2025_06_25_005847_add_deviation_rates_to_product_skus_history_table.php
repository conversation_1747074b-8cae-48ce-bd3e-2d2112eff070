<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('product_skus_history', function (Blueprint $table) {
            $table->decimal('official_guide_price', 10, 2)->nullable()->after('image_url');
            $table->decimal('promotion_deviation_rate', 8, 2)->nullable()->after('official_guide_price');
            $table->decimal('channel_price_deviation_rate', 8, 2)->nullable()->after('promotion_deviation_rate');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('product_skus_history', function (Blueprint $table) {
            $table->dropColumn(['official_guide_price', 'promotion_deviation_rate', 'channel_price_deviation_rate']);
        });
    }
};
