<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('alert_rules', function (Blueprint $table) {
            // 移除不需要的字段
            $columnsToRemove = [
                'monitoring_field',
                'cooldown_minutes',
                'max_alerts_per_hour',
                'max_alerts_per_day',
                'email_notification',
                'data_update_exception',
                'monitoring_task_id',
                'notification_channels',
                'recipients',
                'email_recipients',
                'webhook_url',
                'slack_webhook',
                'teams_webhook'
            ];
            
            foreach ($columnsToRemove as $column) {
                if (Schema::hasColumn('alert_rules', $column)) {
                    $table->dropColumn($column);
                }
            }
            
            // 确保必要字段存在且可为空
            if (!Schema::hasColumn('alert_rules', 'target_field')) {
                $table->string('target_field', 100)->nullable()->comment('目标字段');
            } else {
                $table->string('target_field', 100)->nullable()->change();
            }
            
            if (!Schema::hasColumn('alert_rules', 'operator')) {
                $table->string('operator', 20)->nullable()->comment('操作符：>,<,>=,<=,=,!=');
            } else {
                $table->string('operator', 20)->nullable()->change();
            }
            
            if (!Schema::hasColumn('alert_rules', 'threshold_values')) {
                $table->json('threshold_values')->nullable()->comment('阈值配置');
            } else {
                $table->json('threshold_values')->nullable()->change();
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('alert_rules', function (Blueprint $table) {
            // 如果需要回滚，可以重新添加这些字段
            $table->string('monitoring_field', 100)->nullable();
            $table->integer('cooldown_minutes')->default(5);
            $table->integer('max_alerts_per_hour')->default(10);
            $table->integer('max_alerts_per_day')->default(100);
            $table->boolean('email_notification')->default(true);
            $table->boolean('data_update_exception')->default(false);
        });
    }
};
