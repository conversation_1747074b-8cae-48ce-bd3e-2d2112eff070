<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('data_sources', function (Blueprint $table) {
            $table->id();
            $table->string('name', 100)->comment('数据源名称');
            $table->string('type', 50)->comment('数据源类型：taobao,jd,tmall等');
            $table->text('description')->nullable()->comment('数据源描述');
            $table->string('api_base_url', 255)->comment('API基础URL');
            $table->string('api_endpoint_template')->nullable()->comment('API端点模板，支持 {item_id} 占位符');
            $table->string('api_method', 10)->default('GET')->comment('API请求方法');
            $table->text('api_config')->nullable()->comment('API配置信息(JSON格式)');
            $table->string('token', 255)->nullable()->comment('访问令牌');
            $table->json('headers')->nullable()->comment('请求头配置');
            $table->json('default_params')->nullable()->comment('默认参数');
            $table->integer('rate_limit')->default(60)->comment('频率限制(每分钟请求数)');
            $table->integer('timeout')->default(30)->comment('超时时间(秒)');
            $table->tinyInteger('status')->default(1)->comment('状态：1-启用，0-禁用');
            $table->foreignId('owner_id')->constrained('users')->onDelete('cascade')->comment('创建者ID');
            $table->timestamp('last_used_at')->nullable()->comment('最后使用时间');
            $table->integer('total_requests')->default(0)->comment('总请求次数');
            $table->integer('success_requests')->default(0)->comment('成功请求次数');
            $table->timestamps();
            
            $table->index(['type', 'status']);
            $table->index('owner_id');
            $table->index('last_used_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('data_sources');
    }
};
