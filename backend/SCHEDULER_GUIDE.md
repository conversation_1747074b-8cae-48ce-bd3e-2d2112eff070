# 定时任务调度系统使用指南

## 概述

本系统实现了完整的监控任务定时调度功能，能够自动检查到期的监控任务并将其调度到消息队列进行处理。

## 核心组件

### 1. 调度内核 (Console/Kernel.php)
- 配置了每分钟执行的调度检查
- 设置了队列清理任务
- 防止重复执行的保护机制

### 2. 调度命令 (Console/Commands/DispatchMonitoringTasks.php)
主要调度命令，负责：
- 检查到期的监控任务
- 验证任务和数据源状态
- 调度任务到消息队列
- 更新任务的执行时间

### 3. 任务管理命令 (Console/Commands/ManageMonitoringTasks.php)
任务管理工具，支持：
- 列出所有监控任务
- 启动/停止/暂停/恢复任务
- 查看任务状态和统计信息

### 4. 任务处理器 (Jobs/ProcessDataSourceTask.php)
队列任务处理器，负责：
- 执行实际的数据采集
- 处理API请求和响应
- 更新任务统计信息
- 计算下次执行时间

## 使用方法

### 基本命令

```bash
# 查看待调度的任务（干运行）
php artisan schedule:dispatch-tasks --dry-run

# 实际调度到期的任务
php artisan schedule:dispatch-tasks

# 强制调度所有任务（忽略状态检查）
php artisan schedule:dispatch-tasks --force

# 列出所有监控任务
php artisan monitoring:manage list

# 查看系统整体状态
php artisan monitoring:manage status

# 查看特定任务状态
php artisan monitoring:manage status --id=1

# 启动特定任务
php artisan monitoring:manage start --id=1

# 启动所有任务
php artisan monitoring:manage start --all

# 停止特定任务
php artisan monitoring:manage stop --id=1

# 暂停运行中的任务
php artisan monitoring:manage pause --id=1

# 恢复暂停的任务
php artisan monitoring:manage resume --id=1
```

### 队列处理

```bash
# 启动队列工作进程
php artisan queue:work

# 指定队列处理监控任务
php artisan queue:work --queue=monitoring

# 查看队列状态
php artisan queue:monitor
```

### 生产环境部署

1. **设置系统Cron任务**：
```bash
# 编辑crontab
crontab -e

# 添加以下行（每分钟执行Laravel调度器）
* * * * * cd /path/to/your/project && php artisan schedule:run >> /dev/null 2>&1
```

2. **启动队列守护进程**：
```bash
# 使用Supervisor或systemd管理队列进程
php artisan queue:work --daemon --queue=monitoring
```

## 测试和验证

### 运行测试脚本
```bash
# 运行完整的系统测试
php test_scheduler.php
```

测试脚本会检查：
- 数据库连接和表结构
- 创建测试数据
- 执行调度命令
- 验证Cron表达式解析
- 检查队列配置

### 手动测试步骤

1. **创建测试任务**：
```bash
# 使用API或直接在数据库中创建监控任务
# 设置 next_run_at 为过去的时间以便立即触发
```

2. **执行调度**：
```bash
php artisan schedule:dispatch-tasks --dry-run
php artisan schedule:dispatch-tasks
```

3. **处理队列**：
```bash
php artisan queue:work --once
```

4. **检查结果**：
```bash
php artisan monitoring:manage status --id=任务ID
```

## 配置说明

### 监控任务配置
- `frequency_type`: 频率类型 (interval/cron)
- `frequency_value`: 间隔分钟数（interval模式）
- `cron_expression`: Cron表达式（cron模式）
- `auto_start`: 是否自动启动
- `status`: 任务状态 (pending/running/paused/stopped/failed)

### 支持的Cron表达式
```
*/5 * * * *    # 每5分钟
0 * * * *      # 每小时
0 9 * * *      # 每天9点
0 9 * * 1      # 每周一9点
0 0 1 * *      # 每月1号
```

## 监控和日志

### 日志位置
- 调度日志：`storage/logs/laravel.log`
- 队列日志：`storage/logs/laravel.log`

### 关键日志标识
- `监控任务已调度到队列`
- `调度监控任务失败`
- `数据源任务处理完成`
- `数据源任务处理失败`

### 性能监控
```bash
# 查看队列统计
php artisan monitoring:manage status

# 查看即将执行的任务
php artisan monitoring:manage status
```

## 故障排除

### 常见问题

1. **任务不执行**
   - 检查任务状态是否为 'running'
   - 检查 `auto_start` 是否为 true
   - 检查 `next_run_at` 时间
   - 验证数据源是否可用

2. **Cron表达式错误**
   - 使用在线Cron表达式验证工具
   - 查看日志中的解析错误信息
   - 回退到interval模式

3. **队列处理失败**
   - 检查队列配置
   - 验证数据源API连接
   - 查看任务超时设置

4. **调度器不运行**
   - 检查系统crontab配置
   - 验证Laravel调度器状态
   - 检查文件权限

### 调试命令
```bash
# 查看调度器配置
php artisan schedule:list

# 测试单个任务调度
php artisan schedule:dispatch-tasks --dry-run

# 查看队列失败任务
php artisan queue:failed

# 重试失败任务
php artisan queue:retry all
```

## 扩展功能

### 自定义调度策略
可以在 `DispatchMonitoringTasks` 命令中添加：
- 优先级调度
- 负载均衡
- 错误重试策略
- 动态频率调整

### 监控告警
集成监控系统：
- 任务执行失败告警
- 队列积压告警
- 系统资源监控

### 性能优化
- 批量任务处理
- 数据库连接池
- 缓存机制
- 异步处理优化 