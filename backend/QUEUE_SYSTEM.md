# 异步任务队列系统

本项目实现了基于Laravel的异步任务队列系统，用于处理数据源采集任务。

## 系统架构

### 核心组件

1. **任务类 (Job)**: `App\Jobs\ProcessDataSourceTask`
2. **生产者服务**: `App\Services\QueueProducerService`
3. **消费者命令**: `App\Console\Commands\ConsumeQueueTasks`
4. **测试命令**: `App\Console\Commands\TestQueueSystem`

### 队列驱动支持

- **数据库队列** (默认): 使用MySQL数据库存储任务
- **Redis队列**: 高性能内存队列（需要Redis服务）

## 配置说明

### 环境变量配置

在 `.env` 文件中配置队列连接：

```env
# 队列驱动（database 或 redis）
QUEUE_CONNECTION=database

# Redis配置（如使用Redis队列）
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379
REDIS_QUEUE_CONNECTION=default
REDIS_QUEUE=default
```

### 数据库表

系统使用以下数据库表：
- `jobs`: 存储待处理任务
- `failed_jobs`: 存储失败任务
- `job_batches`: 批量任务管理

## 使用方法

### 1. 发布任务到队列

#### 使用生产者服务

```php
use App\Services\QueueProducerService;

$queueProducer = new QueueProducerService();

// 发布单个任务
$jobId = $queueProducer->publishDataSourceTask(
    dataSourceId: 1,
    targetProducts: [
        ['id' => 123, 'type' => 'product', 'name' => '商品名称']
    ],
    taskParams: ['test_mode' => true],
    monitoringTaskId: null,
    queue: 'default',
    delay: 0
);

// 批量发布任务
$tasks = [
    [
        'data_source_id' => 1,
        'target_products' => [['id' => 123]],
        'task_params' => [],
        'delay' => 0
    ],
    // ... 更多任务
];
$jobIds = $queueProducer->batchPublishDataSourceTasks($tasks);
```

#### 直接使用Job类

```php
use App\Jobs\ProcessDataSourceTask;

$job = new ProcessDataSourceTask(
    dataSourceId: 1,
    targetProducts: [['id' => 123]],
    taskParams: [],
    monitoringTaskId: null
);

dispatch($job);
```

### 2. 启动队列消费者

#### 使用自定义消费者命令

```bash
# 基本启动
php artisan queue:consume-tasks

# 自定义参数
php artisan queue:consume-tasks \
    --queue=default \
    --timeout=60 \
    --memory=256 \
    --sleep=3 \
    --tries=3

# 守护进程模式（生产环境）
php artisan queue:consume-tasks --daemon
```

#### 使用Laravel原生命令

```bash
php artisan queue:work
```

### 3. 监控和管理

#### 查看队列状态

```bash
# 测试队列系统
php artisan queue:test-system --count=5

# 检查队列状态
php artisan queue:monitor
```

#### 管理失败任务

```bash
# 查看失败任务
php artisan queue:failed

# 重试失败任务
php artisan queue:retry all

# 清除失败任务
php artisan queue:flush
```

## 任务处理流程

### ProcessDataSourceTask 任务流程

1. **验证数据源**: 检查数据源是否存在且可用
2. **处理产品列表**: 遍历目标产品列表
3. **发送HTTP请求**: 调用数据源API获取数据
4. **数据处理**: 清洗和转换响应数据
5. **更新统计**: 更新数据源使用统计
6. **错误处理**: 记录失败信息和重试逻辑

### 任务参数说明

- `dataSourceId`: 数据源ID
- `targetProducts`: 目标产品列表
- `taskParams`: 任务参数（API参数等）
- `monitoringTaskId`: 关联的监控任务ID（可选）

## 生产环境部署

### 1. 进程监控

推荐使用 Supervisor 监控队列进程：

```ini
[program:laravel-worker]
process_name=%(program_name)s_%(process_num)02d
command=php /path/to/project/artisan queue:consume-tasks --daemon
directory=/path/to/project
autostart=true
autorestart=true
user=www-data
numprocs=4
redirect_stderr=true
stdout_logfile=/path/to/project/storage/logs/worker.log
```

### 2. Redis队列配置

如果使用Redis队列，确保：

1. Redis服务正常运行
2. PHP Redis扩展已安装
3. 配置合适的连接参数

```env
QUEUE_CONNECTION=redis
REDIS_HOST=127.0.0.1
REDIS_PORT=6379
REDIS_PASSWORD=your_password
```

### 3. 性能优化

- 根据服务器配置调整工作进程数量
- 设置合理的内存限制和超时时间
- 使用Redis队列提高性能
- 定期清理已完成的任务

## 故障排查

### 常见问题

1. **任务不被处理**
   - 检查队列消费者是否运行
   - 验证队列连接配置
   - 查看错误日志

2. **任务失败率高**
   - 检查数据源API状态
   - 验证网络连接
   - 调整超时和重试设置

3. **内存泄漏**
   - 设置合理的内存限制
   - 定期重启工作进程
   - 优化任务代码

### 日志查看

```bash
# Laravel日志
tail -f storage/logs/laravel.log

# 队列工作进程日志
tail -f storage/logs/worker.log
```

## API集成

队列系统可以通过API接口集成到前端应用：

```php
// 在控制器中发布任务
public function startDataCollection(Request $request)
{
    $queueProducer = app(QueueProducerService::class);
    
    $jobId = $queueProducer->publishDataSourceTask(
        $request->data_source_id,
        $request->target_products,
        $request->task_params
    );
    
    return response()->json([
        'success' => true,
        'job_id' => $jobId,
        'message' => '任务已提交到队列'
    ]);
}
```

## 扩展功能

系统支持以下扩展：

1. **任务优先级**: 不同队列处理不同优先级任务
2. **任务调度**: 结合Laravel调度器实现定时任务
3. **任务链**: 实现任务依赖和串行执行
4. **批量处理**: 大批量任务的分批处理
5. **实时通知**: WebSocket推送任务状态更新 