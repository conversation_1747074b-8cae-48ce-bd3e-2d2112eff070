<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\ProductData;
use App\Models\ProductSku;

// 测试偏离率计算功能
echo "=== 测试促销价偏离率和渠道价格偏离率计算功能 ===\n\n";

// 创建测试SKU数据
$testSkus = [
    [
        'sku_id' => 'TEST001',
        'name' => '测试SKU 1',
        'price' => 100.00,           // 原价
        'sub_price' => 80.00,        // 到手价
        'official_guide_price' => 90.00,  // 官方指导价
        'quantity' => 50,
    ],
    [
        'sku_id' => 'TEST002', 
        'name' => '测试SKU 2',
        'price' => 200.00,           // 原价
        'sub_price' => 200.00,       // 到手价（无促销）
        'official_guide_price' => 180.00,  // 官方指导价
        'quantity' => 30,
    ],
    [
        'sku_id' => 'TEST003',
        'name' => '测试SKU 3',
        'price' => 150.00,           // 原价
        'sub_price' => 170.00,       // 到手价（渠道加价）
        'official_guide_price' => 160.00,  // 官方指导价
        'quantity' => 0,
    ],
];

echo "测试数据：\n";
echo "SKU ID\t\t原价\t到手价\t官方指导价\n";
echo "------------------------------------------------------\n";
foreach ($testSkus as $sku) {
    echo sprintf("%-12s\t%.2f\t%.2f\t%.2f\n", 
        $sku['sku_id'], 
        $sku['price'], 
        $sku['sub_price'], 
        $sku['official_guide_price']
    );
}

echo "\n计算结果：\n";
echo "SKU ID\t\t促销价偏离率\t渠道价格偏离率\n";
echo "------------------------------------------------------\n";

foreach ($testSkus as $skuData) {
    // 创建SKU模型实例
    $sku = new ProductSku();
    $sku->fill($skuData);
    
    // 计算偏离率
    $promotionRate = $sku->promotion_deviation_rate;
    $channelRate = $sku->channel_price_deviation_rate;
    
    echo sprintf("%-12s\t%s%%\t\t%s%%\n", 
        $skuData['sku_id'],
        $promotionRate !== null ? number_format($promotionRate, 2) : 'N/A',
        $channelRate !== null ? number_format($channelRate, 2) : 'N/A'
    );
}

echo "\n=== 计算公式说明 ===\n";
echo "促销价偏离率 = (原价 - 到手价) / 原价 × 100%\n";
echo "渠道价格偏离率 = (官方指导价 - 到手价) / 官方指导价 × 100%\n\n";

echo "=== 结果解读 ===\n";
echo "正值：渠道降价/促销\n";
echo "负值：渠道加价\n";
echo "0值：无偏离\n\n";

echo "测试完成！\n"; 