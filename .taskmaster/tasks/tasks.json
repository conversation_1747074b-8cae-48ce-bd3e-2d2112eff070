{"master": {"tasks": [{"id": 1, "title": "环境搭建与项目初始化", "description": "Initialize the backend and frontend projects. Setup Git for version control, install and configure the PHP framework (Laravel/ThinkPHP), and initialize the Vue.js 3 project with Vite, Vue Router, and Pinia.", "details": "Backend: `composer create-project laravel/laravel project-name` or `composer create-project topthink/think project-name`. Configure `.env` file with database credentials and app settings. Frontend: `npm create vite@latest project-name -- --template vue-ts`. Install Element Plus/Ant Design Vue, Vue Router, and Pinia. Establish basic project structure for both.", "testStrategy": "Verify that the backend server runs and returns a default page. Verify the frontend development server starts successfully. Confirm Git repository is initialized and initial commit is made.", "priority": "high", "dependencies": [], "status": "done", "subtasks": [{"id": 1, "title": "Git仓库初始化与项目结构建立", "description": "为整个项目创建Git版本控制，并建立一个包含独立后端和前端目录的 monorepo 结构。这是所有后续开发工作的基础。", "dependencies": [], "details": "1. 在项目根目录执行 `git init` 初始化仓库。\n2. 创建 `backend` 和 `frontend` 两个子目录，分别用于存放后端和前端代码。\n3. 在根目录创建 `.gitignore` 文件，并添加通用忽略规则，例如 `/backend/vendor`, `/backend/.env`, `/frontend/node_modules`, `*.log` 等，以避免不必要的文件被提交。", "status": "done", "testStrategy": "执行 `git status` 命令，确认Git仓库已成功初始化。检查文件系统，确认 `backend` 和 `frontend` 目录已创建。"}, {"id": 2, "title": "后端PHP框架安装与配置", "description": "在 `backend` 目录中安装Laravel或ThinkPHP框架，并完成基础的环境配置，如生成应用密钥和设置数据库连接。", "dependencies": [], "details": "1. 进入 `backend` 目录。\n2. 执行 `composer create-project laravel/laravel .` 或 `composer create-project topthink/think .` 安装框架。\n3. 复制 `.env.example` 为 `.env`。\n4. 对于Laravel，执行 `php artisan key:generate`。\n5. 编辑 `.env` 文件，填入本地开发环境的数据库连接信息（DB_HOST, DB_DATABASE, DB_USERNAME, DB_PASSWORD）和应用URL（APP_URL）。\n<info added on 2025-06-22T14:05:17.969Z>\nLaravel框架安装成功完成。已使用Composer在backend目录安装Laravel 12.19.3，并生成了应用密钥。已修改.env配置文件，将数据库连接改为MySQL，配置信息为：DB_CONNECTION=mysql, DB_HOST=127.0.0.1, DB_PORT=3306, DB_DATABASE=ecommerce_monitor。经测试，artisan命令和路由系统均运行正常。\n</info added on 2025-06-22T14:05:17.969Z>", "status": "done", "testStrategy": "在 `backend` 目录中运行 `php artisan serve` (Laravel) 或 `php think run` (ThinkPHP)，访问浏览器中显示的本地地址（如 http://127.0.0.1:8000），应能看到框架的默认欢迎页面。"}, {"id": 3, "title": "前端Vue.js项目创建", "description": "在 `frontend` 目录中使用Vite脚手架创建一个基于Vue.js 3和TypeScript的新项目，并安装初始依赖。", "dependencies": [2], "details": "1. 进入 `frontend` 目录。\n2. 执行 `npm create vite@latest . -- --template vue-ts` 命令，在当前目录快速创建项目。\n3. 创建完成后，执行 `npm install` 安装项目所需的基础依赖包。\n<info added on 2025-06-22T14:08:32.918Z>\nVue.js项目创建成功完成：\n- 在frontend目录中使用Vite脚手架创建了Vue.js 3 + TypeScript项目\n- 成功安装了所有项目依赖（50个包）\n- 项目结构包含：src目录、public目录、vite.config.ts、tsconfig配置文件等\n- 开发服务器已启动并运行正常\n</info added on 2025-06-22T14:08:32.918Z>", "status": "done", "testStrategy": "在 `frontend` 目录中运行 `npm run dev`，访问浏览器中显示的本地开发服务器地址，应能看到Vite和Vue的默认欢迎页面，且浏览器控制台无错误。"}, {"id": 4, "title": "前端核心库集成：Vue Router与Pinia", "description": "为前端项目安装并集成Vue Router用于页面路由管理，以及Pinia用于全局状态管理。", "dependencies": [], "details": "1. 在 `frontend` 目录中执行 `npm install vue-router@4 pinia`。\n2. 创建 `src/router/index.ts` 文件，定义至少两个基本路由（如Home, About）并导出router实例。\n3. 创建 `src/stores` 目录，并在其中定义一个简单的counter store作为示例。\n4. 在 `src/main.ts` 中导入并 `app.use()` router和pinia实例。\n<info added on 2025-06-22T14:11:42.112Z>\nVue Router与Pinia集成成功完成：\n- 安装了vue-router@4和pinia依赖包\n- 创建了router配置文件(src/router/index.ts)，定义了Home和About两个路由\n- 创建了views目录和对应的Home.vue、About.vue页面组件\n- 创建了stores目录和counter.ts示例store，包含状态管理功能\n- 修改了main.ts文件，正确集成了router和pinia实例\n- 修改了App.vue文件，添加了导航链接、router-view和Pinia store使用示例\n- 开发服务器已启动，所有功能正常工作\n</info added on 2025-06-22T14:11:42.112Z>", "status": "done", "testStrategy": "在 `App.vue` 中添加 `<router-view></router-view>` 和 `<router-link>`。重新运行 `npm run dev`，点击链接应能切换页面。在组件中尝试使用Pinia store并验证状态是否能正常读写。"}, {"id": 5, "title": "前端UI框架集成", "description": "为前端项目安装并配置一个UI组件库，如Element Plus或Ant Design Vue，以统一项目UI风格并提高开发效率。", "dependencies": [4], "details": "1. 在 `frontend` 目录中执行 `npm install element-plus` 或 `npm install ant-design-vue`。\n2. 根据所选UI库的官方文档，在 `src/main.ts` 中进行全局引入或按需引入的配置。\n3. 如果是Element Plus，可能需要额外安装 `unplugin-vue-components` 和 `unplugin-auto-import` 以实现自动导入。\n<info added on 2025-06-22T14:14:57.266Z>\nElement Plus UI框架集成成功完成。已安装element-plus及unplugin-vue-components、unplugin-auto-import自动导入插件，并配置vite.config.ts支持自动导入。App.vue已修改，添加了按钮、空间布局等组件示例。Home.vue页面已更新，使用卡片、栅格、分割线等组件展示系统功能模块。About.vue页面已更新，使用描述列表、时间线、卡片等组件展示系统详细信息。开发服务器已启动，所有Element Plus组件均正常渲染和工作。\n</info added on 2025-06-22T14:14:57.266Z>", "status": "done", "testStrategy": "在任意一个Vue组件（如 `HelloWorld.vue`）中，尝试使用UI库的一个标志性组件（如 `<el-button>` 或 `<a-button>`）。运行开发服务器，检查按钮是否正确渲染并带有预期的样式。"}]}, {"id": 2, "title": "数据库设计与迁移", "description": "Create all necessary database tables as specified in the PRD using the chosen PHP framework's migration system. This includes users, roles, permissions, data_sources, monitoring_tasks, product_data, and all other defined tables.", "details": "For each table defined in the PRD (sections 3.1 to 3.11), create a corresponding migration file using `php artisan make:migration create_table_name`. Define the schema in the migration files, ensuring all columns, data types, constraints, and foreign keys match the PRD. Run `php artisan migrate` to create the tables in the MySQL database.", "testStrategy": "Connect to the MySQL database and verify that all tables and their columns have been created correctly as per the PRD specifications. Check for correct data types, constraints, and relationships.", "priority": "high", "dependencies": [1], "status": "done", "subtasks": [{"id": 1, "title": "创建用户、角色和权限相关的数据表", "description": "根据PRD定义，创建用于管理用户认证和授权的基础数据表，包括用户表、角色表、权限表以及它们之间的关联表。", "dependencies": [], "details": "使用 `php artisan make:migration create_users_table`、`create_roles_table`、`create_permissions_table`、`create_role_user_table` 和 `create_permission_role_table` 命令创建迁移文件。在 `users` 表中定义用户名、邮箱、密码等字段。在 `roles` 和 `permissions` 表中定义名称和描述。在关联（pivot）表中设置外键约束，连接用户、角色和权限。", "status": "done", "testStrategy": "执行 `php artisan migrate` 后，使用数据库客户端检查 `users`, `roles`, `permissions`, `role_user`, `permission_role` 表是否成功创建，并核对所有字段、数据类型、索引和外键约束是否与PRD一致。"}, {"id": 2, "title": "创建数据源管理相关的数据表", "description": "创建用于存储和管理外部数据源信息的数据表，例如电商平台API的连接信息。", "dependencies": [], "details": "使用 `php artisan make:migration create_data_sources_table` 命令创建迁移文件。表中应包含字段：数据源名称、类型（如'Taobao', 'JD'）、API凭证（建议加密存储）、状态（启用/禁用）以及一个指向 `users` 表的外键 `owner_id`，用于标识创建者。", "status": "done", "testStrategy": "执行 `php artisan migrate` 后，检查 `data_sources` 表的结构是否正确，特别是外键 `owner_id` 是否已正确关联到 `users` 表。"}, {"id": 3, "title": "创建商品数据相关的数据表", "description": "创建用于存储从各个数据源同步过来的商品信息的核心数据表。", "dependencies": [], "details": "使用 `php artisan make:migration create_products_table` 命令创建迁移文件。表中应包含字段：商品唯一标识（如SPU/SKU）、商品标题、主图URL、价格、库存等。同时，需要一个外键 `data_source_id` 关联到 `data_sources` 表，以标识商品来源。", "status": "done", "testStrategy": "执行 `php artisan migrate` 后，验证 `products` 表的结构，并确保 `data_source_id` 外键已正确建立。"}, {"id": 4, "title": "创建监控任务相关的数据表", "description": "创建用于定义和管理用户设置的商品监控任务的数据表。", "dependencies": [3], "details": "使用 `php artisan make:migration create_monitoring_tasks_table` 命令创建迁移文件。表中应包含字段：任务名称、监控频率、监控指标（如价格、库存）、触发条件、任务状态（运行中/暂停/已停止）。需要设置外键 `user_id` 关联到 `users` 表，以及外键 `product_id` 关联到 `products` 表。", "status": "done", "testStrategy": "执行 `php artisan migrate` 后，检查 `monitoring_tasks` 表的结构，并验证 `user_id` 和 `product_id` 两个外键是否都已正确设置。"}, {"id": 5, "title": "创建预警和通知相关的数据表", "description": "创建用于记录监控任务触发的预警信息以及通知发送状态的数据表。", "dependencies": [], "details": "使用 `php artisan make:migration create_alerts_table` 命令创建迁移文件。`alerts` 表应包含字段：外键 `monitoring_task_id`、预警类型、触发时的具体数值、预警时间、处理状态（如'待处理', '已处理'）。可以考虑创建 `notifications` 表来记录具体的通知发送记录（如邮件、短信）。", "status": "done", "testStrategy": "执行 `php artisan migrate` 后，检查 `alerts` 表的结构，确保 `monitoring_task_id` 外键正确关联到 `monitoring_tasks` 表。"}, {"id": 6, "title": "创建操作日志数据表", "description": "创建一张通用的操作日志表，用于记录系统中的关键用户行为，如登录、创建任务、修改配置等，便于审计和问题排查。", "dependencies": [3], "details": "使用 `php artisan make:migration create_activity_logs_table` 命令创建迁移文件。表中应包含字段：操作用户 `user_id` (外键)、操作类型（如 'login', 'create_task'）、操作描述、请求IP、用户代理(User Agent)和操作时间。可以考虑使用多态关联，将日志与具体的操作对象（如Task, User）关联起来。", "status": "done", "testStrategy": "执行 `php artisan migrate` 后，检查 `activity_logs` 表的结构，验证 `user_id` 外键及其他字段是否符合设计要求。"}]}, {"id": 3, "title": "后端用户认证与权限(RBAC)模块", "description": "Implement the user authentication system and Role-Based Access Control (RBAC). This includes APIs for user registration, login, logout, and management, as well as role and permission management.", "details": "Create models for User, Role, and Permission. Implement API endpoints for `/register`, `/login`, `/logout`. Use password hashing (e.g., bcrypt). Implement middleware to protect routes based on user roles and permissions. Create CRUD APIs for managing users, roles, and assigning permissions to roles.", "testStrategy": "Unit test user registration and login logic. Integration test API endpoints using a tool like Postman. Test protected routes to ensure unauthorized users are blocked and authorized users are allowed.", "priority": "high", "dependencies": [2], "status": "done", "subtasks": [{"id": 1, "title": "创建数据模型与数据库结构 (Create Data Models and Database Structure)", "description": "定义用户(User)、角色(Role)、权限(Permission)的数据库模型及其关联关系。这是整个认证和权限系统的基础，为后续功能提供数据存储结构。", "dependencies": [], "details": "使用ORM框架（如Sequelize, TypeORM, Mongoose）创建三个核心模型。User模型应包含username, email, password(hashed)等字段。Role模型应包含name (e.g., 'admin', 'editor'), description。Permission模型应包含name (e.g., 'create:post', 'delete:user'), description。关键在于正确定义它们之间的关联关系：一个用户拥有一个角色（User-Role: Many-to-One），一个角色拥有多个权限（Role-Permission: Many-to-Many）。", "status": "done", "testStrategy": "单元测试每个模型的创建、读取和关联关系是否正确。验证数据库迁移脚本是否能成功生成预期的表结构。"}, {"id": 2, "title": "实现用户注册与登录API (Implement User Registration and Login APIs)", "description": "开发核心的用户认证端点，包括新用户注册和现有用户登录功能。这是用户与系统交互的入口。", "dependencies": [], "details": "创建 `/api/auth/register` 接口，接收用户信息，使用bcrypt库对密码进行哈希加密后，创建新的User记录存入数据库。创建 `/api/auth/login` 接口，接收用户凭证，从数据库中查找用户并使用bcrypt.compare验证密码。验证成功后，使用jsonwebtoken (JWT)库生成一个包含用户ID和角色的token，并将其返回给客户端。", "status": "done", "testStrategy": "API集成测试：测试注册接口能否成功创建用户，并对重复用户名等异常情况进行处理。测试登录接口，使用正确的凭证应返回JWT，使用错误的凭证应返回认证失败错误。"}, {"id": 3, "title": "开发JWT认证中间件与登出功能 (Develop JWT Authentication Middleware and Logout)", "description": "创建一个中间件来验证所有受保护API请求中的JWT，确保只有已登录的用户才能访问。同时提供用户登出机制。", "dependencies": [], "details": "编写一个中间件（例如在Express中），它会从请求的 `Authorization` header中提取 'Bearer' token。使用JWT库和密钥来验证token的签名和有效期。如果token有效，解析出payload（包含用户ID和角色），并将其附加到请求对象上（例如 `req.user`）。如果token无效或不存在，则返回401 Unauthorized错误。创建一个 `/api/auth/logout` 接口，其服务端逻辑可以是（可选）将该JWT加入黑名单，以防止其在过期前被重用。", "status": "done", "testStrategy": "创建一个受保护的测试路由（例如 `/api/profile`），应用此中间件。使用有效token访问应成功，不带token或使用无效token访问应返回401。"}, {"id": 4, "title": "实现角色与权限的CRUD管理API (Implement CRUD Management APIs for Roles and Permissions)", "description": "开发用于管理角色(Role)和权限(Permission)的RESTful API，使管理员能够通过API动态地创建、读取、更新和删除系统中的角色和权限。", "dependencies": [], "details": "创建 `/api/roles` 和 `/api/permissions` 的系列端点。为这两个资源实现完整的CRUD操作（GET列表, GET单个, POST创建, PUT更新, DELETE删除）。使用上一步创建的JWT认证中间件来保护这些路由。初始阶段可以硬编码只允许特定用户（如ID为1的用户）访问，后续将由RBAC中间件进行更精细的控制。", "status": "done", "testStrategy": "对每个CRUD端点进行API测试。确保只有经过认证的（未来是授权的）用户才能执行操作。验证数据的创建、更新和删除是否正确反映在数据库中。"}, {"id": 5, "title": "开发角色与权限的分配接口 (Develop APIs for Assigning Roles and Permissions)", "description": "实现将角色分配给用户，以及将权限分配给角色的核心逻辑。这是将用户、角色和权限三者关联起来的关键步骤。", "dependencies": [], "details": "创建用于管理这些关联关系的API。例如：实现 `PUT /api/users/:userId/role` 接口，用于更新指定用户的角色。实现 `POST /api/roles/:roleId/permissions` 接口，接收一个权限ID数组，为指定角色批量添加权限。实现 `DELETE /api/roles/:roleId/permissions/:permissionId` 来移除特定权限。这些操作将修改User表中的roleId外键或Role-Permission中间关联表。", "status": "done", "testStrategy": "API测试：创建一个用户、一个角色和几个权限。测试能否成功将角色分配给用户，以及将权限分配给角色。验证数据库中的外键和关联表是否被正确更新。"}, {"id": 6, "title": "实现基于角色的访问控制(RBAC)授权中间件 (Implement RBAC Authorization Middleware)", "description": "创建最终的授权中间件，它在认证之后运行，根据用户的角色及其拥有的权限来决定其是否有权访问目标API。", "dependencies": [], "details": "开发一个灵活的授权中间件，它可以接收所需的权限作为参数，例如 `requirePermission('delete:user')`。该中间件必须在JWT认证中间件之后执行。它从 `req.user` 中获取用户ID和角色，然后查询数据库（或缓存）以确定该用户的角色是否拥有所需的权限。如果用户具备权限，则调用 `next()` 继续处理请求；否则，返回403 Forbidden错误。最后，将此中间件应用到所有需要特定权限才能访问的API路由上（例如用户管理、角色管理等）。", "status": "done", "testStrategy": "创建一个需要特定权限的测试路由。为测试用户分配一个不含该权限的角色，访问应返回403。然后为该角色添加所需权限，再次访问应成功通过。"}]}, {"id": 4, "title": "数据源管理(data_sources)API开发", "description": "Develop the backend API endpoints for managing data sources. This allows administrators to configure third-party API details, including endpoints, parameters, and field mappings.", "details": "Create a `DataSourceController` with CRUD methods (Create, Read, Update, Delete). The `create` and `update` methods will accept JSON for `fixed_params`, `headers`, `param_mapping`, and `field_mapping` and store them as TEXT in the `data_sources` table. Implement validation to ensure required fields are present.", "testStrategy": "API endpoint testing: Create a data source with valid JSON mappings. Retrieve it to verify data integrity. Update it and verify changes. Delete it. Test validation by sending invalid or incomplete data.", "priority": "high", "dependencies": [3], "status": "done", "subtasks": []}, {"id": 5, "title": "异步任务队列(RabbitMQ/Redis)搭建", "description": "Set up the asynchronous task processing infrastructure using a message queue (RabbitMQ or Redis Streams) to handle concurrent data collection tasks efficiently.", "details": "Install and configure RabbitMQ. In the PHP application, install the appropriate client library (e.g., `php-amqplib/php-amqplib`). Create a 'producer' service that takes a task (e.g., collect data for item X using data source Y) and publishes it to a queue. Create a 'consumer' script (`php artisan command:consume-tasks`) that listens to the queue for incoming messages.", "testStrategy": "Create a test command to publish a message to the queue. Run the consumer script in the background. Verify that the consumer receives and processes the message correctly. Check queue status in RabbitMQ management UI.", "priority": "high", "dependencies": [4], "status": "done", "subtasks": []}, {"id": 6, "title": "核心数据采集与标准化服务", "description": "Develop the core service responsible for fetching data from third-party APIs based on `data_sources` configuration and then standardizing the response using the defined `field_mapping`.", "details": "Create a `DataCollectionService`. This service will: 1. Accept a `data_source_id` and `item_id`. 2. Fetch the `DataSource` configuration from the database. 3. Dynamically construct the API request (URL, method, headers, params). 4. Use a robust HTTP client like <PERSON><PERSON><PERSON> to make the request. 5. Upon receiving a successful response, parse the JSON. 6. Use a JSON Path library to extract data based on `field_mapping`. 7. Implement special logic for fields like `最低到手价` (min value from array) and `State` (string to boolean conversion). 8. Store the standardized data and raw data into the `product_data` table.", "testStrategy": "Unit test the service with mock `DataSource` objects and sample API responses. Verify that the data is correctly mapped and transformed. Test edge cases like API errors, empty responses, and missing fields in the JSON Path.", "priority": "high", "dependencies": [5], "status": "done", "subtasks": []}, {"id": 7, "title": "监控任务管理(monitoring_tasks)API开发", "description": "Develop backend APIs for creating, reading, updating, and deleting monitoring tasks (`monitoring_tasks`) and task groups (`task_groups`).", "details": "Create `MonitoringTaskController` and `TaskGroupController`. Implement full CRUD functionality for both. The `create` method for tasks should validate inputs like `module_type`, `data_source_id`, and `collect_frequency`. Associate tasks and groups with the authenticated `user_id`.", "testStrategy": "Test all CRUD API endpoints for both tasks and groups. Ensure that a user can only manage their own tasks and groups. Validate that tasks cannot be created with non-existent data sources.", "priority": "high", "dependencies": [2], "status": "done", "subtasks": []}, {"id": 8, "title": "定时任务调度系统实现", "description": "Implement a scheduled command that periodically checks for due monitoring tasks and dispatches them to the message queue for processing.", "details": "Use the framework's built-in scheduler (e.g., Laravel Scheduler). Create a command (`php artisan schedule:dispatch-tasks`) that runs every minute. The command will query the `monitoring_tasks` table for tasks where `next_collection_at` is in the past. For each due task, it will publish a job to the message queue (created in Task 5) and update the task's `last_collected_at` and `next_collection_at` timestamps.", "testStrategy": "Manually create a task in the database with a `next_collection_at` in the past. Run the scheduler command. Verify that a message is sent to the queue and the task's timestamps are updated correctly in the database.", "priority": "high", "dependencies": [6, 7], "status": "done", "subtasks": []}, {"id": 9, "title": "前端基础框架与用户登录流程", "description": "Develop the main frontend application structure, including login/registration pages, a main layout with a sidebar, and protected routes using Vue Router.", "details": "Create a `Login.vue` page and handle API calls to the backend authentication endpoints. Use Pinia to store the user's authentication token. Configure Vue Router with a navigation guard (`router.beforeEach`) that checks for the auth token and redirects to the login page if not present. Create a main `AppLayout.vue` with a persistent sidebar and a content area (`<router-view>`).", "testStrategy": "Test the login flow. Attempt to access a protected page without logging in and verify redirection. Log in and confirm access to protected pages. Verify the main layout and sidebar render correctly.", "priority": "high", "dependencies": [1], "status": "done", "subtasks": []}, {"id": 10, "title": "前端数据源管理页面", "description": "Create the administrator-exclusive frontend page for managing data sources. This includes a form for creating/editing sources with a user-friendly interface for JSON mappings.", "details": "Create a new view component, `DataSourceManagement.vue`, accessible only to admin roles. Use a table to list existing data sources with edit/delete buttons. The create/edit form should have inputs for all fields in the `data_sources` table. For JSON fields (`param_mapping`, `field_mapping`), use a code editor component (e.g., `vue-codemirror`) to provide a better editing experience.", "testStrategy": "As an admin user, navigate to the page. Verify the list of data sources is displayed. Test creating a new source with complex JSON mappings. Test editing and deleting an existing source. Verify non-admin users cannot access this page.", "priority": "medium", "dependencies": [4, 9], "status": "done", "subtasks": []}, {"id": 11, "title": "前端监控任务管理页面", "description": "Develop the frontend interface for users to create, view, update, and delete their monitoring tasks and task groups.", "details": "Create a `TaskManagement.vue` page. Implement UI for managing groups (e.g., a list of groups, with the ability to add/edit/delete). Within a selected group, display a table of monitoring tasks. The 'Add/Edit Task' form should be a modal or separate page, allowing users to select a `data_source`, input `item_ids`, and choose a `collect_frequency` from a dropdown.", "testStrategy": "Log in as a standard user. Create a task group. Create a monitoring task within that group. Edit the task's name and frequency. Delete the task and the group. Verify all actions are reflected correctly via API calls.", "priority": "medium", "dependencies": [7, 9], "status": "done", "subtasks": []}, {"id": 12, "title": "预警规则(alert_rules)API开发", "description": "Develop backend APIs for managing alert rules. This includes creating rules based on specific fields, operators, and thresholds.", "details": "Create `AlertRuleController` with full CRUD functionality. The `create` and `update` methods should validate `rule_type`, `target_field`, `operator`, and `threshold`. The API should allow associating rules with a specific task, a task group, or the entire user account. Store notification preferences (`notification_channels`, `recipients`) as JSON.", "testStrategy": "Test all CRUD API endpoints for alert rules. Verify that rules can be created with different operators and thresholds. Test validation by attempting to create a rule with an invalid `target_field` or `operator`.", "priority": "medium", "dependencies": [2], "status": "done", "subtasks": []}, {"id": 13, "title": "后端预警触发器逻辑", "description": "Implement the logic to check for alert conditions after new data is collected and stored. If a rule is triggered, create a record in the `alerts` table.", "details": "Modify the `DataCollectionService` or create a subsequent job in the queue. After saving data to `product_data`, this service will: 1. Fetch all active `alert_rules` relevant to the task/user. 2. For each rule, compare the `target_field` in the new `product_data` record against the rule's `threshold` using the specified `operator`. 3. If the condition is met, insert a new record into the `alerts` table with the details.", "testStrategy": "Create a specific alert rule (e.g., `subPrice` < 100). Manually trigger a data collection that results in a price of 99. Verify that a new record is created in the `alerts` table with the correct message and details.", "priority": "medium", "dependencies": [6, 12], "status": "done", "subtasks": []}, {"id": 14, "title": "通知服务集成(邮件与站内信)", "description": "Integrate a notification service to send alerts via email or other channels when an alert is triggered.", "details": "After an alert is created in the `alerts` table, dispatch a new notification job. This job will read the `notification_channels` from the `alert_rule`. If 'email' is present, use a library like PHPMailer or the framework's mailer to send a formatted email to the specified `recipients`. For 'in_app', create a record in a separate `notifications` table.", "testStrategy": "Set up a test alert rule with your email as the recipient. Trigger the alert condition. Verify that you receive an email with the correct alert details. Check the database for the in-app notification record.", "priority": "medium", "dependencies": [13], "status": "done", "subtasks": []}, {"id": 15, "title": "前端渠道价格监测看板", "description": "Develop the frontend dashboard for the 'Channel Price Monitoring' module, displaying collected product data in a structured and comparable way.", "details": "Create a `ChannelPriceDashboard.vue` component. It should fetch and display data from the `product_data` table related to the user's tasks. Use a data table component (from Element Plus/Ant Design Vue) to show products with key fields like `Title`, `subPrice`, `Quantity`, `State`. Implement filtering and sorting options. Add a feature to select multiple products and view their details side-by-side in a modal.", "testStrategy": "Populate the `product_data` table with sample data for a user's task. Log in as that user and navigate to the dashboard. Verify the data is displayed correctly. Test the filtering, sorting, and comparison features.", "priority": "medium", "dependencies": [11], "status": "done", "subtasks": []}, {"id": 16, "title": "后端数据分析与指标计算", "description": "Implement the backend logic for calculating advanced analytical metrics for the 'Competitor Dynamics Monitoring' module as specified in the PRD.", "details": "Create an `AnalyticsService`. Implement methods to calculate metrics like `促销策略倾向`, `单品价格综合折扣率`, `竞品价格趋势斜率`, etc. These methods will query historical data from the `product_data` table for a given set of items over a time period. The results can be cached to improve performance.", "testStrategy": "Unit test each calculation method with predefined datasets to ensure the mathematical logic is correct. Create an API endpoint that exposes these calculations and test it with various product and time-range inputs.", "priority": "low", "dependencies": [6], "status": "done", "subtasks": []}, {"id": 17, "title": "前端竞品动态监测看板与图表", "description": "Develop the frontend dashboard for the 'Competitor Dynamics Monitoring' module, focusing on data visualization with charts and graphs.", "details": "Create a `CompetitorDashboard.vue` component. Integrate a charting library like ECharts or AntV G2. Use the backend analytics API to fetch data. Display key metrics. Implement visualizations such as line charts for price trends, pie charts for promotion type distribution, and heatmaps for category price bands.", "testStrategy": "With sample data and a working analytics API, verify that all charts and graphs on the dashboard render correctly. Check tooltips and interactive features of the charts. Ensure the data visualization accurately represents the underlying data.", "priority": "low", "dependencies": [15, 16], "status": "done", "subtasks": []}, {"id": 18, "title": "前端预警中心(规则设置与历史记录)", "description": "Create the frontend interface for users to manage their alert rules and view a history of triggered alerts.", "details": "Create an `AlertsCenter.vue` page with two tabs. The 'Rules' tab will allow users to create/edit/delete alert rules using a form similar to the backend API structure. The 'History' tab will display a list of triggered alerts from the `alerts` table, showing the message, value, and time. Implement a 'mark as read' feature.", "testStrategy": "As a user, create a new alert rule via the UI. Trigger the alert and verify it appears in the 'History' tab. Test the 'mark as read' functionality. Edit and delete an existing rule.", "priority": "low", "dependencies": [12, 14], "status": "done", "subtasks": []}, {"id": 19, "title": "操作日志(audit_logs)模块", "description": "Implement a system for logging key user actions and system events for auditing and debugging purposes.", "details": "Create an `audit_logs` table as per the PRD. Use middleware or model observers in the backend framework to automatically log actions. For example, an observer on the `MonitoringTask` model can log 'create', 'update', 'delete' actions, storing the user ID, action type, and changed data (as JSON) in the `details` field.", "testStrategy": "Perform a key action, such as creating a new monitoring task. Query the `audit_logs` table to verify that a corresponding log entry was created with the correct user ID, action, and details.", "priority": "low", "dependencies": [3], "status": "done", "subtasks": []}, {"id": 20, "title": "系统性能优化与安全加固", "description": "Perform a comprehensive review and optimization of the system, focusing on database performance, API response times, and security vulnerabilities.", "details": "Database: Analyze slow queries and add indexes to frequently queried columns in `product_data` and `monitoring_tasks`. API: Implement caching for frequently accessed, non-dynamic data. Review all API endpoints for security vulnerabilities like SQL Injection, XSS, and ensure proper authorization checks are in place. Encrypt sensitive data like API tokens in the `data_sources` table.", "testStrategy": "Use database tools to analyze query performance before and after adding indexes. Use API testing tools to measure response times under load. Conduct a security audit using automated tools and manual code review to identify potential vulnerabilities.", "priority": "low", "dependencies": [8, 17], "status": "done", "subtasks": []}], "metadata": {"created": "2025-06-22T13:16:26.145Z", "updated": "2025-06-22T17:34:44.192Z", "description": "Tasks for master context"}}}