---
description: 
globs: *.vue,*.js,*.ts,*.json,*.css,*.scss,*.html
alwaysApply: false
---
# 前端（Vue 3 + Vite）结构与说明（修正版）

- **主目录**：`frontend/src/`
  - `views/`：页面级组件，每个文件对应一个路由页面，详见“3.1 views”。
  - `components/`：可复用UI组件，如预警规则管理、布局、验证码等。
  - `api/`：所有与后端API交互的封装模块，见“3.2 api”。
  - `stores/`：Pinia状态管理，用户认证、全局状态等。
  - `router/`：Vue Router路由配置。
  - `styles/`：全局样式（如global.css）。
  - `assets/`：静态资源（如图片、SVG）。
  - `App.vue`：根组件。
  - `main.ts`：应用入口，初始化Vue实例。

- **其它**：
  - `public/`：静态资源目录，构建时直接复制。
  - `index.html`：SPA入口HTML。
  - `vite.config.ts`、`package.json`、`tsconfig.json`：构建、依赖、类型配置。



