---
description: 
globs: 
alwaysApply: true
---
# 项目结构总览（修正版）

本项目为电商市场动态监测系统，采用前后端分离架构。

- **根目录**：
  - `frontend/`：前端Vue 3 + Vite单页应用，所有UI与交互逻辑均在此目录。
  - `backend/`：后端Laravel API服务，包含所有业务逻辑、数据库、队列等。
  - `database/`：部分遗留数据库迁移文件，主迁移已移至`backend/database/migrations/`。
  - `img/`：项目相关图片资源（如文档配图）。
  - `app/`：遗留/误放目录，核心代码已全部迁移至`backend/app/`。
  - 其它：如`开发蓝图.txt`、`接口说明.txt`、`电商市场动态监测系统需求文档.md`为文档说明，`*.php`为测试或工具脚本。

- **后端主入口**：`backend/public/index.php`
- **前端主入口**：`frontend/src/main.ts`，根组件为`frontend/src/App.vue`
- **数据库迁移**：`backend/database/migrations/`为主迁移目录，`database/migrations/`为历史遗留。


