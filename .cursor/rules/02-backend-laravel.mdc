---
description: 
globs: *.php,*.json,*.env,*.md,*.js,*.css,*.blade.php
alwaysApply: false
---
# 后端（Laravel）结构与说明（修正版）

- **核心目录**：
  - `backend/app/Models/`：Eloquent模型，映射数据库表，见项目结构原理图"1.1 Models"。
  - `backend/app/Services/`：业务服务类，封装采集、分析、预警、日志等核心逻辑。
  - `backend/app/Http/Controllers/Api/`：API控制器，RESTful接口入口，见"1.3 Controllers/Api"。
  - `backend/app/Jobs/`：队列任务，异步处理数据采集、预警检测、通知等。
  - `backend/app/Console/Commands/`：Artisan命令，调度、管理、测试队列与监控任务。
  - `backend/app/Http/Requests/`：表单验证类，保障API数据安全。
  - `backend/app/Http/Middleware/`：中间件，权限、角色、跨域等。
  - `backend/app/Policies/`：授权策略，细粒度权限控制。
  - `backend/app/Providers/`：服务提供者，框架扩展与事件注册。
  - `backend/app/Observers/`、`Listeners/`、`Notifications/`：事件、通知、日志等。

- **数据库迁移**：
  - `backend/database/migrations/`：所有表结构、字段变更、性能优化、历史表等迁移文件，见"2. 数据库迁移"。

- **配置与入口**：
  - `backend/config/`：数据库、队列、缓存、认证等所有配置。
  - `backend/routes/api.php`：API路由主入口。
  - `backend/public/index.php`：后端Web入口。

- **测试与资源**：
  - `backend/tests/`：自动化测试（Unit/Feature）。
  - `backend/resources/`：Blade模板、JS、CSS资源（主要用于后端独立页面）。

- **其它**：
  - `artisan`：Laravel命令行工具。
  - `composer.json`：PHP依赖管理。


