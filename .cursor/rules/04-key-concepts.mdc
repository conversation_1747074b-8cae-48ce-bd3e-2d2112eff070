---
description: 
globs: *
alwaysApply: false
---
# 核心概念与主流程（修正版）

本系统围绕“监控任务”自动采集、分析、预警电商数据，核心流程如下：

1. **数据源（DataSource）**
   - 定义采集目标，管理于`DataSourceManagement.vue`，后端为`DataSourceController.php`。
2. **监控任务（MonitoringTask）**
   - 用户在`ChannelPriceTaskManagement.vue`等页面创建，API由`MonitoringTaskController.php`处理。
   - 任务参数校验由`StoreMonitoringTaskRequest.php`等负责。
3. **调度与队列**
   - `DispatchMonitoringTasks`命令定时分发到期任务，推送`ProcessDataSourceTask`到队列。
   - 队列Worker异步执行采集、分析，核心逻辑在`DataCollectionService.php`。
   - 采集结果写入`product_data`、`product_skus`等表。
4. **预警与通知**
   - 预警规则由`AlertRulesManagement.vue`配置，API为`AlertRuleController.php`。
   - 采集后自动触发`ProcessAlertChecking`，如命中规则则生成`Alert`并通过`AlertTriggeredNotification.php`通知。
5. **数据展示与分析**
   - 前端各大盘、报表页面（如`ChannelPriceDashboard.vue`、`AnalyticsController.php`）实时展示分析结果。

**主线API、模型、服务、队列、前端页面均已在项目结构原理图和本规则中详细标注。**


