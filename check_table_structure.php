<?php

require __DIR__.'/backend/vendor/autoload.php';

$app = require_once __DIR__.'/backend/bootstrap/app.php';

$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

$tableName = 'product_data';

if (Schema::hasTable($tableName)) {
    $columns = Schema::getColumnListing($tableName);
    echo "Table '{$tableName}' exists. Columns: " . implode(', ', $columns) . "\n\n";

    echo "Detailed structure for '{$tableName}':\n";
    $columnDetails = DB::select("DESCRIBE {$tableName}");
    foreach ($columnDetails as $column) {
        echo "  - {$column->Field} ({$column->Type})";
        if ($column->Null === 'YES') {
            echo " [NULLABLE]";
        }
        if ($column->Key) {
            echo " [KEY: {$column->Key}]";
        }
        if ($column->Default) {
            echo " [DEFAULT: {$column->Default}]";
        }
        if ($column->Extra) {
            echo " [EXTRA: {$column->Extra}]";
        }
        echo "\n";
    }

} else {
    echo "Table '{$tableName}' does not exist.";
} 