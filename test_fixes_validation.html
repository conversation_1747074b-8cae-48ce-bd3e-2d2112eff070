<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修复验证测试</title>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #f0f9ff; border-color: #0369a1; }
        .error { background-color: #fef2f2; border-color: #dc2626; }
        .loading { background-color: #fffbeb; border-color: #d97706; }
        button { padding: 10px 15px; margin: 5px; border: none; border-radius: 3px; cursor: pointer; }
        .btn-primary { background-color: #3b82f6; color: white; }
        .btn-success { background-color: #10b981; color: white; }
        .btn-warning { background-color: #f59e0b; color: white; }
        pre { background-color: #f3f4f6; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>电商监测系统修复验证</h1>
    
    <div class="test-section">
        <h2>1. 预警规则统计API测试</h2>
        <button class="btn-primary" onclick="testAlertRulesStats()">测试 /api/alert-rules/stats</button>
        <div id="alertStatsResult"></div>
    </div>
    
    <div class="test-section">
        <h2>2. 预警规则列表API测试</h2>
        <button class="btn-primary" onclick="testAlertRulesList()">测试 /api/alert-rules (监控任务页面用)</button>
        <div id="alertListResult"></div>
    </div>
    
    <div class="test-section">
        <h2>3. 创建预警规则测试（修复priority问题）</h2>
        <button class="btn-primary" onclick="testCreateAlertRule()">测试创建规则（含medium priority）</button>
        <div id="createRuleResult"></div>
    </div>
    
    <div class="test-section">
        <h2>4. 获取选项配置测试</h2>
        <button class="btn-primary" onclick="testAlertRuleOptions()">测试 /api/alert-rules/options</button>
        <div id="optionsResult"></div>
    </div>
    
    <div class="test-section">
        <h2>5. 综合测试</h2>
        <button class="btn-success" onclick="runAllTests()">运行所有测试</button>
        <div id="comprehensiveResult"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000/api';
        const AUTH_TOKEN = '28|wJ85np4MPWlytOB1VcqmWTWTKTi28buL5CNBEO96c7e26591';

        axios.defaults.headers.common['Authorization'] = `Bearer ${AUTH_TOKEN}`;
        axios.defaults.headers.common['Accept'] = 'application/json';
        axios.defaults.headers.common['Content-Type'] = 'application/json';

        function setLoading(elementId, message = '测试中...') {
            document.getElementById(elementId).innerHTML = `
                <div class="loading">
                    <p><strong>${message}</strong></p>
                </div>
            `;
        }

        function setResult(elementId, success, message, data = null) {
            const className = success ? 'success' : 'error';
            let html = `
                <div class="${className}">
                    <p><strong>${success ? '✅ 成功' : '❌ 失败'}</strong></p>
                    <p>${message}</p>
            `;
            
            if (data) {
                html += `<pre>${JSON.stringify(data, null, 2)}</pre>`;
            }
            
            html += '</div>';
            document.getElementById(elementId).innerHTML = html;
        }

        async function testAlertRulesStats() {
            setLoading('alertStatsResult');
            try {
                const response = await axios.get(`${API_BASE}/alert-rules/stats`);
                setResult('alertStatsResult', true, '预警规则统计API正常工作', response.data);
            } catch (error) {
                setResult('alertStatsResult', false, 
                    `统计API失败: ${error.response?.status} - ${error.response?.data?.message || error.message}`,
                    error.response?.data
                );
            }
        }

        async function testAlertRulesList() {
            setLoading('alertListResult');
            try {
                const response = await axios.get(`${API_BASE}/alert-rules`, {
                    params: { status: 1, per_page: 100 }
                });
                
                let message = '预警规则列表API正常工作';
                if (response.data.success && response.data.data && response.data.data.data) {
                    message += ` (找到 ${response.data.data.data.length} 个规则)`;
                }
                
                setResult('alertListResult', true, message, {
                    success: response.data.success,
                    total_rules: response.data.data?.meta?.total || 0,
                    current_page_rules: response.data.data?.data?.length || 0,
                    sample_rule: response.data.data?.data?.[0] || null
                });
            } catch (error) {
                setResult('alertListResult', false, 
                    `规则列表API失败: ${error.response?.status} - ${error.response?.data?.message || error.message}`,
                    error.response?.data
                );
            }
        }

        async function testCreateAlertRule() {
            setLoading('createRuleResult');
            try {
                const testRule = {
                    name: "修复测试规则_" + Date.now(),
                    description: "测试medium priority修复",
                    rule_type: ["listing_status_change"],
                    conditions: {},
                    severity: "medium",
                    priority: "medium", // 这里测试修复后的medium选项
                    notification_method: ["system"],
                    status: true
                };

                const response = await axios.post(`${API_BASE}/alert-rules`, testRule);
                setResult('createRuleResult', true, 'Priority修复成功，可以使用medium优先级', response.data);
            } catch (error) {
                if (error.response?.data?.errors?.priority) {
                    setResult('createRuleResult', false, 
                        'Priority验证仍有问题: ' + JSON.stringify(error.response.data.errors.priority),
                        error.response.data
                    );
                } else {
                    setResult('createRuleResult', false, 
                        `创建规则失败: ${error.response?.status} - ${error.response?.data?.message || error.message}`,
                        error.response?.data
                    );
                }
            }
        }

        async function testAlertRuleOptions() {
            setLoading('optionsResult');
            try {
                const response = await axios.get(`${API_BASE}/alert-rules/options`);
                
                let priorities = response.data.data?.priorities || {};
                let notifications = response.data.data?.notification_methods || {};
                
                let message = '选项配置API正常';
                if (priorities.medium) {
                    message += ' ✅ Medium priority已修复';
                }
                if (!notifications.sms) {
                    message += ' ✅ SMS选项已移除';
                }
                
                setResult('optionsResult', true, message, {
                    priorities: priorities,
                    notification_methods: notifications,
                    has_medium_priority: !!priorities.medium,
                    no_sms_option: !notifications.sms
                });
            } catch (error) {
                setResult('optionsResult', false, 
                    `选项配置API失败: ${error.response?.status} - ${error.response?.data?.message || error.message}`,
                    error.response?.data
                );
            }
        }

        async function runAllTests() {
            setLoading('comprehensiveResult', '运行所有测试...');
            
            const results = {
                stats: false,
                list: false,
                create: false,
                options: false
            };

            // 测试统计API
            try {
                await axios.get(`${API_BASE}/alert-rules/stats`);
                results.stats = true;
            } catch (e) {
                console.error('Stats test failed:', e);
            }

            // 测试列表API
            try {
                const response = await axios.get(`${API_BASE}/alert-rules`, {
                    params: { status: 1, per_page: 10 }
                });
                results.list = response.data.success;
            } catch (e) {
                console.error('List test failed:', e);
            }

            // 测试创建规则
            try {
                const testRule = {
                    name: "综合测试规则_" + Date.now(),
                    rule_type: ["listing_status_change"],
                    conditions: {},
                    severity: "medium",
                    priority: "medium",
                    notification_method: ["system"],
                    status: true
                };
                await axios.post(`${API_BASE}/alert-rules`, testRule);
                results.create = true;
            } catch (e) {
                console.error('Create test failed:', e);
            }

            // 测试选项API
            try {
                const response = await axios.get(`${API_BASE}/alert-rules/options`);
                results.options = response.data.success;
            } catch (e) {
                console.error('Options test failed:', e);
            }

            const allPassed = Object.values(results).every(r => r);
            const passedCount = Object.values(results).filter(r => r).length;
            
            setResult('comprehensiveResult', allPassed, 
                `综合测试完成: ${passedCount}/4 项通过${allPassed ? ' 🎉' : ''}`,
                {
                    stats_api: results.stats ? '✅ 通过' : '❌ 失败',
                    list_api: results.list ? '✅ 通过' : '❌ 失败', 
                    create_rule: results.create ? '✅ 通过' : '❌ 失败',
                    options_api: results.options ? '✅ 通过' : '❌ 失败',
                    overall_status: allPassed ? '所有功能已修复' : '部分功能仍需修复'
                }
            );
        }

        // 页面加载完成后自动运行基础测试
        window.onload = function() {
            console.log('页面加载完成，准备测试...');
        };
    </script>
</body>
</html> 