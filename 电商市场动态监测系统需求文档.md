**电商市场动态监测系统 开发说明**

**一、系统概述**

**一）系统目标**

1、实时监控：定时监控指定平台的商品价格、促销偏好等波动情况。

2、自动预警：支持字段级预警规则（如价格、库存等）自定义。

3、竞争分析：建立多维度评估模型，为经营提供市场洞察、决策支持。

**二）系统特点**

* 高效采集：采用多线程技术，支持大批量数据采集任务。
* 灵活配置：预警规则和数据分析模型可自定义扩展。
* 可视化展示：提供直观的对比分析结果。

**二、系统功能结构**

**一）整体架构**

**1、数据采集层：**通过API定时请求获取商品数据。采用多线程技术提高数据采集效率，应对大批量采集任务。

**2、数据存储层：**使用MySQL数据库存储商品基本信息、价格历史数据、竞品数据等结构化数据。

**3、数据分析层：**根据MySQL数据库字典，对数据进行清洗、转换和多维度分析。

**4、预警与展示层：**根据设定的预警规则，通过微信、邮件或系统站内消息推送预警信息。同时，生成可视化界面展示分析结果。

**二）技术栈**

* 后端：php+MySQL，需支持多线程请求采集数据。
* 前端：html5+css3，支持多端自适应，有更好的可以推荐（唯一要求是：不能调用远程资源，需要本地化调用）。
* 预警通知方式：邮件通知、站内消息。

**三、功能模块说明：共三个模块**

**一）渠道价格监测模块**

**目标：从微观（SKU级）定时监测渠道商在不同平台的商品销售价行为。**

**3.1.1数据源接入：**

3.1.1.1 API实时采集：

* 详情接口：支持手动添加或批量导入接口url或商品id，请求获取和保存最新数据列表。

（详情接口:http://60.247.148.208:5001/tb/new/item\_detail\_base?token=testcc85S9zszzs&item\_id=************）

3.1.1.2 Excel批量导入：

* 支持人工上传本地Excel文件（字段符合模板规范，参见附录1）。

3.1.1.3 历史数据存储：记录每次采集的新数据，支持按时间范围查询。

**3.1.2数据标准化：**

3.1.2.1本模块数据表字段（字段可自定义拓展增减）：

| **字段名** | **数据类型** | **必填** | **说明** | **示例** |
| --- | --- | --- | --- | --- |
| **Code** | VARCHAR(10) | 是 | 状态码(以接口返回为准) | "200" |
| **Id** | VARCHAR(50) | 是 | 商品唯一ID（与**skuId**区分，一个商品可能含多个skuId） | "************" |
| **商品主图** | VARCHAR(255) | 否 | 商品主图URL（第一张展示图） |  |
| **Title** | TEXT | 是 | 商品标题 |  |
| **最低到手价** | DECIMAL(10,2) | 否 | 单品所有SKU中的最低到手价 | 7999.00 |
| **最高到手价** | DECIMAL(10,2) | 否 | 单品所有SKU中的最高到手价 | 8999.00 |
| **skuId** | VARCHAR(50) | 是 | SKU唯一标识符 | "5187112947623" |
| **skuName** | VARCHAR(100) | 是 | SKU名称（规格组合） | "轮胎类型:普通胎" |
| **Price** | DECIMAL(10,2) | 是 | SKU原价 | 8999.00 |
| **subPrice** | DECIMAL(10,2) | 是 | SKU券后价（实际到手价，无券后价时**subPrice=Price**） | 8499.00 |
| **subPriceTitle** | VARCHAR(100) | 否 | 券后价名称（以接口返回为准） | "折后" |
| **Quantity** | INT | 是 | SKU库存数量 | 100 |
| **Promotion** | TEXT | 否 | 优惠信息列表（以接口返回为准） | {"content": "官方立折","sub\_content": "官方8.5折","type": "discount"} |
| **Sale** | INT | 否 | 商品销量（近30天） | 1500 |
| **CommentCount** | INT | 否 | 商品评论数 | 3200 |
| **State** | TINYINT(1) | 是 | 商品状态：1-上架，0-下架 | 1 |
| **is\_Sku** | TINYINT(1) | 是 | 是否有SKU：1-是，0-否 | 1 |
| **itemType** | VARCHAR(20) | 是 | 商品平台类型（淘宝/天猫/京东等） | "天猫" |
| **category\_Id** | VARCHAR(50) | 是 | 类目ID | "50012131" |
| **category\_Path** | VARCHAR(200) | 是 | 类目全路径（以接口返回为准） | "汽车零部件->轮胎->乘用车轮胎" |
| **shopId** | VARCHAR(50) | 是 | 店铺ID | "548713772" |
| **shopName** | VARCHAR(100) | 是 | 店铺名称 | "官方旗舰店" |
| **Props** | TEXT | 否 | 商品参数信息(以接口返回为准) | **{"颜色":"银色","内存":"256GB"}** |
| **Delivery** | VARCHAR(100) | 否 | 发货地 | "上海" |
| **Time** | DATETIME | 是 | 数据采集更新时间（ISO格式） | "2023-10-20 14:30:00" |

![图片1](img/图片1.png)

▲系统界面大致效果参考

3.1.2.2数据处理：

* 去重：基于商品ID和SKU ID去重。
* 补全：缺失字段填充默认值或标记为异常。

**3.1.3数据分析结果：**

**3.1.3.1关键指标计算**

| **指标名称** | **计算逻辑** | **必填** |
| --- | --- | --- |
| 促销价偏离率  （sku级） | =(Price - subPrice) / Price × 100%，计算单品的每个Sku价格偏离率。   * 值为 负 → 渠道加价（SubPrice > Price）。 * 值为 正 → 渠道降价（SubPrice < Price）。 * 值为 0或1 → 渠道无促销行为，有以下几种情况：   ———SubPrice = Price，值为0  ———Price - subPrice=Price，值为1，即subPrice=0或不存在 | 否 |
| 渠道价格偏离率  （sku级） | =（官方指导价 - subPrice）/ 官方指导价 × 100%，计算单品的每个Sku的subPrice与官方指导价 偏离率。   * 值为 负 → 渠道加价（SubPrice > 官方指导价）。 * 值为 正 → 渠道降价（SubPrice < 官方指导价）。 * 值为 0或1 → 渠道无促销行为，有以下几种情况：   ———SubPrice = 官方指导价，值为0  ———官方指导价 - subPrice = 官方指导价，值为1，即subPrice=0或不存在时可统一令：subPrice = Price代入公式进行新计算 | 否 |
| 其它 | 可自定义选择预警的字段项 | 否 |

TIP：每行一个商品，可选择多行或全部商品生成对比结果。

**3.1.4预警规则设置：**

![图片2](img/图片2.png)

| **预警类型** | **阈值示例** | **目的** |
| --- | --- | --- |
| 促销价偏离率 | 偏离率：>10%（设置1个参数） | 防止促销过度导致亏损，关注自营商品。 |
| 渠道价格偏离率 | 官方指导价：100元；  偏离率：>10%（设置2个参数） | 监测低价乱价行为，关注渠道商商品。 |
| 数据更新频率 | 按小时或天，可自定义数据更新时间频率 | 可自定义频率 |
| 上下架状态 | —— | 上架/下架通知 |
| 数据更新异常 | 超24小时未更新 | 渠道数据未及时更新通知 |
| 通知方式 | 邮件、站内消息（支持多人订阅）。 |  |
| 其它 | 可自定义选择预警的字段项 |  |

**3.1.5任务分组：**给每批分析清单自定义分组，方便多任务管理。

**二）竞品动态监测模块**

**目标：定时监控竞品的定价结构、优惠体系策略。**

**3.2.1 数据源接入：**

3.2.1.1 API实时采集：

* 详情接口：支持手动添加或批量导入接口url或商品id，请求获取和保存最新数据列表。

（详情接口:http://60.247.148.208:5001/tb/new/item\_detail\_base?token=testcc85S9zszzs&item\_id=************）

3.2.1.2 Excel批量导入：

* 支持人工上传本地Excel文件（字段符合模板规范，参见附录1）。

3.2.1.3 历史数据存储：记录每次采集的新数据，支持按时间范围查询。

**3.2.2 竞品识别（筛选条件）：**关键词匹配（商品标题、类目）。

**3.2.3 数据标准化：**

基于第3.1.2项模块数据表字典（字段可自定义拓展增减），新增第3.2.4.1项核心指标字段。

**3.2.4数据分析结果：**

3.2.4.1核心指标

| **指标名称** | **计算逻辑** | **必填** |
| --- | --- | --- |
| 促销策略倾向 | 解析“Promotion”字段中的促销类型（content），统计各促销策略（sub\_content）的使用频率和占比，识别竞品偏好的促销策略。 | 否 |
| 单品价格综合折扣率  （sku级） | = (1 − (ΣsubPricei / ΣPricei))\*100%，计算单品整体促销强度（折扣力度）。   * ΣsubPricei：单品所有sku的subPrice价（到手价）总和 * ΣPricei：单品所有sku的Price价（原价）总和 | 否 |
| 总体促销强度指数  （sku级） | =(促销SKU数 / 总SKU数)×(1 − 平均subPrice / 平均rice)，横向计算监测中竞品所有sku的整体促销强度。数值越大，说明促销范围广、折扣深，整体促销越激进。 | 否 |
| 单品价格综合偏差率  （sku级） | =（我方指导价 - subPrice）/ 我方指导价 × 100%，计算单品的每个Sku的subPrice与我方指导价 偏差率。   * 值为正 → 竞品价低（我方指导价 > subPrice），可能分流我方客户，需评估是否跟进调整。 * 值为负 → 竞品价高（我方指导价 < subPrice），我方指导价更具竞争力，可强调性价比或扩大市场份额。 * 值为 0值或1 → 有以下几种情况：   ———我方指导价 = subPrice，值为0  ———我方指导价 - subPrice = 我方指导价，值为1，即subPrice=0时可统一令：subPrice = Price代入公式进行新计算 | 否 |
| 单品最低价偏差率  （sku级） | =(我方最低指导价 - 竞品最低价subPrice) / 我方最低指导价 × 100%，关注价格竞争力下限（避免因高价失去市场）。 | 否 |
| 单品最高价偏差率  （sku级） | =(我方最高指导价 - 竞品最高价SubPrice) / 我方指导价 × 100%，关注价格上限的偏离（试探高端市场潜力）。 | 否 |
| 竞品价格趋势斜率 | 正斜率代表涨价趋势（如a=5，代表每天涨5元）；负斜率代表降价趋势；a≈0价格基本稳定。趋势斜率 = 线性回归斜率a，a值如下：  ![图片3](img/图片3.png)  ![图片4](img/图片4.png) | 否 |

TIP：每行一个商品，可选择多行或全部商品生成对比结果。

*******可视化图表输出

* 价格趋势折线图（基于单品最低价、单品最高价）：X轴为时间（如月份），Y轴为价格，不同颜色的折线代表主产品和竞品A、B、C...的价格变化趋势，支持选择多个竞品进行对比。

![图片5](img/图片5.png)

* 促销类型占比饼图&排名表（基于“Promotion”字段生成）：

|  |
| --- |
| 左图：饼图展示促销类型（content参数）占比。  右表：根据左图content占比排名，生成右图促销策略（sub\_content参数）倾向排名表格。 |

——多竞品对比

| **排名** | **竞品名称** | **主要促销策略** | **具体示例** | **使用频率** |
| --- | --- | --- | --- | --- |
| 1 | 竞品A | 满减 | 满100元减20元 | 45% |
| 2 | 竞品B | 折扣 | 全场8折/限时5折 | 30% |

——单品对比

| **排名** | **促销策略** | **具体示例** | **使用频率** |
| --- | --- | --- | --- |
| 1 | **满减** | 满100元减20元 | 15% |
| 2 | **折扣** | 全场8折/限时5折 | 12% |

* 类目价格带分布热力图。

基于category\_Path 分类，X轴为价格区间（如0-50, 50-100...），Y轴为产品类目，颜色深浅表示商品数量密度。

**3.2.5预警规则设置：**

![图片6](img/图片6.png)

| **预警类型** | **阈值示例** | **目的** |
| --- | --- | --- |
| 价格偏差率 | 我方指导价：100元  单品价格综合偏差率：>10%  单品最低价偏差率：15%  单品最高价偏差率：11%  （3个偏差率可任选1个或多个组合监测预警） | 关注单品各类偏差率情况，调整价格结构 |
| 数据更新频率 | 按小时或天，可自定义数据更新时间频率 | 可自定义频率 |
| 上下架状态 | —— | 上架/下架通知 |
| 数据更新异常 | 超24小时未更新 | 渠道数据未及时更新通知 |
| 通知方式 | 邮件、站内消息（支持多人订阅）。 |  |
| 其它 | 可自定义选择预警的字段项 |  |

**3.2.6任务分组：**给每批分析清单自定义分组，方便多任务管理。

**三）相似同款商品查询模块**

**目标：查询相似同款商品最低价与最高价、商品素材侵权情况、商品是否存在非授权售卖行为。**

**3.3.1数据源接入：**

3.3.1.1 API实时采集

* 详情接口：支持手动添加或批量导入接口url或商品id，请求获取和保存最新数据列表。

（详情接口:http://60.247.148.208:5001/tb/new/item\_detail\_base?token=testcc85S9zszzs&item\_id=************）

* 同款商品接口：支持手动导入商品主图URL或商品id获取和保存最新数据列表。

（同款商品接口：http://60.247.148.208:5001/taobao/SimilarProduct?token=testcc85S9zszzs&itemId=669444749200）

* 关键词搜索接口：导入关键词，翻页请求获取和保存最新数据列表。

（搜索接口：http://60.247.148.208:5001/tb/new/item\_search?token=testcc85S9zszzs&keyword=nova13&page\_no=1）

* 店铺接口：导入店铺id，翻页请求获取和保存最新数据列表。

(店铺接口:http://60.247.148.208:5001/tb/new/shop\_item?token=testcc85S9zszzs&shop\_id=273907929&page=1)

3.3.1.2 历史数据存储：建立时间序列数据库，记录每次采集的快照

**3.3.2 相似同款识别（筛选条件）：**关键词匹配（商品标题、类目）。

**3.3.3数据处理与标准化：**

本模块数据表字典（字段可自定义拓展增减）

| **接口类型** | **字段名** | **数据类型** | **必填** | **说明** | **示例** |
| --- | --- | --- | --- | --- | --- |
| 1.关键词搜索接口  2.店铺商品列表接口 | **Code** | VARCHAR(10) | 是 | 状态码(以接口返回为准) | "200" |
| **Id** | VARCHAR(50) | 是 | 商品唯一ID | "P123456" |
| **商品主图** | VARCHAR(255) | 否 | 商品主图URL（第一张展示图） |  |
| **Title** | TEXT | 是 | 商品标题 |  |
| **Price** | DECIMAL(10,2) | 否 | 单品价格 | 7999.00 |
| **Quantity** | INT | 是 | 库存数 | 100 |
| **shopName** | VARCHAR(100) | 是 | 店铺名称 | "官方旗舰店" |
| **Time** | DATETIME | 是 | 数据采集更新时间（ISO格式） | "2023-10-20 14:30:00" |
| 同款商品接口 | **category\_Id** | VARCHAR(50) |  | 类目ID | "12365" |
| **category\_Path** | VARCHAR(200) |  | 类目全路径（以接口返回为准） | "汽车零部件/养护/美容/维保->轮胎->乘用车轮胎" |

**3.3.4数据分析结果：**

| **新字段** | **计算逻辑** |
| --- | --- |
| 相似度评分 | 相似度评分 = w1×标题相似度 + w2×类目匹配度 + w3×图像相似度 + w4×价格相似度 + |

**3.3.5任务分组：**给每批分析清单自定义分组，方便多任务管理。

**四、其它功能要求**

**4.1 安全要求**

* 数据加密：敏感字段（如价格）数据库加密存储。
* 权限控制：角色分级（管理员、运营人员、只读用户）。

**4.2 扩展性**

* 支持新增电商平台API接入（预留接口配置，以列表的形式进行管理）。
* 预警规则支持自定义公式。
* 系统配置：基本信息可自定义