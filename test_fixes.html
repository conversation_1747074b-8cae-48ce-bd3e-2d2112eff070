<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修复验证测试</title>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
</head>
<body>
    <h1>修复验证测试</h1>
    
    <h2>测试1: 创建预警规则（修复priority问题）</h2>
    <button onclick="testCreateAlertRule()">测试创建预警规则</button>
    <div id="createResult"></div>
    
    <h2>测试2: 获取预警规则列表</h2>
    <button onclick="testGetAlertRules()">测试获取预警规则</button>
    <div id="getRulesResult"></div>
    
    <h2>测试3: 获取选项配置</h2>
    <button onclick="testGetOptions()">测试获取选项</button>
    <div id="optionsResult"></div>

    <script>
        const API_BASE = 'http://localhost:8000/api';
        const AUTH_TOKEN = '28|wJ85np4MPWlytOB1VcqmWTWTKTi28buL5CNBEO96c7e26591';

        axios.defaults.headers.common['Authorization'] = `Bearer ${AUTH_TOKEN}`;
        axios.defaults.headers.common['Content-Type'] = 'application/json';

        async function testCreateAlertRule() {
            try {
                const response = await axios.post(`${API_BASE}/alert-rules`, {
                    name: "测试规则修复",
                    description: "测试priority修复",
                    rule_type: ["listing_status_change"],
                    conditions: {},
                    severity: "medium",
                    priority: "medium", // 修复后应该支持
                    notification_method: ["system"], // 移除了sms
                    status: true
                });
                
                document.getElementById('createResult').innerHTML = 
                    `<pre style="color: green;">${JSON.stringify(response.data, null, 2)}</pre>`;
            } catch (error) {
                document.getElementById('createResult').innerHTML = 
                    `<pre style="color: red;">${JSON.stringify(error.response?.data || error.message, null, 2)}</pre>`;
            }
        }

        async function testGetAlertRules() {
            try {
                const response = await axios.get(`${API_BASE}/alert-rules`);
                
                document.getElementById('getRulesResult').innerHTML = 
                    `<pre style="color: green;">${JSON.stringify(response.data, null, 2)}</pre>`;
            } catch (error) {
                document.getElementById('getRulesResult').innerHTML = 
                    `<pre style="color: red;">${JSON.stringify(error.response?.data || error.message, null, 2)}</pre>`;
            }
        }

        async function testGetOptions() {
            try {
                const response = await axios.get(`${API_BASE}/alert-rules/options`);
                
                document.getElementById('optionsResult').innerHTML = 
                    `<pre style="color: green;">${JSON.stringify(response.data, null, 2)}</pre>`;
            } catch (error) {
                document.getElementById('optionsResult').innerHTML = 
                    `<pre style="color: red;">${JSON.stringify(error.response?.data || error.message, null, 2)}</pre>`;
            }
        }
    </script>
</body>
</html> 